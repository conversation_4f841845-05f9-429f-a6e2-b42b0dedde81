<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>bigdata-bi</artifactId>
        <groupId>com.bestpay.bigdata</groupId>
        <version>1.67.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>bi-thirdparty</artifactId>

    <properties>
        <maven.compiler.source>14</maven.compiler.source>
        <maven.compiler.target>14</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.bestpay.mbp</groupId>
            <artifactId>mbp-output-gateway-api</artifactId>
            <version>1.30.1</version>
        </dependency>

        <dependency>
            <groupId>com.bestpay.bigdata</groupId>
            <artifactId>bi-common</artifactId>
             <version>1.67.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.sgroschupf</groupId>
            <artifactId>zkclient</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.bestpay.bigdata</groupId>
            <artifactId>bi-backend-api</artifactId>
            <version>1.67.0</version>
            <scope>compile</scope>
        </dependency>


    </dependencies>
</project>