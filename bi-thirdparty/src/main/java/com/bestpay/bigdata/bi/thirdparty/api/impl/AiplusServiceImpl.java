package com.bestpay.bigdata.bi.thirdparty.api.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpException;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.bean.AiPlusUserSearchRequest;
import com.bestpay.bigdata.bi.common.bean.UserInfoRequest;
import com.bestpay.bigdata.bi.common.common.AIPlusPageable;
import com.bestpay.bigdata.bi.common.common.Constant;
import com.bestpay.bigdata.bi.common.config.ApolloRefreshConfig;
import com.bestpay.bigdata.bi.common.config.AuthConfig;
import com.bestpay.bigdata.bi.common.constant.AuthorizationConstant;
import com.bestpay.bigdata.bi.common.dto.AIplusUserInfo;
import com.bestpay.bigdata.bi.common.dto.common.AiPlusUserGroup;
import com.bestpay.bigdata.bi.common.entity.Org;
import com.bestpay.bigdata.bi.common.entity.PermissionInfo;
import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.common.entity.UserInfo.UserType;
import com.bestpay.bigdata.bi.common.enums.CodeEnum;
import com.bestpay.bigdata.bi.common.enums.PermissionLevel;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import com.bestpay.bigdata.bi.common.exception.HttpRequestException;
import com.bestpay.bigdata.bi.common.exception.ZhiJiaException;
import com.bestpay.bigdata.bi.common.request.AIplusPermissionRequest;
import com.bestpay.bigdata.bi.common.response.UserMobileResponse;
import com.bestpay.bigdata.bi.common.util.HttpUtils;
import com.bestpay.bigdata.bi.common.util.JSONObjectUtil;
import com.bestpay.bigdata.bi.common.util.RsaUtil;
import com.bestpay.bigdata.bi.thirdparty.api.AiPlusUserService;
import com.bestpay.bigdata.bi.thirdparty.api.AiplusService;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021/3/30 14:49
 **/
@Slf4j
@Service
@Component
@ConditionalOnProperty(value = "bi.application.mode.aiplus", havingValue = "NORMAL", matchIfMissing = true)
public class AiplusServiceImpl implements AiplusService {

  @Resource
  private AuthConfig authConfig;

  @Resource
  private ApolloRefreshConfig refreshConfig;

  @Autowired
  private AiPlusUserService aiPlusUserService;


  /**
   * get current user permission in specify plat system type
   *
   * @param userId token
   * @return Boolean
   */
  @Override
  public Boolean getCurrentUserPermission(String userId) {
    try {
      // Step1: package request entity and request aiplus with uri
      AIplusPermissionRequest request = new AIplusPermissionRequest();
      log.info("package request entity : {}, and request aiplus with uri : {}", request,
          authConfig.getCheckPermissionUrl());
      //http://**************:19080/aiplus/api/current/user/platform/permission/check
      Map<String, String> headers = Collections.singletonMap(AuthorizationConstant.USERID, userId);
      return HttpUtils.get(authConfig.getCheckPermissionUrl() + "?resource=" + Constant.BI_SYSTEM_TYPE + ":"
              + Constant.REPORT_QUEYY_RESOURCE_EN_NAME + "&permissionLevel=" + PermissionLevel.MANAGE.getCode(), headers,
          null, s -> JSONObjectUtil.processBooleanResponse(s));
    } catch (HttpException e) {
      log.error("Failed to get all users from AIPlus ...", e);
      throw new BusinessException(CodeEnum.JUMP_FAILED.code(), e.getMessage());
    }
  }

  /**
   * get current user in aiplus plat all information,will to expand more info to AIplusUserInfo
   *
   * @param userId token
   * @return AIplusUserInfo json format
   */
  @Override
  public AIplusUserInfo getCurrentUserInfo(String userId) {
    // Step1: request aipus with uri and parse response
    UserInfo userInfo = getUserInfo(userId);
    AIplusUserInfo aIplusUserInfo = new AIplusUserInfo();
    aIplusUserInfo.setOrgCode(userInfo.getOrg().getCode());
    aIplusUserInfo.setProvinceCode(userInfo.getProvince().getCode());
    aIplusUserInfo.setCityCode(userInfo.getCity().getCode());

    log.info("package response to AIplusUserInfo and return to return, resp from aiplus : {}",
        aIplusUserInfo);
    return aIplusUserInfo;
  }

  /**
   * get current user in aiplus plat all information,will to expand more info to AIplusUserInfo
   *
   * @param cookieId token
   * @return AIplusUserInfo json format
   */
  @Override
  public UserInfo getUserInfo(String cookieId) {
    if (StringUtils.isNotBlank(refreshConfig.getMockAiPlusUserInfo())) {
      return JSONUtil.toBean(refreshConfig.getMockAiPlusUserInfo(), UserInfo.class);
    }

    // 调用智加currentUser获取当前用户
    Map<String, String> headers = Collections.singletonMap(AuthorizationConstant.USERID, cookieId);
    UserInfo userInfo = null;
    try {
      userInfo = HttpUtils.get(authConfig.getNewCurrentUserUrl(), headers, null, s -> {

        JSONObject object = JSONObjectUtil.processResponse(s);
        if (object == null) {
          throw new BusinessException("用户不存在, cookieValue=" + cookieId);
        }
        UserInfo info = object.toBean(UserInfo.class);
        info.setCookieId(cookieId);
        info.setUserType(UserType.AI_PLUS_AUTH);
        return info;

      });
      log.info(" getNewCurrentUserUrl userInfo : {}", JSONUtil.toJsonStr(userInfo));
    } catch (ZhiJiaException e) {
      log.error("getCurrentUser Exception" + e.getMessage(), e);
      throw new ZhiJiaException(CodeEnum.REQUEST_CURRENT_USER_ERROR.code(), e.getMessage());
    } catch (Exception e) {

      if (e instanceof BusinessException) {
        throw e;
      }

      log.error("getCurrentUser Exception" + e.getMessage(), e);
      throw new BusinessException(CodeEnum.REQUEST_CURRENT_USER_ERROR.code(), e.getMessage());
    }
    return userInfo;
  }

  @Override
  public UserInfo getUserInfoByEmailFromAiplus(String email) {
    UserInfo userInfo = null;
    try {
      userInfo = HttpUtils.get(authConfig.getUserInfoByEmailUrl() + "?email=" + email, null, null, s -> {
        JSONObject object = JSONObjectUtil.processResponse(s);
        if (object == null) {
          throw new BusinessException("用户不存在, email=" + email);
        }
        UserInfo info = object.toBean(UserInfo.class);
        info.setUserType(UserType.EMAIL_AUTH);
        return info;
      });

    } catch (ZhiJiaException e) {
      log.error("getUserInfoByEmail Exception", e);
      throw new ZhiJiaException(CodeEnum.REQUEST_CURRENT_USER_ERROR.code(), e.getMessage());
    } catch (Exception e) {

      if (e instanceof BusinessException) {
        throw e;
      }

      log.error("getUserInfoByEmail Exception", e);
      throw new BusinessException(CodeEnum.REQUEST_CURRENT_USER_ERROR.code(), e.getMessage());
    }

    return userInfo;
  }

  /**
   * get current user in aiplus plat all information,will to expand more info to AIplusUserInfo
   * 注意：此方法待废弃，此方法有可能从缓存中获取到用户信息，所以可能不会返回用户的额外信息，如userGroupList。 建议使用AiPlusUserService.getUserInfoByExactMatchEmail方法
   *
   * @param email email
   * @return AIplusUserInfo json format
   */
  @Override
  public UserInfo getUserInfoByEmail(String email) {
    AiPlusUserSearchRequest request = new AiPlusUserSearchRequest();
    request.setOwnerNames(Lists.newArrayList(email));
    List<UserInfo> userInfos = aiPlusUserService.getUserList(request);
    //如果精确匹配了邮箱 直接返回
    for (UserInfo user : userInfos) {
      if (user.getEmail().equals(email)) {
        return user;
      }
    }

    if (CollUtil.isNotEmpty(userInfos)) {
      return userInfos.get(0);
    }

    return getUserInfoByEmailFromAiplus(email);
  }

  @Override
  public List<PermissionInfo> getPermissionInfo(String cookieValue, String platForm) {
    List<PermissionInfo> permissionInfoList = null;
    try {
      Map<String, String> headers = Collections.singletonMap(AuthorizationConstant.USERID, cookieValue);
      permissionInfoList = HttpUtils.get(
          authConfig.getNewPermissionUrl() + "?" + AuthorizationConstant.PLATFORM + "=" + platForm, headers, null,
          s -> JSONObjectUtil.processResponseArray(s).toList(PermissionInfo.class));
    } catch (HttpRequestException he) {
      log.error(Throwables.getStackTraceAsString(he));
      throw new BusinessException(CodeEnum.REQUEST_CURRENT_USER_ERROR.code(), he.getMessage());
    } catch (Exception e) {
      log.info("get permission info exception:", e);
      throw new BusinessException(CodeEnum.REQUEST_CURRENT_USER_ERROR.code(), e.getMessage());
    }
    return permissionInfoList;
  }

  @Override
  public List<AiPlusUserGroup> getAiPlusUserGroupList() {

    if (StringUtils.isNotBlank(refreshConfig.getMockAiPlusUserGroup())) {
      return JSONUtil.toList(refreshConfig.getMockAiPlusUserGroup(), AiPlusUserGroup.class);
    }

    try {
      List<AiPlusUserGroup> userGroups = HttpUtils.get(authConfig.getAiPlusUserGroupList(), null, null,
          s -> JSONObjectUtil.processResponseArray(s).toList(AiPlusUserGroup.class));

      return userGroups;

    } catch (HttpRequestException he) {
      log.error(Throwables.getStackTraceAsString(he));
      throw new BusinessException(CodeEnum.REQUEST_CURRENT_USER_ERROR.code(), he.getMessage());
    } catch (Exception e) {
      log.info("get permission info exception:", e);
      throw new BusinessException(CodeEnum.REQUEST_CURRENT_USER_ERROR.code(), e.getMessage());
    }
  }

  /**
   * get user/user list info from ai plus
   *
   * @return json format str AIPlus user info
   */
  @Override
  public AIPlusPageable getAiPlusUserInfo(UserInfoRequest request) {
    try {
      //Step1: prepare param and request aiplus get user list
      Map<String, Object> data = new HashMap<>();
      Map<String, Object> dataMap = new HashMap<>();
      data.put("current", request.getPageNum());
      data.put("size", request.getPageSize());
      if (StringUtils.isNotBlank(request.getUserName())) {
        dataMap.put("username", request.getUserName());
      }
      data.put("search", dataMap);
      log.info("prepare data : {} and request aiplus get user list url :{}", JSONUtil.toJsonStr(data),
          authConfig.getNewUserListUrl());
      JSONObject resultDataJson = HttpUtils.post(authConfig.getNewUserListUrl(), null, data,
          result -> JSONObjectUtil.processResponse(result));
      AIPlusPageable<UserInfo> userInfoAiPlusPageable = JSONUtil.toBean(resultDataJson,
          new TypeReference<AIPlusPageable<UserInfo>>() {
          }, true);
      return userInfoAiPlusPageable;
    } catch (HttpException e) {
      log.error("Failed to get all users from AIPlus ...", e);
      throw new BusinessException(CodeEnum.REQUEST_CURRENT_USER_ERROR.code(), e.getMessage());
    } catch (ZhiJiaException e) {
      log.error("Failed to get all users from AIPlus ...", e);
      throw new ZhiJiaException(CodeEnum.REQUEST_CURRENT_USER_ERROR.code(), e.getMessage());
    }

  }

  @Override
  public List<Org> getOrgList() {
    List<Org> orgList;
    try {
      orgList = HttpUtils.get(authConfig.getNewOrgListUrl(), Maps.newHashMap(), null,
          s -> JSONObjectUtil.processResponseArray(s).toList(Org.class));
    } catch (Exception e) {
      log.error("getOrgList Exception:{}", e.getMessage(), e);
      throw new BusinessException(CodeEnum.REQUEST_ORG_LIST_ERROR.code(), e.getMessage());
    }

    return orgList;
  }

  @Override
  public UserMobileResponse getUserMobileByEmail(List<String> emailList) {
    List<String> senderErrorMessageList = new ArrayList<>();
    // 用户手机号
    int emailSize = emailList.size();
    List<String> userMobileList = new ArrayList<>(emailSize);
    for (String email : emailList) {
      UserInfo userInfo = getUserInfoByEmail(email);
      if (Objects.nonNull(userInfo)) {
        String mobile = userInfo.getMobile();
        if (StrUtil.isNotBlank(mobile)) {
          // 手机号解密
          try {

            userMobileList.add(RsaUtil.decryptMobile(mobile, refreshConfig.getDecryptMobilePrivateKey()));
          } catch (Exception e) {
            senderErrorMessageList.add("手机号解密失败，用户邮箱：" + email);
          }
        } else {
          // 说明有用户信息 但是这个用户没有手机号
          log.warn("根据用户邮箱未查询到手机号，用户邮箱：{}", email);
          senderErrorMessageList.add("根据用户邮箱未查询到手机号，用户邮箱：" + email);
        }
      } else {
        // 说明根据邮箱查询没有查询到用户信息
        log.warn("根据用户邮箱未查到用户信息，用户邮箱：{}", email);
        senderErrorMessageList.add("根据用户邮箱未查到用户信息，用户邮箱：" + email);
      }
    }
    if (userMobileList.size() != emailSize && CollUtil.isNotEmpty(userMobileList)) {
      // 说明 根据邮箱查询的手机号  和 邮箱的个数不一致  所以此处给出日志告警即可
      log.warn("根据邮箱查询的手机号的大小和邮箱的个数不一致,订阅的邮箱大小：{}，用户的根据邮箱查询到的手机号的大小：{}",
          emailSize, userMobileList.size());

    }
    return UserMobileResponse.builder().userMobileList(userMobileList).senderErrorMessageList(senderErrorMessageList)
        .build();
  }
}
