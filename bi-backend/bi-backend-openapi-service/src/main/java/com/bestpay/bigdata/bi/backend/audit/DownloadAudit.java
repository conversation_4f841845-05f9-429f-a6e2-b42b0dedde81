package com.bestpay.bigdata.bi.backend.audit;

import cn.hutool.core.bean.BeanUtil;
import com.bestpay.bigdata.bi.backend.audit.dto.BaseAuditDto;
import com.bestpay.bigdata.bi.backend.audit.dto.DownloadAuditDto;
import com.bestpay.bigdata.bi.backend.persist.PersistHelper;
import com.bestpay.bigdata.bi.common.enums.Status;
import com.bestpay.bigdata.bi.common.util.LogTraceIdGenerator;
import com.bestpay.bigdata.bi.database.api.common.DownloadStatService;
import com.bestpay.bigdata.bi.database.bean.DownloadStat;
import java.util.Date;
import java.util.Objects;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 文件下载
 * <AUTHOR>
 * @date 2022/3/1 10:24
 **/
@Component
public class DownloadAudit implements Audit {

    private static final Logger logger = LoggerFactory.getLogger(DownloadAudit.class);

    @Autowired
    private PersistHelper persister;

    @Resource
    private DownloadStatService downloadStatService;


    public String persistFile(DownloadAuditDto downloadAuditVo) throws Exception {
        return persister.persistToRemote(downloadAuditVo);
    }

    public void saveAudit(DownloadAuditDto downloadAuditDto) {
        DownloadStat downloadStat = new DownloadStat();
        BeanUtil.copyProperties(downloadAuditDto, downloadStat);
        downloadStat.setCreatedBy(downloadAuditDto.getUsername());
        downloadStat.setCreatedAt(new Date());
        downloadStat.setUpdatedBy(downloadAuditDto.getUsername());
        downloadStat.setUpdatedAt(new Date());

        if (Objects.nonNull(downloadAuditDto.getFileType())) {
            downloadStat.setFileType(downloadAuditDto.getFileType().name());
        }
        downloadStat.setTraceId(MDC.get(LogTraceIdGenerator.TRACE_ID));
        downloadStat.setDownloadContent(downloadAuditDto.getDownloadContent());
        downloadStat.setEngineQueryId(downloadAuditDto.getEngineQueryId());
        downloadStat.setTraceId(MDC.get(LogTraceIdGenerator.TRACE_ID));
        downloadStatService.insert(downloadStat);
    }

    @Override
    public void audit(BaseAuditDto auditDto) {
        if (!supports(auditDto.getAuditType())) {
            logger.debug("this DownloadAudit can't support the user:{} operation:{}  auditType:{}",
                    auditDto.getUsername(), auditDto.getTypeCode(), auditDto.getAuditType());
            return;
        }

        DownloadAuditDto downloadAuditVo = (DownloadAuditDto) auditDto;
        try {
            String filePath = persistFile(downloadAuditVo);
            downloadAuditVo.setFilePath(filePath);
        } catch (Exception e) {
            downloadAuditVo.setStatus(Status.QUERY_FAILED.getCode());
            downloadAuditVo.setMessage(e.getMessage());
            logger.error("user:{} persistFile failed. cause:", auditDto.getUsername(), e);
        } finally {
            saveAudit(downloadAuditVo);
        }

    }

    @Override
    public boolean supports(AuditType auditType) {
        return AuditType.DOWNLOAD.equals(auditType);
    }


}
