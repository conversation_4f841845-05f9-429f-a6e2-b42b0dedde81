package com.bestpay.bigdata.bi.common.config;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.PostConstruct;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/4/6 9:56
 **/
@ConfigurationProperties(prefix = "refresh")
@Component("apolloRefreshConfig")
@RefreshScope
@Slf4j
@Data
public class ApolloRefreshConfig {

    private Boolean swaggerSwitch;

    /**
     * 钉钉告警群配置信息
     */
    private String dingDingWarnGroup;
    /**
     * 值班排期
     */
    private String dutyArrangement;

    private String northStarDashboardSpecialConf ;

    private Integer queryScriptCharLength = 30000;

    private Integer scriptLimit = 1000;

    private String probeDatasourceList;

    private String reportDatasourceList;

    private String requestSystemList;

    /**
     * 盐值
     */
    private String smsSendSalt;
    /**
     * 短信发送的基本配置
     */
    private String smsBaseConf;

    /**
     * 手机号解密 秘钥
     */
    private String decryptMobilePrivateKey;

    /**
     * 查询结果缓存时间
     */
    private int resultMinutes;
    /**
     * 批量sql执行结果缓存时间
     */
    private int batchResultMinutes;
    /**
     * 超时时间
     */
    private int queryTimeoutSecond;
    /**
     * 超时时间
     */
    private int batchTimeoutSecond;
    /**
     * 查询结果大小限制
     */
    private long queryResultSize;
    /**
     * 多维分析模块查询结果大小限制
     */
    private long analysisQueryResultSize;
    /**
     * 报表查询模块查询结果大小限制
     */
    private long reportQueryResultSize;
    /**
     * 报即席查询模块查询结果大小限制
     */
    private long probeQueryResultSize;
    /**
     * ftp连接信息
     */
    private String sftp;
    /**
     * presto、ck超时时间
     */
    private int statementQueryTimeoutSeconds;

    /**
     * presto fengkong超时时间
     */
    private int statementOpenApiQueryTimeoutSeconds;
    /**
     * hive超时时间
     */
    private int statementHiveQueryTimeoutSeconds;


    /**
     * 这是临时方案，例 "11":false,表示QUERY_PROBE操作operateType为下载 还是走原有的数据梳理逻辑，true走新逻辑
     */
    private String queryAndDownloadAuth;

    /**
     * 查询结果 block的大小
     */
    private long resultBlockSize;


    private String sqlSwitchModel;

    private String sqlWhiteList;

    private String appWhiteList;

    private Long tableauDownloadSleepTime;

    private String biMysqlDatasource;

    private String indexMysqlDatasource;

    private String fileSystem;

    private String originalMinioEndpoint;

    private String originalMinioAccessKey;

    private String originalMinioSecretKey;

    private String originalMinioBucketName;

    /**
     * minio上传超时时间
     */
    private int minioUploadTimeoutMills = 600000;

    /**
     * minio下载超时时间
     */
    private int minioDownloadTimeoutMills = 600000;

    private String openapiRequestWhiteUser;

    private String openapiRequestWhiteClickhouse;

    private String noMetaAuthEngineList;

    private int ckMaxBatch=50000;


    private String supportSetParamList;

    private String waitSyncTableList;

    private String urlAuthorityConfig;
    private String probeUrlAuthorityConfig;

    private String numberPattern;
    private String datePattern;
    private String stringPattern;

    private String encryptControllerMethodList;

    private String mockAiPlusUserGroup;
    private String mockAiPlusUserInfo;

    private String stopSyncTable;

    private Integer resultRowsNumLimit;

    private Integer imeiRowCountLimit =  200001;

    // 秒
    private Integer subscribeTimeout = 15*60;

    private String fileRequestSuffixUrl;

    private String fileDownloadUrl;

    private String scientificNumberPattern = "[+-]?[0-9]*(\\.[0-9]+)?[eE][+-]?[0-9]+";


    private String dorisHiveAuthDbList;

    private String queryStatisticMetricConfig;


    /**
     * 钉钉个人推送账号
     */
    private String dingDingPersonalPushAccount;

    /**
     * 钉钉个人推送url
     */
    private String dingDingPersonalPushUrl;

    /**
     * 钉钉个人推送获取token url
     */
    private String dingDingAccessTokenUrl;


    /**
     * 需要告警的订阅Id列表
     */
    private String warnGroupReportSubCodes;


    /**
     * 应用嵌入移动端场景下，用于指定默认排序优先展示的组织编码（orgCode）。
     * 若配置该值，在查询仪表板列表时，会将对应组织的嵌入对象排在最前面展示。
     * 示例值："20010"
     */
    private String appEmbedMobileOrderFirstOrgCode;


    @PostConstruct
    private void initialize() throws IllegalAccessException {
        log.info("ApolloRefreshConfig initialized, here are all the attribute values obtained from apollo.");
        Class<? extends ApolloRefreshConfig> configInstance = this.getClass();
        Field[] declaredFields = configInstance.getDeclaredFields();
        for (Field declaredField : declaredFields) {
            declaredField.setAccessible(true);
            Object value = declaredField.get(this);
            if (value instanceof Logger) continue;
            String keyValue = value == null ? "" : value.toString();
            log.info("ApolloRefreshConfig got refresh config properties:{}={}", declaredField.getName(), keyValue);
        }
    }

    public Map<String, Boolean> getConvertQueryAndDownloadAuth() {
        ObjectMapper mapper = new ObjectMapper();
        Map<String, String> map = null;
        HashMap<String, Boolean> result = new HashMap<>();
        try {
            map = mapper.readValue(queryAndDownloadAuth, Map.class);
            log.info("queryAndDownloadAuth json parse size :{}", map.size());
            if (map.size() > 0) {
                for (Map.Entry<String, String> entry : map.entrySet()) {
                    result.put(entry.getKey(), Boolean.parseBoolean(entry.getValue()));
                }
            }
        } catch (JsonProcessingException e) {
            log.error("apollo getConvertQueryAndDownloadAuth fail",e);
        }

        return result;
    }


    public Map<String, List<String>> getConvertProbeDatasourceListMap() {
        ObjectMapper mapper = new ObjectMapper();
        Map<String, String> map = null;
        HashMap<String, List<String>> result = new HashMap<>();
        try {
            map = mapper.readValue(probeDatasourceList, Map.class);
            log.info("probeDatasourceList json parse size :{}", map.size());
            if (map.size() > 0) {
                for (Map.Entry<String, String> entry : map.entrySet()) {
                    result.put(entry.getKey(), Arrays.asList(entry.getValue().split(",")));
                }
            }
        } catch (JsonProcessingException e) {
            log.error("apollo getConvertProbeDatasourceListMap fail",e);
        }

        return result;
    }


    public Map<String, List<String>> getConvertReportDatasourceListMap() {
        ObjectMapper mapper = new ObjectMapper();
        Map<String, String> map = null;
        HashMap<String, List<String>> result = new HashMap<>();
        try {
            map = mapper.readValue(reportDatasourceList, Map.class);
            log.info("probeDatasourceList json parse size :{}", map.size());
            if (map.size() > 0) {
                for (Map.Entry<String, String> entry : map.entrySet()) {
                    result.put(entry.getKey(), Arrays.asList(entry.getValue().split(",")));
                }
            }
        } catch (JsonProcessingException e) {
            log.error("apollo getConvertReportDatasourceListMap fail",e);
        }

        return result;
    }

    public Map<String, String> getConvertSetParamMap() {
        ObjectMapper mapper = new ObjectMapper();
        Map<String, String> map = null;
        HashMap<String, String> result = new HashMap<>();
        try {
            map = mapper.readValue(supportSetParamList, Map.class);
            log.info("supportSetParamList json parse size :{}", map.size());
            if (map.size() > 0) {
                for (Map.Entry<String, String> entry : map.entrySet()) {
                    result.put(entry.getKey().toLowerCase(), entry.getValue());
                }
            }
        } catch (JsonProcessingException e) {
            log.error("apollo getConvertSetParamMap fail",e);
        }

        log.info("getConvertSetParamMap result map : {}", result.keySet());
        return result;
    }

    public Map<String, String> getQueryStatisticMetricConfig() {
        ObjectMapper mapper = new ObjectMapper();
        Map<String, String> map = null;
        HashMap<String, String> result = new HashMap<>();
        try {
            map = mapper.readValue(queryStatisticMetricConfig, Map.class);
            if (!map.isEmpty()) {
                for (Map.Entry<String, String> entry : map.entrySet()) {
                    result.put(entry.getKey().toLowerCase(), entry.getValue());
                }
            }
        } catch (JsonProcessingException e) {
            log.error("apollo getQueryStatisticMetricConfig fail",e);
        }

        log.info("getQueryStatisticMetricConfig result map : {}", result.keySet());
        return result;
    }


    public List<String> getOpenapiUserWhiteList() {
        List<String> list = Lists.newArrayList();
        if (StringUtils.isNotEmpty(openapiRequestWhiteUser)) {
            list.addAll(Arrays.asList(openapiRequestWhiteUser.split(",")));
        }
        return list;
    }

    public List<String> getOpenapiRequestSystemWhiteList() {
        List<String> list = Lists.newArrayList();
        if (StringUtils.isNotEmpty(openapiRequestWhiteClickhouse)) {
            list.addAll(Arrays.asList(openapiRequestWhiteClickhouse.split(",")));
        }
        return list;
    }

    public Set<String> getNoMetaAuthCheckEngineList(){

        Set<String> set = Sets.newHashSet();
        if (StringUtils.isNotEmpty(noMetaAuthEngineList)) {
            set.addAll(Arrays.asList(noMetaAuthEngineList.split(",")));
        }

        return set;
    }

    public Set<String> getRequestSystemSet(){

        Set<String> set = Sets.newHashSet();
        if (StringUtils.isNotEmpty(requestSystemList)) {
            set.addAll(Arrays.asList(requestSystemList.split(",")));
        }

        return set;
    }


    public Set<String> getDorisHiveAuthDbSet() {
        Set<String> set = Sets.newHashSet();
        if (StringUtils.isNotEmpty(dorisHiveAuthDbList)) {
            set.addAll(Arrays.asList(dorisHiveAuthDbList.split(",")));
        }
        return set;
    }


    public List<String> getEncryptControllerMethodList(){
        return JSONUtil.toList(encryptControllerMethodList, String.class);
    }

    public Map<String, String> getWaitSyncTableSet() {
        ObjectMapper mapper = new ObjectMapper();
        Map<String, String> map = null;
        HashMap<String, String> result = new HashMap<>();
        try {
            map = mapper.readValue(waitSyncTableList, Map.class);
            log.info("waitSyncTableList json parse size :{}", map.size());
            if (map.size() > 0) {
                for (Map.Entry<String, String> entry : map.entrySet()) {
                    result.put(entry.getKey().toLowerCase(), entry.getValue());
                }
            }
        } catch (JsonProcessingException e) {
            log.error("apollo waitSyncTableList fail",e);
        }

        log.info("waitSyncTableList result map : {}", result.keySet());
        return result;
    }
}
