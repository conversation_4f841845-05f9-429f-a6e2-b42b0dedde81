package com.bestpay.bigdata.bi.common.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/4/6 9:56
 **/
@ConfigurationProperties(prefix = "authorization")
@Component("authConfig")
@RefreshScope
@Slf4j
@Data
public class AuthConfig {
    /**
     * 短信发送URL
     */
    private String smsSendUrl;
    /**
     * 智加-组织列表
     */
    private String newOrgListUrl;
    private String newUserListUrl;
    private String aiPlusUserGroupList;
    /**
     * 智加-当前登录用户信息
     */
    private String newCurrentUserUrl;
    /**
     * 智加-当前登录用户权限
     */
    private String newPermissionUrl;
    /**
     * 智加-根据邮箱获取用户信息
     */
    private String userInfoByEmailUrl;
    private String checkPermissionUrl;
    private String downloadAppInsertUrl;
    private String metadataUrl;
    private String metadataSearchTableUrl;
    private String metadataQueryFieldUrl;
    private String metadataQueryDatabaseUrl;
    private String metadataQueryTableUrl;
    private String metadataHiveUdfUrl;
    private String metadataHiveResourceCheckUrl;
    private String metadataSecurityLevelUrl;
    private String metadataAuthorityDatabaseUrl;
    private String metadataCkCreateTableUrl;
    private String metadataCkDropTableUrl;
    private String desktopUrl;
    private String datasbpUserReportsUrl;
    private String datasbpReportUrl;
    private String innerDatasbpReportUrl;
    private String openapiQueryStatusUrl;
    private String openapiFileUploadUrl;
    private String openapiTextQueryUrl;
    private String openapiBatchQueryUrl;
    private String openapiQueryResultUrl;
    private String openapiCancelQueryUrl;

    //for dashboard AI analysis reporting
    private String dashboardAIAnalysisUrl;
    private String dashboardAIAnalysisAPIKey;
    private String dashboardAIAnalysisAppID;

    //for AI find report
    private String findReportAIUrl;
    private String findReportAIAPIKey;
    private String findReportAIAppID;
}
