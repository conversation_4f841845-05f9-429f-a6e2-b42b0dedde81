<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>bigdata-bi</artifactId>
        <groupId>com.bestpay.bigdata</groupId>
         <version>1.67.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

  <artifactId>bi-common</artifactId>

  <properties>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
    <slf4j.version>1.7.30</slf4j.version>
    <bestpay-logback.version>1.3.1</bestpay-logback.version>
    <logback.version>1.2.3</logback.version>
  </properties>


  <dependencies>
<!--    <dependency>-->
<!--      <groupId>com.github.xiaoymin</groupId>-->
<!--      <artifactId>knife4j-spring-boot-starter</artifactId>-->
<!--                      <version>3.0.3</version>-->
<!--    </dependency>-->
    <dependency>
      <groupId>com.bestpay.bigdata.product.profile</groupId>
      <artifactId>product.profile</artifactId>
      <version>1.0.4-SNAPSHOT</version>
      <scope>system</scope>
      <systemPath>${project.basedir}/lib/product.profile-1.0.4-SNAPSHOT.jar</systemPath>
    </dependency>

    <dependency>
      <groupId>com.ctrip.framework.apollo</groupId>
      <artifactId>apollo-client</artifactId>
    </dependency>

    <dependency>
      <groupId>cn.hutool</groupId>
      <artifactId>hutool-all</artifactId>
    </dependency>

    <dependency>
      <groupId>com.bestpay.seccore</groupId>
      <artifactId>sec-hsm-core-api</artifactId>
      <version>1.23.0</version>
    </dependency>

    <dependency>
      <groupId>io.swagger.core.v3</groupId>
      <artifactId>swagger-annotations</artifactId>
      <version>2.1.2</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-redis</artifactId>
      <exclusions>
        <exclusion>
          <groupId>io.lettuce</groupId>
          <artifactId>lettuce-core</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-aop</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>spring-boot-starter-logging</artifactId>
          <groupId>org.springframework.boot</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>redis.clients</groupId>
      <artifactId>jedis</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>${commons-lang3.version}</version>
    </dependency>
    <dependency>
      <groupId>com.facebook.presto</groupId>
      <artifactId>presto-jdbc</artifactId>
      <version>0.268</version>
    </dependency>

    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-collections4</artifactId>
      <version>${commons-collections4.version}</version>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>

    <dependency>
      <groupId>com.alibaba.boot</groupId>
      <artifactId>dubbo-spring-boot-starter</artifactId>
    </dependency>


    <!--        日志相关依赖  开始-->
    <!--为了兼容traceLogid和避免引入多个版本的logback相关依赖,请在bestpay-logback中排除相关依赖再引入-->
    <dependency>
      <groupId>com.bestpay</groupId>
      <artifactId>bestpay-logback</artifactId>
      <version>${bestpay-logback.version}</version>
      <exclusions>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>ch.qos.logback</groupId>
          <artifactId>logback-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>ch.qos.logback</groupId>
          <artifactId>logback-classic</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>log4j-over-slf4j</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>jcl-over-slf4j</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>jul-to-slf4j</artifactId>
        </exclusion>
        <exclusion>
          <groupId>commons-lang</groupId>
          <artifactId>commons-lang</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>jackson-core-asl</artifactId>
          <groupId>org.codehaus.jackson</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jackson-mapper-asl</artifactId>
          <groupId>org.codehaus.jackson</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>jcl-over-slf4j</artifactId>
      <version>${slf4j.version}</version>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>${slf4j.version}</version>
    </dependency>
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-core</artifactId>
      <version>${logback.version}</version>
      <exclusions>
        <exclusion>
          <groupId>com.bestpay.framework.apollo</groupId>
          <artifactId>apollo-client</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
      <version>${logback.version}</version>
    </dependency>
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-access</artifactId>
      <version>${logback.version}</version>
    </dependency>

    <!--        日志相关依赖  结束-->

    <!--<dependency>
        <groupId>io.springfox</groupId>
        <artifactId>springfox-boot-starter</artifactId>
        <version>${swagger.version}</version>
    </dependency>-->

    <dependency>
      <groupId>io.springfox</groupId>
      <artifactId>springfox-swagger2</artifactId>
      <version>2.9.2</version>
    </dependency>
    <dependency>
      <groupId>io.springfox</groupId>
      <artifactId>springfox-swagger-ui</artifactId>
      <version>2.9.2</version>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.4</version>
      <scope>compile</scope>
    </dependency>

    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-context</artifactId>
      <version>2.2.2.RELEASE</version>
      <scope>compile</scope>
    </dependency>

    <!---SNAPSHOT  版本是技术部的有问题，SNAPSHOT-->
    <dependency>
      <groupId>com.bestpay.drip</groupId>
      <artifactId>oss-proxy-client</artifactId>
      <version>1.0.2</version>
    </dependency>
    <dependency>
      <groupId>com.squareup.okhttp3</groupId>
      <artifactId>okhttp</artifactId>
      <version>4.8.1</version>
      <exclusions>
        <exclusion>
          <artifactId>kotlin-stdlib</artifactId>
          <groupId>org.jetbrains.kotlin</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <artifactId>kotlin-stdlib</artifactId>
      <groupId>org.jetbrains.kotlin</groupId>
      <version>1.3.70</version>
    </dependency>

    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>fastjson</artifactId>
      <version>1.2.83</version>
    </dependency>

    <dependency>
      <groupId>com.opencsv</groupId>
      <artifactId>opencsv</artifactId>
      <version>5.5.2</version>
    </dependency>

    <dependency>
      <groupId>org.csource</groupId>
      <artifactId>fastdfs</artifactId>
      <version>1.27.0.1</version>
    </dependency>

    <!-- Apache POI - Core -->
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi</artifactId>
      <version>4.1.2</version>
    </dependency>

    <!-- Apache POI - OOXML poi-ooxml 则提供了对 Office Open XML (OOXML) 格式的支持，包括 xlsx 文件格式。 -->
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi-ooxml</artifactId>
      <version>4.1.2</version>
    </dependency>

    <!-- Apache Commons Compress -->
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-compress</artifactId>
      <version>1.22</version>
    </dependency>

    <!--add minio-->
    <dependency>
      <groupId>io.minio</groupId>
      <artifactId>minio</artifactId>
      <version>8.4.5</version>
      <exclusions>
        <exclusion>
          <artifactId>bcprov-jdk15on</artifactId>
          <groupId>org.bouncycastle</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- sensitive word check -->
    <dependency>
      <groupId>com.bestpay.cif</groupId>
      <artifactId>bas-service-api</artifactId>
      <version>1.101.1</version>
    </dependency>

  </dependencies>

</project>