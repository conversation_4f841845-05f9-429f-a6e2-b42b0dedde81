<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.2.6.RELEASE</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.bestpay.bigdata</groupId>
    <artifactId>bigdata-bi</artifactId>
    <packaging>pom</packaging>
     <version>1.67.0</version>

    <modules>
        <module>bi-common</module>
        <module>bi-thirdparty</module>
        <module>bi-database</module>
        <module>bi-parsesql</module>
        <module>bi-backend</module>
        <module>bi-probe</module>
        <module>bi-analysis</module>
        <module>bi-report</module>
    </modules>

    <properties>
        <hutool.version>5.5.9</hutool.version>
        <mysql-connector.version>5.1.26</mysql-connector.version>
        <druid.version>1.1.6</druid.version>
        <spring-cloud-starter-gateway.version>2.2.2.RELEASE</spring-cloud-starter-gateway.version>
        <apache-dubbo.version>2.7.8</apache-dubbo.version>
        <apollo.version>1.3.0</apollo.version>
        <elastic-job.version>2.1.5.1</elastic-job.version>
        <swagger.version>3.0.0</swagger.version>
        <maven-compiler-plugin.version>2.3.2</maven-compiler-plugin.version>
        <hbase.version>1.2.0-cdh5.9.2</hbase.version>
        <gson.version>2.8.6</gson.version>
        <curator.version>2.10.0</curator.version>
        <alibaba-dubbo.version>0.2.0</alibaba-dubbo.version>
        <zkclient.version>0.1</zkclient.version>
        <jedis.version>3.1.0</jedis.version>
        <mybatis-starter.version>2.1.4</mybatis-starter.version>
        <mybatis-plus.version>3.3.0</mybatis-plus.version>
        <commons-collections4.version>4.4</commons-collections4.version>
        <powermock.version>1.7.0</powermock.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-gateway</artifactId>
                <version>${spring-cloud-starter-gateway.version}</version>
            </dependency>

            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector.version}</version>
            </dependency>

<!--            <dependency>-->
<!--                <groupId>com.alibaba</groupId>-->
<!--                <artifactId>druid</artifactId>-->
<!--                <version>${druid.version}</version>-->
<!--            </dependency>-->

            <dependency>
                <groupId>com.ctrip.framework.apollo</groupId>
                <artifactId>apollo-client</artifactId>
                <version>${apollo.version}</version>
            </dependency>

            <!-- elastic-job 依赖 -->
            <dependency>
                <groupId>com.bestpay</groupId>
                <artifactId>elastic-job-lite-core</artifactId>
                <version>${elastic-job.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bestpay</groupId>
                <artifactId>elastic-job-lite-spring</artifactId>
                <version>${elastic-job.version}</version>
            </dependency>

            <!-- 接口文档 swagger-ui 的依赖 -->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-boot-starter</artifactId>
                <version>${swagger.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${alibaba-dubbo.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.sgroschupf</groupId>
                <artifactId>zkclient</artifactId>
                <version>${zkclient.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.hbase</groupId>
                <artifactId>hbase-client</artifactId>
                <version>${hbase.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-recipes</artifactId>
                <version>${curator.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-framework</artifactId>
                <version>${curator.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-dependencies-zookeeper</artifactId>
                <version>${apache-dubbo.version}</version>
                <type>pom</type>
            </dependency>

            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>${jedis.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>2.1.4</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>org.mybatis.spring.boot</groupId>-->
<!--                <artifactId>mybatis-spring-boot-starter</artifactId>-->
<!--                <version>${mybatis-starter.version}</version>-->
<!--            </dependency>-->

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>


            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>4.8.1</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-parent</artifactId>
                <version>2.2.6.RELEASE</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.squareup.okhttp3</groupId>
                        <artifactId>okhttp</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.codehaus.janino</groupId>
            <artifactId>janino</artifactId>
            <version>3.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <version>2.2.6.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <version>2.2.6.RELEASE</version>
            <scope>test</scope>
        </dependency>

    </dependencies>


    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>2.3</version>
                <configuration>
                    <generateBackupPoms>false</generateBackupPoms>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-report-plugin</artifactId>
                <version>2.12.2</version>
                <configuration>
                    <showSuccess>false</showSuccess>
                </configuration>
            </plugin>

        </plugins>
    </build>

</project>