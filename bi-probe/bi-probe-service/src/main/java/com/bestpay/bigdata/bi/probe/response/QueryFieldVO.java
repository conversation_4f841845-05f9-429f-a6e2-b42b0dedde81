package com.bestpay.bigdata.bi.probe.response;

import com.bestpay.bigdata.bi.common.response.metaData.MetaDataField;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @create 2022-03-22-15:21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("数据探查-公共数据字段查询返回实体类")
public class QueryFieldVO implements Serializable {

    //current page num
    private Integer current;

    //page size
    private Integer size;

    //response record count
    private Integer total;

    List<MetaDataField> records;
}
