package com.bestpay.bigdata.bi.probe.monitor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import javax.management.MBeanServer;
import javax.management.ObjectName;
import java.lang.management.ManagementFactory;

import static com.bestpay.bigdata.bi.probe.monitor.CommonMonitor.commonMonitor;

/**
 * <AUTHOR>
 * @date 2025/1/6 16:14
 * @Description :
 **/
@Slf4j
@Component
public class MonitorManager  implements ApplicationListener<ApplicationReadyEvent> {


    @Override
    public void onApplicationEvent(ApplicationReadyEvent applicationReadyEvent) {
        initCommonMonitorMBean();
    }

    private void initCommonMonitorMBean() {
        MBeanServer server = ManagementFactory.getPlatformMBeanServer();
        ObjectName commonMonitorCounter = null;
        try {
            commonMonitorCounter = new ObjectName("com.bestpay.bigdata.bi.probe:name=CommonMonitorInfo");
            //create mbean and register mbean
            server.registerMBean(commonMonitor, commonMonitorCounter);
        } catch (Exception e) {
            log.error("create mbean and register CommonMonitor mbean error", e);
        }
    }
}
