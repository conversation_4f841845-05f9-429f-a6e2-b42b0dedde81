package com.bestpay.bigdata.bi.probe.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

@Data
@ApiModel(value = "数据探查请求类")
public class TextSQLRequest implements Serializable {
    protected static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "查询SQl语句",required = true)
    private String sql;
    @ApiModelProperty(value = "查询人",required = true)
    private String requestUser;
    @ApiModelProperty(value = "查询人所属组织",required = true)
    private String requestOrg;
    @ApiModelProperty(value = "查询引擎",required = true,allowableValues ="presto,hive,kylin,spark_sql,clickhouse")
    private String sqlEngine;
    @ApiModelProperty(value = "数据源",required = true,allowableValues ="hive_11,hive_36")
    private String databaseSource;

    private Integer limit;

    /** 数据源名称 */
    private String datasourceName;

    /** 数据源类型*/
    private String datasourceType;
    @ApiModelProperty(value = "请求来源",required = false)
    private String source;
}
