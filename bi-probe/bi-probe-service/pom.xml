<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>bi-probe</artifactId>
        <groupId>com.bestpay.bigdata</groupId>
         <version>1.67.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>bi-probe-service</artifactId>
    <packaging>jar</packaging>
    <properties>
        <hadoop.version>2.6.0-cdh5.9.2</hadoop.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.sgroschupf</groupId>
            <artifactId>zkclient</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bestpay.bigdata</groupId>
            <artifactId>bi-common</artifactId>
            <version>1.67.0</version>
        </dependency>
        <dependency>
            <groupId>com.bestpay.bigdata</groupId>
            <artifactId>bi-probe-auth</artifactId>
            <version>1.67.0</version>
        </dependency>
        <dependency>
            <groupId>com.bestpay.bigdata</groupId>
            <artifactId>bi-database</artifactId>
            <version>1.67.0</version>
        </dependency>
        <dependency>
            <groupId>com.bestpay.bigdata</groupId>
            <artifactId>bi-backend-api</artifactId>
            <version>1.67.0</version>
        </dependency>
        <dependency>
            <groupId>com.bestpay.bigdata</groupId>
            <artifactId>bi-parsesql</artifactId>
            <version>1.67.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.bestpay.bigdata</groupId>
            <artifactId>bi-thirdparty</artifactId>
            <version>1.67.0</version>
        </dependency>

        <dependency>
            <groupId>org.apache.lucene</groupId>
            <artifactId>lucene-core</artifactId>
            <version>4.0.0</version>
        </dependency>

        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-common</artifactId>
            <version>${hadoop.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-hdfs</artifactId>
            <version>${hadoop.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-client</artifactId>
            <version>${hadoop.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.15</version>
            <scope>runtime</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>
        </plugins>
    </build>



</project>

