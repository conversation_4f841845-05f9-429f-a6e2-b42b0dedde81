<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>bi-probe</artifactId>
    <groupId>com.bestpay.bigdata</groupId>
    <version>1.67.0</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>bi-probe-auth</artifactId>

  <properties>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.alibaba.boot</groupId>
      <artifactId>dubbo-spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.github.sgroschupf</groupId>
      <artifactId>zkclient</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.bestpay.bigdata</groupId>
      <artifactId>bi-common</artifactId>
      <version>${parent.version}</version>
    </dependency>
    <dependency>
      <groupId>com.bestpay.bigdata</groupId>
      <artifactId>bi-parsesql</artifactId>
      <version>${parent.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>slf4j-log4j12</artifactId>
          <groupId>org.slf4j</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.bestpay.bigdata</groupId>
      <artifactId>bi-thirdparty</artifactId>
      <version>${parent.version}</version>
    </dependency>

  </dependencies>
</project>