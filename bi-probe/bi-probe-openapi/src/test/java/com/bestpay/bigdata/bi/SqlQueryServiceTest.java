package com.bestpay.bigdata.bi;

import com.bestpay.bigdata.bi.common.bean.AiPlusUserSearchRequest;
import com.bestpay.bigdata.bi.common.entity.QueryContext;
import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.common.entity.Org;
import com.bestpay.bigdata.bi.openapi.service.SqlQueryService;
import com.bestpay.bigdata.bi.thirdparty.api.AiPlusUserService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class SqlQueryServiceTest {

    @Mock
    private AiPlusUserService aiPlusUserService;

    @InjectMocks
    private SqlQueryService sqlQueryService;

    private QueryContext queryContext;

    @BeforeEach
    void setUp() {
        queryContext = new QueryContext();
    }

    @Test
    void handleOrgInfo_shouldSetUserOrg_whenUserInfoExists() {
        // 准备测试数据
        String email = "<EMAIL>";
        String orgName = "测试组织";
        
        // 构建模拟用户信息
        UserInfo userInfo = new UserInfo();
        Org org = new Org();
        org.setName(orgName);
        userInfo.setOrg(org);
        
        // 模拟服务返回
        when(aiPlusUserService.getUserList(any(AiPlusUserSearchRequest.class)))
                .thenReturn(Arrays.asList(userInfo));

        // 执行测试
        sqlQueryService.handleOrgInfo(email, queryContext);

        // 验证结果
        assertEquals(orgName, queryContext.getUserOrg());
    }

    @Test
    void handleOrgInfo_shouldNotSetUserOrg_whenUserInfoNotExists() {
        // 准备测试数据
        String email = "<EMAIL>";
        
        // 模拟服务返回空列表
        when(aiPlusUserService.getUserList(any(AiPlusUserSearchRequest.class)))
                .thenReturn(Collections.emptyList());

        // 执行测试
        sqlQueryService.handleOrgInfo(email, queryContext);

        // 验证结果
        assertNull(queryContext.getUserOrg());
    }

    @Test
    void handleOrgInfo_shouldNotThrowException_whenEmailIsEmpty() {
        // 准备测试数据
        String email = "";
        
        // 模拟服务返回空列表
        when(aiPlusUserService.getUserList(any(AiPlusUserSearchRequest.class)))
                .thenReturn(Collections.emptyList());

        // 执行测试并验证不会抛出异常
        assertDoesNotThrow(() -> sqlQueryService.handleOrgInfo(email, queryContext));
    }
}