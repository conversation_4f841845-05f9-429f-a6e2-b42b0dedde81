<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>bi-probe</artifactId>
    <groupId>com.bestpay.bigdata</groupId>
    <version>1.67.0</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>bi-probe-openapi</artifactId>
  <packaging>jar</packaging>
  <dependencies>
    <dependency>
      <groupId>com.bestpay.bigdata</groupId>
      <artifactId>bi-database</artifactId>
      <version>1.67.0</version>
    </dependency>
    <dependency>
      <groupId>com.alibaba.boot</groupId>
      <artifactId>dubbo-spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.github.sgroschupf</groupId>
      <artifactId>zkclient</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>com.bestpay.bigdata</groupId>
      <artifactId>bi-common</artifactId>
      <version>${parent.version}</version>
    </dependency>
    <dependency>
      <groupId>com.bestpay.bigdata</groupId>
      <artifactId>bi-backend-api</artifactId>
      <version>${parent.version}</version>
    </dependency>
    <dependency>
      <groupId>com.bestpay.bigdata</groupId>
      <artifactId>bi-probe-auth</artifactId>
      <version>${parent.version}</version>
    </dependency>
    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
      <version>8.0.15</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-jdbc</artifactId>
    </dependency>

  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <includeSystemScope>true</includeSystemScope>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>