<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>bi-analysis</artifactId>
    <groupId>com.bestpay.bigdata</groupId>
    <version>1.67.0</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>bi-analysis-service</artifactId>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <java.version>1.8</java.version>
    <calcite.version>1.27.0</calcite.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.oceanbase</groupId>
      <artifactId>oceanbase-client</artifactId>
      <version>2.4.0</version>
    </dependency>

    <dependency>
      <groupId>com.bestpay.bigdata</groupId>
      <artifactId>bi-common</artifactId>
      <version>1.67.0</version>
      <exclusions>
        <exclusion>
          <artifactId>protobuf-java</artifactId>
          <groupId>com.google.protobuf</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.bestpay.bigdata</groupId>
      <artifactId>bi-analysis-api</artifactId>
      <version>1.67.0</version>
    </dependency>
    <dependency>
      <groupId>com.bestpay.bigdata</groupId>
      <artifactId>bi-thirdparty</artifactId>
      <version>1.67.0</version>
    </dependency>
    <dependency>
      <groupId>com.bestpay.bigdata</groupId>
      <artifactId>bi-database</artifactId>
      <version>1.67.0</version>
    </dependency>
    <dependency>
      <groupId>com.alibaba.boot</groupId>
      <artifactId>dubbo-spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.github.sgroschupf</groupId>
      <artifactId>zkclient</artifactId>
    </dependency>


    <dependency>
      <groupId>org.apache.calcite</groupId>
      <artifactId>calcite-core</artifactId>
      <version>${calcite.version}</version>
    </dependency>

    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>29.0-jre</version>
    </dependency>
    <dependency>
      <groupId>ru.yandex.clickhouse</groupId>
      <artifactId>clickhouse-jdbc</artifactId>
      <version>0.2.4</version>
    </dependency>

    <dependency>
      <groupId>com.bestpay.bigdata</groupId>
      <artifactId>bi-backend-api</artifactId>
      <version>1.67.0</version>
    </dependency>
    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
      <version>8.0.15</version>
      <scope>runtime</scope>
    </dependency>

    <dependency>
      <groupId>org.apache.kyuubi</groupId>
      <artifactId>kyuubi-hive-jdbc-shaded</artifactId>
      <version>1.6.0-incubating</version>
    </dependency>

    <dependency>
      <groupId>org.mybatis.spring.boot</groupId>
      <artifactId>mybatis-spring-boot-starter</artifactId>
      <version>2.1.4</version>
    </dependency>
    <dependency>
      <groupId>com.github.pagehelper</groupId>
      <artifactId>pagehelper-spring-boot-starter</artifactId>
      <version>1.2.5</version>
      <exclusions>
        <exclusion>
          <artifactId>mybatis-spring-boot-starter</artifactId>
          <groupId>org.mybatis.spring.boot</groupId>
        </exclusion>
      </exclusions>
    </dependency>


    <dependency>
      <groupId>io.github.biezhi</groupId>
      <artifactId>TinyPinyin</artifactId>
      <version>2.0.3.RELEASE</version>
    </dependency>



  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <includeSystemScope>true</includeSystemScope>
        </configuration>
        <version>2.2.6.RELEASE</version>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>2.3.2</version>
        <configuration>
          <source>8</source>
          <target>8</target>
        </configuration>
      </plugin>
    </plugins>
  </build>

</project>