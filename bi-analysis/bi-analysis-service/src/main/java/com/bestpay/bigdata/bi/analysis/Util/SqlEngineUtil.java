package com.bestpay.bigdata.bi.analysis.Util;

import com.bestpay.bigdata.bi.common.enums.FieldType;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;

/**
 * ClassName: SqlEngineUtil
 * Package: com.bestpay.bigdata.bi.analysis.Util
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/11/10 15:32
 * @Version 1.0
 */
public class SqlEngineUtil {

    public static final List<String> MYSQL_CHARACTER_TYPE_LIST = Lists.newArrayList("CHAR", "VARCHAR", "TINYTEXT",
            "TEXT", "LONGTEXT", "TINYBLOB", "BLOB", "<PERSON><PERSON><PERSON><PERSON>OB");

    public static final List<String> PG_CHARACTER_TYPE_LIST = Lists.newArrayList("CHAR", "CHARACTER", "VARCHAR", "TEXT", "CHARACTER VARYING");

    public static final List<String> CK_CHARACTER_TYPE_LIST
            = Lists.newArrayList("STRING", "FIXEDSTRING", "UUID", "NULLABLE(STRING)");


    public static String ckCompatibleDataType(String originEnName, String originType, String showType) {
        String convertEnName = null;

        // originType belong to CHARACTER but showType is DECIMAL
        if (CK_CHARACTER_TYPE_LIST.contains(originType.toUpperCase())) {
            if (showType.equals(FieldType.DECIMAL.name())) {
                convertEnName = "toFloat64OrZero(toNullable(" + originEnName + "))";
            } else if (showType.equals(FieldType.DATETIME.name())) {
                convertEnName = "parseDateTimeBestEffortOrNull(toNullable(" + originEnName + "))";
            }
        }
        if (Objects.isNull(convertEnName)) {
            convertEnName = originEnName;
        }
        return convertEnName;
    }


    public static String pgCompatibleDataType(String originEnName, String originType, String showType) {
        String convertEnName = null;

        // originType belong to CHARACTER but showType is DECIMAL
        if (PG_CHARACTER_TYPE_LIST.contains(originType.toUpperCase())) {
            if (showType.equals(FieldType.DECIMAL.name())) {
                convertEnName = "CAST(" + originEnName + " AS  DECIMAL)";
            } else if (showType.equals(FieldType.DATETIME.name())) {
                convertEnName = "TO_TIMESTAMP(" + originEnName + " , 'YYYY-MM-DD HH24:MI:SS')";
            }
        }
        if (Objects.isNull(convertEnName)) {
            convertEnName = originEnName;
        }
        return convertEnName;
    }


    public static String mysqlCompatibleDataType(String originEnName, String originType, String showType) {
        String convertEnName = null;

        // originType belong to CHARACTER but showType is DECIMAL
        if (MYSQL_CHARACTER_TYPE_LIST.contains(originType.toUpperCase())) {
            if (showType.equals(FieldType.DECIMAL.name())) {
                convertEnName = "CAST(" + originEnName + " AS  DECIMAL(65,30))";
            } else if (showType.equals(FieldType.DATETIME.name())) {
                convertEnName = "CAST(" + originEnName + " AS DATETIME)";
            }
        }
        if (Objects.isNull(convertEnName)) {
            convertEnName = originEnName;
        }
        return convertEnName;
    }
}
