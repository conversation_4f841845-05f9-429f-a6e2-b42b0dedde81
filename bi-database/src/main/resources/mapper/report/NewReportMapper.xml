<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bestpay.bigdata.bi.database.mapper.report.NewReportMapper">

  <resultMap id="ReportResultMap" type="com.bestpay.bigdata.bi.database.dao.report.NewReportDO">
    <id property="id" column="id" javaType="java.lang.Long"/>
    <result property="orgSelected" column="org_selected" javaType="java.lang.String"/>
    <result property="orgCode" column="org_code" javaType="java.lang.String"/>
    <result property="dirId" column="dir_id" javaType="java.lang.Long"/>
    <result property="reportName" column="report_name" javaType="java.lang.String"/>
    <result property="orgAuth" column="org_auth" javaType="java.lang.String"/>
    <result property="reportDesc" column="report_desc" javaType="java.lang.String"/>
    <result property="dataAuth" column="data_auth" javaType="java.lang.String"/>
    <result property="ownerNameCh" column="owner_name_ch" javaType="java.lang.String"/>
    <result property="ownerName" column="owner_name" javaType="java.lang.String"/>
    <result property="datasetId" column="dataset_id" javaType="java.lang.Long"/>
    <result property="email" column="email" javaType="java.lang.String"/>
    <result property="fileContainSensitiveInfo" column="file_contain_sensitive_info" javaType="java.lang.Integer"/>
    <result property="sensitiveFields" column="sensitive_fields" javaType="java.lang.String"/>
    <result property="statusCode" column="status_code" javaType="java.lang.Integer"/>
    <result property="orderNum" column="order_num" javaType="java.lang.Long"/>
    <result property="reportStructure" column="report_structure" javaType="java.lang.String"/>
    <result property="createdAt" column="created_at" javaType="java.util.Date"/>
    <result property="createdBy" column="created_by" javaType="java.lang.String"/>
    <result property="updatedAt" column="updated_at" javaType="java.util.Date"/>
    <result property="updatedBy" column="updated_by" javaType="java.lang.String"/>
  </resultMap>

  <select id="queryAll" resultMap="ReportResultMap">
    select
    id,
    org_selected,
    org_code,
    dir_id,
    order_num,
    report_name,
    org_auth,
    report_desc,
    data_auth,
    owner_name_ch,
    owner_name,
    dataset_id,
    email,
    report_structure,
    status_code,
    created_at,
    created_by,
    updated_at,
    updated_by
    from t_new_report r
    <where>
      status_code!=9
      <if test="dirId != null">
        and dir_id = #{dirId}
      </if>
      <if test="orgAuth != null and orgAuth != ''">
        and org_auth like CONCAT('%',#{orgAuth},'%')
      </if>
      <if test="orgCode != null and orgCode != ''">
        and org_code = #{orgCode}
      </if>
      <if test="reportNameKeyword != null and reportNameKeyword != ''">
        and report_name like CONCAT('%',#{reportNameKeyword},'%')
      </if>
      <if test="idList != null and idList.size > 0">
        and id in
        <foreach collection="idList" item="item" index="index" separator="," open="(" close=")">
          #{item}
        </foreach>
      </if>
    </where>
    order by order_num;
  </select>



    <select id="queryAllReport" resultMap="ReportResultMap">
        select
        *
        from t_new_report r
        <where>
            status_code!=9
        </where>
    </select>

  <select id="getMaxDirSort" parameterType="int" resultType="Long">
    select max(order_num) from t_new_report
    where `status_code`!=9
  </select>

  <select id="queryExitData" resultMap="ReportResultMap">
    select
    id, report_name
    from t_new_report
    <where>
      status_code!=9
      and report_name = #{reportName}
      <if test="id != null">
        and id != #{id}
      </if>
    </where>
  </select>

  <insert id="insertReport">
    <selectKey resultType="Long" keyProperty="id" order="AFTER">
      SELECT LAST_INSERT_ID()
    </selectKey>
    INSERT INTO t_new_report
    (<if test="id != null">id,</if>
     org_selected,
     org_code,
     dir_id,
     report_name,
     org_auth,
     report_desc,
     data_auth,
     owner_name_ch,
     owner_name,
     dataset_id,
     email,
     file_contain_sensitive_info,
     sensitive_fields,
     status_code,
     order_num,
     report_structure,
     created_at,
     created_by,
     updated_at,
     updated_by)
    VALUES
      (<if test="id != null">#{id},</if>
       #{orgSelected},
       #{orgCode},
       #{dirId},
       #{reportName},
       #{orgAuth},
       #{reportDesc},
       #{dataAuth},
       #{ownerNameCh},
       #{ownerName},
       #{datasetId},
       #{email},
       #{fileContainSensitiveInfo},
       #{sensitiveFields},
       #{statusCode},
       #{orderNum},
       #{reportStructure},
       NOW(),
       #{createdBy},
       NOW(),
       #{updatedBy}
       )
  </insert>





    <insert id="insertShadeReport">
        INSERT INTO t_new_shade_report
        (
        id,
        org_selected,
        org_code,
        dir_id,
        report_name,
        org_auth,
        report_desc,
        data_auth,
        owner_name_ch,
        owner_name,
        dataset_id,
        email,
        file_contain_sensitive_info,
        sensitive_fields,
        status_code,
        order_num,
        report_structure,
        created_at,
        created_by,
        updated_at,
        updated_by)
        VALUES
        (
        #{id},
        #{orgSelected},
        #{orgCode},
        #{dirId},
        #{reportName},
        #{orgAuth},
        #{reportDesc},
        #{dataAuth},
        #{ownerNameCh},
        #{ownerName},
        #{datasetId},
        #{email},
        #{fileContainSensitiveInfo},
        #{sensitiveFields},
        #{statusCode},
        #{orderNum},
        #{reportStructure},
        #{createdAt},
        #{createdBy},
        #{updatedAt},
        #{updatedBy}
        )
    </insert>



    <select id="queryShadeAll" resultMap="ReportResultMap">
        select
        *
        from t_new_shade_report r
        <where>
            status_code!=9
        </where>
        order by order_num
    </select>


  <update id="updateReport" parameterType="com.bestpay.bigdata.bi.database.dao.report.NewReportDO">
    UPDATE t_new_report
    <set>
      <if test="orgSelected != null">org_selected = #{orgSelected},</if>
      <if test="orgCode != null">org_code = #{orgCode},</if>
      <if test="dirId != null">dir_id = #{dirId},</if>
      <if test="reportName != null">report_name = #{reportName},</if>
      <if test="orgAuth != null">org_auth = #{orgAuth},</if>
      <if test="reportDesc != null">report_desc = #{reportDesc},</if>
      <if test="dataAuth != null">data_auth = #{dataAuth},</if>
      <if test="ownerNameCh != null">owner_name_ch = #{ownerNameCh},</if>
      <if test="ownerName != null">owner_name = #{ownerName},</if>
      <if test="datasetId != null">dataset_id = #{datasetId},</if>
      <if test="email != null">email = #{email},</if>
      <if test="fileContainSensitiveInfo != null">file_contain_sensitive_info = #{fileContainSensitiveInfo},</if>
      <if test="sensitiveFields != null">sensitive_fields = #{sensitiveFields},</if>
      <if test="statusCode != null">status_code = #{statusCode},</if>
      <if test="orderNum != null">order_num = #{orderNum},</if>
      <if test="reportStructure != null">report_structure = #{reportStructure},</if>
      updated_at = #{updatedAt},
      updated_by = #{updatedBy}
    </set>
    WHERE id = #{id}
  </update>

  <select id="getReportById" resultMap="ReportResultMap">
    SELECT *
    FROM t_new_report
    WHERE id = #{id}
  </select>
    <select id="queryAllIdList" resultType="java.lang.Long">
        select
         id
        from t_new_report
        where
        status_code!=9
    </select>

  <select id="topk" resultMap="ReportResultMap">
    select t2.id,t2.report_name from (
      select t.res_id from t_dataset_res_access_stats t where t.res_type ='report'
      and exists(
        select null from t_new_report t2 where t2.status_code!=9
        and t2.id=t.res_id
        <if test="orgList != null and orgList.size > 0">
          and t2.org_code in
          <foreach collection="orgList" item="item" index="index" separator="," open="(" close=")">
            #{item}
          </foreach>
        </if>
      )
      order by t.access_cnt desc limit 10
    ) t inner join t_new_report t2 on t.res_id=t2.id
  </select>

</mapper>
