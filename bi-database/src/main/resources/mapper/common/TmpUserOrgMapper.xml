<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bestpay.bigdata.bi.database.mapper.common.TmpUserOrgMapper">

    <resultMap type="com.bestpay.bigdata.bi.database.dao.common.UserOrgDO" id="map">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="username" column="user_name" jdbcType="VARCHAR"/>
        <result property="userOrg" column="auth_resource_id" jdbcType="VARCHAR"/>
    </resultMap>
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_tmp_user_org (
        user_name,
        user_org
        )
        VALUES
        <foreach collection="userOrgDOList" item="item" index="index" separator=",">
            (
            #{item.username,jdbcType=VARCHAR},
            #{item.userOrg,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>


</mapper>