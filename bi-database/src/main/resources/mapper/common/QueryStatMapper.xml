<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bestpay.bigdata.bi.database.mapper.common.QueryStatMapper">

  <resultMap type="com.bestpay.bigdata.bi.database.bean.QueryStat" id="QueryStatMap">
    <result property="id" column="id" jdbcType="INTEGER"/>
    <result property="username" column="user_name" jdbcType="VARCHAR"/>
    <result property="requestSystem" column="request_system" jdbcType="VARCHAR"/>
    <result property="userOrg" column="user_org" jdbcType="VARCHAR"/>
    <result property="queryStartMillis" column="query_start_millis" jdbcType="TIMESTAMP"/>
    <result property="queryFinishMillis" column="query_finish_millis" jdbcType="TIMESTAMP"/>
    <result property="queryTimeMillis" column="query_time_millis" jdbcType="INTEGER"/>
    <result property="status" column="status" jdbcType="INTEGER"/>
    <result property="message" column="message" jdbcType="VARCHAR"/>
    <result property="engineName" column="engine_name" jdbcType="VARCHAR"/>
    <result property="sql" column="query_sql" jdbcType="VARCHAR"/>
    <result property="resultRowsNum" column="result_rows_num" jdbcType="INTEGER"/>
    <result property="resultLength" column="result_length" jdbcType="VARCHAR"/>
    <result property="engineQueryId" column="engine_query_id" jdbcType="VARCHAR"/>
    <result property="queryId" column="query_id" jdbcType="VARCHAR"/>
    <result property="traceId" column="trace_id" jdbcType="VARCHAR"/>
    <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
    <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
    <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
    <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
    <result property="typeCode" column="type_code" jdbcType="INTEGER"/>
    <result property="clusterName" column="cluster_name" jdbcType="VARCHAR"/>
  </resultMap>

  <!--新增所有列-->
  <insert id="insert" keyProperty="id" useGeneratedKeys="true">
    insert into t_query_stat(user_name,
                             request_system,
                             user_org,
                             query_start_millis,
                             query_finish_millis,
                             query_time_millis,
                             status,
                             message,
                             engine_name,
                             cluster_name,
                             query_sql,
                             result_rows_num,
                             result_length,
                             engine_query_id,
                             query_id,
                             trace_id,
                             created_at,
                             created_by,
                             updated_at,
                             updated_by,
                             type_code)
    values (#{username},
            #{requestSystem},
            #{userOrg},
            #{queryStartMillis},
            #{queryFinishMillis},
            #{queryTimeMillis},
            #{status},
            #{message},
            #{engineName},
            #{clusterName},
            #{sql},
            #{resultRowsNum},
            #{resultLength},
            #{engineQueryId},
            #{queryId},
            #{traceId},
            #{createdAt},
            #{createdBy},
            #{updatedAt},
            #{updatedBy},
            #{typeCode})
  </insert>
    <!--通过主键删除-->
  <delete id="deleteByDueTimeLessThanCreatedAt">
    delete
    from t_query_stat
    where created_at &lt; #{dueTime}
  </delete>

  <select id="query" resultMap="QueryStatMap" >
      SELECT * FROM t_query_stat
      <where>
          1=1
          <if test="createdStartAt != null and createdStartAt != ''">
              and created_at <![CDATA[ >= ]]> #{createdStartAt}
          </if>
          <if test="createdEndAt != null and createdEndAt != ''">
              and created_at <![CDATA[ <= ]]> #{createdEndAt}
          </if>
          <if test="engineName != null and engineName != ''">
              and engine_name = #{engineName}
          </if>
          <if test="username != null and username != ''">
              and user_name = #{username}
          </if>
          <if test="typeCode != null">
              and type_code = #{typeCode}
          </if>
          <if test="status != null">
              and status != #{status}
          </if>
      </where>
  </select>
    <select id="queryGtId" resultType="java.lang.String">
        SELECT user_name FROM t_query_stat
        WHERE id > #{id}
        <if test="typeCode != null">
            and type_code = #{typeCode}
        </if>
    </select>


</mapper>

