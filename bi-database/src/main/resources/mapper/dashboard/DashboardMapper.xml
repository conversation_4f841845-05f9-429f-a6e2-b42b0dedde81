<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bestpay.bigdata.bi.database.mapper.dashboard.DashboardMapper">

  <resultMap type="com.bestpay.bigdata.bi.database.bean.dashboard.Dashboard" id="BaseMap">
    <result property="id" column="id" jdbcType="BIGINT"/>
    <result property="typeCode" column="type_code" jdbcType="INTEGER"/>
    <result property="statusCode" column="status_code" jdbcType="INTEGER"/>
    <result property="isTabDashboard" column="is_tab_dashboard" jdbcType="INTEGER"/>
    <result property="isPublishMobile" column="is_publish_mobile" jdbcType="INTEGER"/>
    <result property="orgCode" column="org_code" jdbcType="VARCHAR"/>
    <result property="dirSort" column="dir_sort" jdbcType="BIGINT"/>
    <result property="dirId" column="dir_id" jdbcType="BIGINT"/>
    <result property="orderNum" column="order_num" jdbcType="BIGINT"/>
    <result property="name" column="name" jdbcType="VARCHAR"/>
    <result property="outsideDir" column="outside_dir" jdbcType="VARCHAR"/>
    <result property="orgAuth" column="org_auth" jdbcType="VARCHAR"/>
    <result property="owner" column="owner" jdbcType="VARCHAR"/>
    <result property="ownerEn" column="owner_en" jdbcType="VARCHAR"/>
    <result property="latestPublishAt" column="latest_publish_at" jdbcType="TIMESTAMP"/>
    <result property="ownerEmail" column="owner_email" jdbcType="VARCHAR"/>
    <result property="displayType" column="display_type" jdbcType="BIGINT"/>
    <result property="isShare" column="is_share" jdbcType="BIGINT"/>
    <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
    <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
    <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
    <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
    <result property="dirName" column="dir_name" jdbcType="VARCHAR"/>
    <result property="dataScreenConfId" column="data_screen_conf_id" jdbcType="BIGINT"/>
  </resultMap>

  <select id="getById" parameterType="java.lang.Long" resultMap="BaseMap">
      select db.`id`,
      db.`org_code`,
      db.`dir_id`,
      db.`order_num`,
      db.`type_code`,
      db.`is_tab_dashboard`,
      db.`is_publish_mobile`,
      db.`outside_dir`,
      db.`org_auth`,
      db.`name`,
      db.`owner`,
      db.`owner_en`,
      db.`owner_email`,
      db.`display_type`,
      db.`is_share`,
      db.`latest_publish_at`, td.`name` dir_name,
      db.`created_at`,
      db.`status_code`,
      db.`created_by`,
      db.`updated_at`,
      db.`updated_by`,
      td.`name` dir_name,
      tdsc.id as data_screen_conf_id
    from t_dashboard db left join t_dashboard_directory td on db.dir_id = td.id
      left join t_data_screen_conf tdsc on db.id = tdsc.dashboard_id
    where db.id = #{id} and db.`status_code`!=9
  </select>

  <select id="queryByCondition" parameterType="com.bestpay.bigdata.bi.database.bean.dashboard.DashboardQuery" resultMap="BaseMap">
    select db.`id`,
      db.`org_code`,
      db.`org_auth`,
      db.`type_code`,
      db.`is_tab_dashboard`,
      db.`is_publish_mobile`,
      td.`dir_sort`,
      db.`order_num`,
      db.`dir_id`,
      db.`name`,
      db.`owner`,
      db.`owner_en`,
      db.`owner_email`,
      db.`latest_publish_at`,
      td.`name` dir_name,
      db.status_code
    from t_dashboard db left join t_dashboard_directory td on db.dir_id = td.id
    <where>
        1=1 and db.`status_code`!=9
        <if test="typeCode!=null ">
            and db.`type_code` =#{typeCode}
        </if>
        <if test="dirId!=null ">
            and db.`dir_id` =#{dirId}
        </if>
        <if test='displayType=="0"'>
          and td.status_code=0
        </if>
        <if test="orgCode!=null and orgCode!=''">
            and db.`org_code` = #{orgCode}
        </if>
        <if test="orgAuth != null and orgAuth != ''">
            and db.`org_auth` like CONCAT('%',#{orgAuth},'%')
        </if>
        <if test="ownerEmail!=null and ownerEmail!=''">
            and db.`owner_email` = #{ownerEmail}
        </if>
        <if test="name!=null and name!=''">
            and db.`name` =#{name}
        </if>
        <if test="displayType!=null">
            and db.`display_type` =#{displayType}
        </if>
        <if test="keyword!=null and keyword!=''">
            and db.`name` like  CONCAT('%',#{keyword},'%')
        </if>
        <if test="isPublishMobile != null">
            and db.`is_publish_mobile` = #{isPublishMobile}
        </if>
    </where>
      order by db.`order_num` asc
  </select>

  <!--新增所有列-->
  <insert id="insert" keyProperty="id" parameterType="com.bestpay.bigdata.bi.database.bean.dashboard.Dashboard"
    useGeneratedKeys="true">
    <selectKey resultType="Long" keyProperty="id" order="AFTER">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_dashboard(`type_code`,
                            `is_tab_dashboard` ,
                            `org_code`,
                            `name`,
                            `dir_id`,
                            `order_num`,
                            `outside_dir`,
                            `org_auth`,
                            `owner`,
                            `owner_en`,
                            `owner_email`,
                            display_type,
                            created_by,
                            updated_by,
                            created_at,
                            updated_at)
    values (#{typeCode},
            #{isTabDashboard},
            #{orgCode},
            #{name},
            #{dirId},
            #{orderNum},
            #{outsideDir},
            #{orgAuth},
            #{owner},
            #{ownerEn},
            #{ownerEmail},
            #{displayType},
            #{createdBy},
            #{updatedBy},
            #{createdAt},
            #{updatedAt})
  </insert>

  <update id="updateById" parameterType="com.bestpay.bigdata.bi.database.bean.dashboard.Dashboard">
    update t_dashboard
    <set>
      <if test="typeCode!=null and typeCode!=''">
        type_code = #{typeCode},
      </if>
      <if test="isTabDashboard!=null">
        is_tab_dashboard= #{isTabDashboard},
      </if>
      <if test="isPublishMobile != null">
        `is_publish_mobile` = #{isPublishMobile},
      </if>
      <if test="orgCode!=null and orgCode!=''">
        org_code = #{orgCode},
      </if>
      <if test="dirId!=null">
        dir_id = #{dirId},
      </if>
      <if test="orderNum != null">
        order_num = #{orderNum},
      </if>
      <if test="name!=null and name!=''">
        `name` = #{name},
      </if>
        <if test="outsideDir!=null and outsideDir!=''">
            `outside_dir` = #{outsideDir},
        </if>
        <if test="orgAuth!=null and orgAuth!=''">
            `org_auth` = #{orgAuth},
        </if>
      <if test="owner!=null and owner!=''">
        `owner` = #{owner},
      </if>
      <if test="ownerEn!=null and ownerEn!=''">
        `owner_en` = #{ownerEn},
      </if>
      <if test="ownerEmail!=null and ownerEmail!=''">
        owner_email = #{ownerEmail},
      </if>
      <if test="latestPublishAt!=null">
        latest_publish_at = #{latestPublishAt},
      </if>
      <if test="displayType!=null">
        display_type = #{displayType},
      </if>
      <if test="isShare!=null">
        is_share = #{isShare},
      </if>
      <if test="statusCode != null">
        status_code = #{statusCode},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy}
      </if>
    </set>
    where id =#{id}
  </update>

    <select id="queryListByDirectoryId" parameterType="long" resultMap="BaseMap">
        select * from t_dashboard
        where `status_code`!=9
        <if test="directoryId != null">
           and `dir_id` = #{directoryId}
        </if>
    </select>

    <select id="getMaxDirSort" parameterType="int" resultType="Long">
        select max(order_num) from t_dashboard
        where `status_code`!=9
        <if test="displayType != null">
            and display_type = #{displayType}
        </if>

        <if test="directoryId != null">
            and dir_id = #{directoryId}
        </if>

    </select>

    <select id="queryByIdList" resultMap="BaseMap">
        select db.`id`,
               db.`org_code`,
               db.`type_code`,
               db.`is_tab_dashboard`,
               db.`is_publish_mobile`,
               db.`dir_id`,
               db.`order_num`,
               db.`name`,
               db.`owner`,
               db.`owner_en`,
               db.`owner_email`,
               db.`latest_publish_at`,
               db.status_code
        from t_dashboard db
        <where>
            db.`status_code`!=9
            <if test="dashboardIdList!=null and dashboardIdList.size > 0">
                and db.`id` IN
                <foreach collection="dashboardIdList" open="(" close=")" separator="," item="id" >
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <select id="queryAllIdList" resultType="java.lang.Long">
        SELECT id FROM t_dashboard WHERE status_code  != 9;
    </select>


</mapper>

