package com.bestpay.bigdata.bi.database.dao.common;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;


/**
 * 数据源连接配置
 * <AUTHOR>
 */
@Data
public class DataSourceConnectConfigDO implements Serializable {
    private static final long serialVersionUID = -6617409399201332595L;

    private Long id;

    /**
     * 数据源
     */
    private String dataSource;

    /**
     * 引擎类型
     */
    private String engineName;

    /**
     * 请求系统
     */
    private String requestSystem;

    /**
     * 分组
     */
    private String routerGroup;

    /**
     * 连接配置
     */
    private String config;

    /**
     * encrypt pwd
     */
    private String encryptCode;

    /**
     * 状态（0：已上线  1：已下线  9：删除）
     */
    private Integer statusCode;

    private Date createdAt;

    private String createdBy;

    private Date updatedAt;

    private String updatedBy;

    private String centerName;

    private int maxWorkReqNum;

    private long maxSeatMills;

    private int queueLimit;

    private long maxWaitMills;
}
