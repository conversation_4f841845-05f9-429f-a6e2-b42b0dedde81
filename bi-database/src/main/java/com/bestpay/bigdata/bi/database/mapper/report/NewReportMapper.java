package com.bestpay.bigdata.bi.database.mapper.report;

import com.bestpay.bigdata.bi.database.bean.report.ReportQueryDTO;
import com.bestpay.bigdata.bi.database.dao.report.NewReportDO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface NewReportMapper {

  NewReportDO getReportById(Long reportId);

  Long insertReport(NewReportDO report);

  Long insertShadeReport(NewReportDO report);

  List<NewReportDO> queryShadeAll(ReportQueryDTO report);

  int updateReport(NewReportDO report);

  List<NewReportDO> queryExitData(@Param("reportName")String reportName, @Param("id") Long id);

  Long getMaxDirSort();

  List<NewReportDO> queryAll(ReportQueryDTO report);

  List<NewReportDO> queryAllReport();

  List<Long> queryAllIdList();

  List<NewReportDO> topk(@Param("orgList") List<String> orgList);
}
