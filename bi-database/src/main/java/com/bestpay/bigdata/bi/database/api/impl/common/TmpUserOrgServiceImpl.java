package com.bestpay.bigdata.bi.database.api.impl.common;

import com.bestpay.bigdata.bi.database.api.common.TmpUserOrgService;
import com.bestpay.bigdata.bi.database.dao.common.UserOrgDO;
import com.bestpay.bigdata.bi.database.mapper.common.ObjectAuthMapper;
import com.bestpay.bigdata.bi.database.mapper.common.TmpUserOrgMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class TmpUserOrgServiceImpl implements TmpUserOrgService {

    @Resource
    private TmpUserOrgMapper tmpUserOrgMapper;
    @Override
    public int batchInsert(List<UserOrgDO> userOrgDOList) {
        return tmpUserOrgMapper.batchInsert(userOrgDOList);
    }
}
