package com.bestpay.bigdata.bi.database.api.dataset;

import com.bestpay.bigdata.bi.common.dto.dataset.DatasetQueryDTO;
import com.bestpay.bigdata.bi.common.dto.dataset.ResourceBaseDO;
import com.bestpay.bigdata.bi.database.dao.dataset.DatasetDo;
import com.bestpay.bigdata.bi.database.dao.dataset.DatasetWithResAccessStatsDo;
import com.bestpay.bigdata.bi.database.dao.dataset.ResAccessStatsDO;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-07-04-15:59
 */
public interface DatasetDAOService {

    DatasetDo findLatestDatasetDo();

    int insertOrUpdate(DatasetDo datasetDo);

    List<DatasetDo> query(DatasetQueryDTO queryDTO);

    List<DatasetWithResAccessStatsDo> queryWithAccessStats(DatasetQueryDTO queryDTO);

    DatasetDo queryDoByDatasetCode(String datasetCode);


    /**
     * 使用这个接口 注意必须设置 id 或者 code
     * @param datasetDo
     * @return
     */
    int updateDatasetDo(DatasetDo datasetDo);

    Long count(DatasetQueryDTO pageQueryDTO);

    List<ResourceBaseDO> queryResList(ResAccessStatsDO build,List<Long> screenIds);

    List<String> resNameList(Long datasetId);

    /**
     * 查询数据集分页信息
     * @param datasetQueryDTO 查询条件 包含分页数据 ，关键字等
     * @return 数据集相关的信息
     */
    List<DatasetDo> queryDataSetList(DatasetQueryDTO datasetQueryDTO);
}
