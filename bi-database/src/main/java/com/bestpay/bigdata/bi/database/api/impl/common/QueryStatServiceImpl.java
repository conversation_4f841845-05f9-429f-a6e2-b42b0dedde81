package com.bestpay.bigdata.bi.database.api.impl.common;


import com.bestpay.bigdata.bi.database.api.common.QueryStatService;
import com.bestpay.bigdata.bi.database.bean.QueryStat;
import com.bestpay.bigdata.bi.database.bean.QueryStatDTO;
import com.bestpay.bigdata.bi.database.mapper.common.QueryStatMapper;

import java.util.Date;
import java.util.List;
import java.util.Set;

import org.springframework.stereotype.Component;

/**
 * 查询使用统计(QueryStat)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-01-17 15:08:22
 */
@Component
public class QueryStatServiceImpl implements QueryStatService {


  private QueryStatMapper queryStatMapper;
  public QueryStatServiceImpl(QueryStatMapper queryStatMapper) {
    this.queryStatMapper=queryStatMapper;
  }

  /**
   * 新增数据
   *
   * @param queryStat 实例对象
   * @return 实例对象
   */
  @Override
  public QueryStat insert(QueryStat queryStat) {
    this.queryStatMapper.insert(queryStat);
    return queryStat;
  }


  /**
   * 删除截至时间之前的数据
   * @param dueTime 截至时间
   * @return 影响行数
   */
  @Override
  public long deleteByDueTimeLessThanCreatedAt(Date dueTime) {
    return this.queryStatMapper.deleteByDueTimeLessThanCreatedAt(dueTime);
  }


  @Override
  public List<QueryStat> query(QueryStatDTO queryStatDTO) {
    return this.queryStatMapper.query(queryStatDTO);
  }


  @Override
  public Set<String> queryGtId(Long id, Integer typeCode) {
    return this.queryStatMapper.queryGtId(id,typeCode);
  }
}
