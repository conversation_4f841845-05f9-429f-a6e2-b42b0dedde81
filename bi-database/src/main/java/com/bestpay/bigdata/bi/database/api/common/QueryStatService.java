package com.bestpay.bigdata.bi.database.api.common;

import com.bestpay.bigdata.bi.database.bean.QueryStat;
import com.bestpay.bigdata.bi.database.bean.QueryStatDTO;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 查询使用统计(QueryStat)表服务接口
 *
 * <AUTHOR>
 * @since 2022-01-17 15:08:22
 */
public interface QueryStatService {
  /**
   * 新增数据
   *
   * @param QueryStat 实例对象
   * @return 实例对象
   */
  QueryStat insert(QueryStat QueryStat);

  long deleteByDueTimeLessThanCreatedAt(Date dueTime);

  List<QueryStat> query(QueryStatDTO queryStatDTO);

  Set<String> queryGtId(Long id, Integer typeCode);
}
