package com.bestpay.bigdata.bi.database.api.impl.dataset;

import cn.hutool.core.collection.CollUtil;
import com.bestpay.bigdata.bi.common.dto.dataset.DatasetParamQueryDTO;
import com.bestpay.bigdata.bi.database.api.dataset.DatasetParamDAOService;
import com.bestpay.bigdata.bi.database.dao.dataset.DatasetParamDo;
import com.bestpay.bigdata.bi.database.mapper.dataset.DatasetParamMapper;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 */
@Component
public class DatasetParamDAOServiceImpl implements DatasetParamDAOService {

    @Resource
    private DatasetParamMapper paramMapper;

    @Override
    public List<DatasetParamDo> query(DatasetParamQueryDTO queryDTO) {
        return paramMapper.query(queryDTO);
    }

    @Override
    public int batchInsert(List<DatasetParamDo> list) {
        if(CollUtil.isEmpty(list)){
            return 0;
        }
        return paramMapper.batchInsert(list);
    }

    @Override
    public int update(DatasetParamDo paramDo) {
        return paramMapper.update(paramDo);
    }

    @Override
    public int updateById(DatasetParamDo paramDo) {
        return paramMapper.updateById(paramDo);
    }
}
