package com.bestpay.bigdata.bi.database.mapper.common;

import com.bestpay.bigdata.bi.database.bean.QueryStat;
import com.bestpay.bigdata.bi.database.bean.QueryStatDTO;
import java.util.Date;
import java.util.List;
import java.util.Set;

import org.apache.ibatis.annotations.Param;

/**
 * 查询使用统计(QueryStat)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-01-18 10:32:36
 */
public interface QueryStatMapper {

  /**
   * 新增数据
   *
   * @param queryStat 实例对象
   * @return 影响行数
   */
  int insert(QueryStat queryStat);


  /**
   * 删除截至时间之前的数据
   * @param dueTime 截至时间
   * @return 影响行数
   */
  int deleteByDueTimeLessThanCreatedAt(@Param("dueTime") Date dueTime);


  /**
   * 查询list
   * @param queryStatDTO
   * @return list
   */
  List<QueryStat> query(QueryStatDTO queryStatDTO);


  /**
   * 查询大于id和类型查询
   * @param id 大于
   * @param typeCode 类型
   * @return QueryStat
   */
  Set<String> queryGtId(@Param("id") Long id, @Param("typeCode") Integer typeCode);

}

