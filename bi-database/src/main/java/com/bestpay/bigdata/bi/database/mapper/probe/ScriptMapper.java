package com.bestpay.bigdata.bi.database.mapper.probe;

import com.bestpay.bigdata.bi.database.bean.ProbeScriptDo;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @create 2022-03-16-14:51
 */
public interface ScriptMapper {

    ProbeScriptDo selectOneRecordByUserNameAndScriptName(ProbeScriptDo probeScriptDo);

    int selectCountRecordByUserName(@Param("userName")String userName);

    int insertProbeScriptRecord(ProbeScriptDo probeScriptDo);


    ProbeScriptDo selectOneRecordById(Long id);


    List<ProbeScriptDo> selectListRecordByUserName(@Param("userName")String userName);

    int updateScriptNameById(@Param("id") Long id, @Param("newScriptName") String newScriptName,
                             @Param("userName") String userName, @Param("updateDate")Date updateDate, @Param("dirId") Long dirId);

    int updateScriptById(@Param("id") Long id,@Param("newScript") String newScript,@Param("updateDate")Date updateDate);

    int deleteScriptById(@Param("id") Long id,@Param("userName") String userName,@Param("updateDate") Date updateDate);

    Long getMaxDirSort(@Param("userName") String userName);


    List<ProbeScriptDo> getListByDirId(@Param("directoryId") Long directoryId);

    void updateById(ProbeScriptDo probeScriptDo);

    List<ProbeScriptDo> getAllList();
}
