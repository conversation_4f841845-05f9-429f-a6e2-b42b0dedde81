package com.bestpay.bigdata.bi.report.request.dashboard;

import com.bestpay.bigdata.bi.common.dto.dashboard.CardInfoDTO;
import com.bestpay.bigdata.bi.report.request.tablecard.TableCardDataRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author:tangye
 * @description:
 * @createTime:2025/6/6 13:42
 * @version:1.0
 */
@Data
@ApiModel(description = "仪表盘AI解读报告请求")
public class DashboardAnalysisByAIRequest {

    @ApiModelProperty(value = "卡片信息")
    private CardInfoDTO cardInfo;

    @ApiModelProperty(value = "参数")
    private TableCardDataRequest paramsInfo;

    @ApiModelProperty(value = "卡片数据")
    private DashboardReturnDataRequest returnData;

}
