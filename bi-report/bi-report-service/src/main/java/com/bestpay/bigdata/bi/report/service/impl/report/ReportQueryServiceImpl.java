package com.bestpay.bigdata.bi.report.service.impl.report;

import static com.bestpay.bigdata.bi.common.common.Constant.LIMIT_SQL_TEMPLATE;
import static com.bestpay.bigdata.bi.common.constant.BIConstant.OBJECT_TYPE_REPORT;
import static com.bestpay.bigdata.bi.common.enums.CodeEnum.ADD_FAVORITES_FAIL;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.bestpay.bigdata.bi.backend.api.QueryService;
import com.bestpay.bigdata.bi.common.bean.AiPlusUserSearchRequest;
import com.bestpay.bigdata.bi.common.bean.MoveRequest;
import com.bestpay.bigdata.bi.common.common.Constant;
import com.bestpay.bigdata.bi.common.dto.dashboard.DashBoardRelatedDTO;
import com.bestpay.bigdata.bi.common.dto.dashboard.ReportCardQueryDTO;
import com.bestpay.bigdata.bi.common.dto.dataset.DatasetInfo;
import com.bestpay.bigdata.bi.common.dto.dataset.DatasetQueryDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.SensitiveComponentDTO;
import com.bestpay.bigdata.bi.common.entity.ExtendQueryContext;
import com.bestpay.bigdata.bi.common.entity.QueryContext;
import com.bestpay.bigdata.bi.common.entity.ResultBlockMetadata;
import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.common.enums.CodeEnum;
import com.bestpay.bigdata.bi.common.enums.FileType;
import com.bestpay.bigdata.bi.common.enums.OperateType;
import com.bestpay.bigdata.bi.common.enums.QueryType;
import com.bestpay.bigdata.bi.common.enums.RequestSystem;
import com.bestpay.bigdata.bi.common.enums.Status;
import com.bestpay.bigdata.bi.common.enums.StatusCodeEnum;
import com.bestpay.bigdata.bi.common.error.ReportErrorCode;
import com.bestpay.bigdata.bi.common.error.ScheduleErrorCode;
import com.bestpay.bigdata.bi.common.exception.BiException;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import com.bestpay.bigdata.bi.common.execution.DataManage;
import com.bestpay.bigdata.bi.common.oss.OssServiceFactory;
import com.bestpay.bigdata.bi.common.response.ReportDetailVO;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.common.util.DownloadFileUtil;
import com.bestpay.bigdata.bi.common.util.StringUtil;
import com.bestpay.bigdata.bi.common.util.UserContextUtil;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardReportCardService;
import com.bestpay.bigdata.bi.database.api.dataset.DatasetDAOService;
import com.bestpay.bigdata.bi.database.api.report.CollectionService;
import com.bestpay.bigdata.bi.database.api.report.ReportDirectoryDAOService;
import com.bestpay.bigdata.bi.database.api.report.component.NewReportService;
import com.bestpay.bigdata.bi.database.bean.AuthOperateEnum;
import com.bestpay.bigdata.bi.database.bean.AuthResourceTypeEnum;
import com.bestpay.bigdata.bi.database.bean.Collection;
import com.bestpay.bigdata.bi.database.bean.CollectionResponse;
import com.bestpay.bigdata.bi.database.bean.report.Report;
import com.bestpay.bigdata.bi.database.bean.report.ReportQueryDTO;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardReportCardDO;
import com.bestpay.bigdata.bi.database.dao.dataset.DatasetDo;
import com.bestpay.bigdata.bi.database.dao.report.NewReportDO;
import com.bestpay.bigdata.bi.database.dao.report.ReportDirectoryDo;
import com.bestpay.bigdata.bi.database.mapper.report.NewReportMapper;
import com.bestpay.bigdata.bi.report.bean.report.QueryReportMetaInfo;
import com.bestpay.bigdata.bi.report.bean.report.ReportDownloadResultDTO;
import com.bestpay.bigdata.bi.report.bean.report.ReportInfoVo;
import com.bestpay.bigdata.bi.report.beforeSQL.BeforeSQLService;
import com.bestpay.bigdata.bi.report.beforeSQL.QueryParamHandlerService;
import com.bestpay.bigdata.bi.report.chart.handler.util.ReportOrderTypeEnum;
import com.bestpay.bigdata.bi.report.download.DownloadId;
import com.bestpay.bigdata.bi.report.download.DownloadManager;
import com.bestpay.bigdata.bi.report.download.bean.DownloadResult;
import com.bestpay.bigdata.bi.report.download.bean.ReportDownloadContext;
import com.bestpay.bigdata.bi.report.download.job.DownloadJob;
import com.bestpay.bigdata.bi.report.download.job.ReportDownloadJob;
import com.bestpay.bigdata.bi.report.enums.dataset.DataSetResourceEnums;
import com.bestpay.bigdata.bi.report.enums.dataset.DatasetStatusEnum;
import com.bestpay.bigdata.bi.report.request.auth.ObjectAuthRequest;
import com.bestpay.bigdata.bi.report.request.report.DownloadApplyRequest;
import com.bestpay.bigdata.bi.report.request.report.DownloadColumnRequest;
import com.bestpay.bigdata.bi.report.request.report.MoveReportRequest;
import com.bestpay.bigdata.bi.report.request.report.QueryReportRequest;
import com.bestpay.bigdata.bi.report.request.report.ReportMarketRequest;
import com.bestpay.bigdata.bi.report.request.report.ReportRequest;
import com.bestpay.bigdata.bi.report.response.common.QueryIndexAndReportResponse;
import com.bestpay.bigdata.bi.report.response.report.ReportDownloadVO;
import com.bestpay.bigdata.bi.report.response.report.ReportMarket;
import com.bestpay.bigdata.bi.report.response.report.TopKReportInfoResponse;
import com.bestpay.bigdata.bi.report.service.auth.ObjectAuthService;
import com.bestpay.bigdata.bi.report.service.common.EngineDataQueryService;
import com.bestpay.bigdata.bi.report.service.dataset.DatasetService;
import com.bestpay.bigdata.bi.report.service.report.AccessStatisticsService;
import com.bestpay.bigdata.bi.report.service.report.ReportProcessService;
import com.bestpay.bigdata.bi.report.service.report.ReportQueryService;
import com.bestpay.bigdata.bi.report.service.report.ReportUpdateService;
import com.bestpay.bigdata.bi.report.util.AuthorityCheckUtil;
import com.bestpay.bigdata.bi.report.util.EngineQueryParamCheckUtil;
import com.bestpay.bigdata.bi.report.util.FileUploadUtils;
import com.bestpay.bigdata.bi.report.util.FileUploadUtils.MiniIOUpLoadResult;
import com.bestpay.bigdata.bi.thirdparty.api.AiPlusUserService;
import com.bestpay.drip.oss.proxy.common.Constants;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.io.File;
import java.io.IOException;
import java.io.Serializable;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * 报表查询服务
 *
 * <AUTHOR>
 * @date 2022/04/28
 */
@Service
@Slf4j
@Data
@NoArgsConstructor
@Component
public class ReportQueryServiceImpl implements ReportQueryService, Serializable {
  @Resource
  private CollectionService collectionService;
  @Reference
  private QueryService queryService;
  @Resource
  private ReportDirectoryDAOService directoryDAOService;
  @Resource
  private DatasetDAOService datasetDAOService;
  @Resource
  private EngineQueryParamCheckUtil engineQueryParamCheckUtil;
  @Resource
  private BeforeSQLService beforeSQLService;
  @Resource
  private DownloadManager downloadManager;
  @Resource
  private DataManage dataManage;
  @Resource
  private AiPlusUserService aiPlusUserService;
  @Resource
  private QueryParamHandlerService queryParamHandlerService;
  @Resource
  private ReportUpdateService updateService;
  @Resource
  private AccessStatisticsService statisticsService;
  @Resource
  private NewReportService newReportService;
  @Resource
  private ReportUpdateService reportService;
  @Resource
  private ObjectAuthService objectAuthService;
  @Resource
  private AuthorityCheckUtil authorityCheckUtil;
  @Resource
  private DashboardReportCardService reportCardService;
  @Resource
  private DatasetService datasetService;
  @Resource
  private EngineDataQueryService engineDataQueryService;
  @Resource
  private ReportProcessService reportProcessService;
  @Resource
  private OssServiceFactory ossServiceFactory;
  @Resource
  private NewReportMapper newReportMapper;

  private static final String REPORT_TOTAL_DATE_DELIMITER = "&&";
  private static final String FILE_ETX_NAME = "zip";
  private ReportDownloadJob.ReportDownloadJobFactory reportDownloadJobFactory
      = new ReportDownloadJob.ReportDownloadJobFactory();

  /**
   * 获取报表详情
   * @return
   */
  @Override
  public Response<ReportInfoVo> getReportInfo(Long id)
  {
    ReportInfoVo reportInfoVo = new ReportInfoVo();

    Report report = reportService.queryById(id);
    ReportDirectoryDo directoryDo = directoryDAOService.queryDbById(report.getDirId());

    // 获取报表访问量 - mobile and pc
    Map<Long, Long> accessCountMap = statisticsService.countByType(OBJECT_TYPE_REPORT,
            Lists.newArrayList(report.getId()), null);

    reportInfoVo.setReportName(report.getReportName());
    reportInfoVo.setReportId(id);
    reportInfoVo.setOrgCode(report.getOrgCode());
    reportInfoVo.setOrgAuth(report.getOrgAuth());

    reportInfoVo.setDirName(directoryDo.getName());
    reportInfoVo.setOwnerName(report.getOwnerName());
    reportInfoVo.setCreatedAt(DateUtil.format(report.getCreatedAt(), "yyyy-MM-dd HH:mm:ss"));
    reportInfoVo.setAccessCount(accessCountMap.get(id));

    return Response.ok(reportInfoVo);
  }


  @Override
  public ReportDownloadVO download(DownloadApplyRequest reportRequest) throws IOException {

    // 1、校验请求参数
    engineQueryParamCheckUtil.check(reportRequest);

    reportRequest.setIsAll(true);
    Long reportId = reportRequest.getReportId();
    String fileIdentifier = "";
    if (StringUtils.isBlank(reportRequest.getType())) {
      throw new BiException(ScheduleErrorCode.INTERVAL_ERROR, "参数传递异常: type is empty");
    }
    if (reportRequest.getType().equalsIgnoreCase("report")) {
      Report report = reportService.queryById(reportId);
      fileIdentifier = report.getReportName();
      reportRequest.setFileContainSensitiveInfo(report.getFileContainSensitiveInfo());
      reportRequest.setSensitiveFields(report.getSensitiveFields());
    } else if (reportRequest.getType().equalsIgnoreCase("dashboard")) {
      ReportCardQueryDTO cardQueryDTO = new ReportCardQueryDTO();
      cardQueryDTO.setIdList(Lists.newArrayList(reportId));
      List<DashboardReportCardDO> reportCardDOS = reportCardService.find(cardQueryDTO);
      DashboardReportCardDO reportCardDO = reportCardDOS.get(0);
      fileIdentifier = reportCardDO.getReportName();
      reportRequest.setFileContainSensitiveInfo(reportCardDO.getFileContainSensitiveInfo());
      reportRequest.setSensitiveFields(reportCardDO.getSensitiveFields());
    }

    // 2、提交 download manager
    UserInfo userInfo = UserContextUtil.getUserInfo();
    ReportDownloadContext downloadContext = new ReportDownloadContext();
    downloadContext.setDownloadApplyRequest(reportRequest);
    //downloadContext.setCookieValue(userInfo.getCookieId());
    downloadContext.setUserInfo(userInfo);
    downloadContext.setRequestDownloadCenter(false);

    downloadContext.setRequestSystem(RequestSystem.BI.getName());
    downloadContext.setUsername(userInfo.getEmail());
    downloadContext.setUserOrg(userInfo.getOrg().getName());
    downloadContext.setFileIdentifier(fileIdentifier);
    downloadContext.setFileType(FileType.EXCEL);
    downloadContext.setTypeCode(QueryType.QUERY_REPORT.getCode());
    downloadContext.setOssServiceFactory(ossServiceFactory);

    DownloadColumnRequest downloadColumnRequest = new DownloadColumnRequest();
    downloadColumnRequest.setId(reportRequest.getReportId());
    downloadColumnRequest.setType("report");
//    reportRequest.setIsAll(false);
    reportRequest.setChooseColumnList(Lists.newArrayList());

    DownloadJob downloadJob = reportDownloadJobFactory.createDownloadJob(downloadContext);
    DownloadResult downloadResult = downloadManager.syncSubmit(downloadJob, downloadContext);

    // 3、获取报表数据
    String localFilePath = downloadResult.getLocalFilePath();
    Map<String, String> map = DownloadFileUtil.getDirectoryAndFileName(localFilePath);

    File file = null;
    try {
      ReportDownloadResultDTO resultDTO = ReportDownloadResultDTO.builder()
          .fileName(map.get("fileName"))
          .fileLocalPath(localFilePath).build();

      resultDTO.setExtName(FILE_ETX_NAME);

      log.info("报表下载, 获取报表数据成功");

      // 4、上传文件系统(保存1天)
      file = new File(resultDTO.getFileLocalPath());

      MiniIOUpLoadResult result = FileUploadUtils.uploadFile(ossServiceFactory,
          resultDTO.getFileName(),
          Files.newInputStream(file.toPath()),
          1,
          Constants.ACCESSED_BY_UNSIGNED_URL);

      ReportDownloadVO reportDownloadVO = new ReportDownloadVO();
      reportDownloadVO.setUrl(result.getUrl());
      reportDownloadVO.setPath(result.getPath());
      reportDownloadVO.setFileName(fileIdentifier);
      return reportDownloadVO;
    }catch (Exception e){
      log.error("下载失败" , e);
      throw e;
    } finally {
      if(file!=null){
        file.delete();
      }
    }
  }

  @Transactional(rollbackFor = Throwable.class)
  @Override
  public Response<Long> createCollection(Collection collection) {
    // 权限校验
    ObjectAuthRequest request = new ObjectAuthRequest();
    request.setAuthResourceId(collection.getReportId()+"");
    request.setAuthResourceType(AuthResourceTypeEnum.report.name());

    // 权限校验
    authorityCheckUtil.checkOwnerAndAuth(request);

    UserInfo userInfo = UserContextUtil.getUserInfo();
    collection.setUserName(userInfo.getEmail());
    collection.setCreatedAt(new Date());
    collection.setCreatedBy(userInfo.getEmail());
    collection.setUpdatedAt(new Date());
    collection.setUpdatedBy(userInfo.getEmail());
    log.info(JSONUtil.toJsonStr(collection));
    try {
      Long result = collectionService.insert(collection);
      return Response.ok(result);
    }catch (Exception e){
      log.error("createCollection fail", e);
      return Response.error(ADD_FAVORITES_FAIL);
    }
  }

  @Override
  public Response<Integer> cancelCollection(Collection collection) {
    int result = collectionService.deleteById(collection.getId());
    return Response.ok(result);
  }

  @Override
  public Response<List<ReportMarket>> getCollectionList(Collection collection) {
    UserInfo userInfo = UserContextUtil.getUserInfo();

    // 找到最大收藏id：判断是否有收藏
    Map<Long,Long> reportIdMaxCollectionIdMap = getMaxCollectionIdMap();

    log.info("collectReportIdSet : {}", reportIdMaxCollectionIdMap);
    // 目录
    List<ReportDirectoryDo> directoryDoList = directoryDAOService.queryReportDirectoryList(ReportDirectoryDo.builder().build());
    HashMap<Long, List<ReportDirectoryDo>> parentIdDirectoryMap  = new HashMap<>();
    List<ReportDirectoryDo> rootDirectoryList = new ArrayList<>();

    // 区分父子目录
    for (ReportDirectoryDo directoryDo : directoryDoList) {
      Long parentId = directoryDo.getParentId();

      if (Objects.isNull(parentId)) {
        // 主目录
        rootDirectoryList.add(directoryDo);
      } else {
        // 子目录
        List<ReportDirectoryDo> childDirectoryList = parentIdDirectoryMap.getOrDefault(parentId, new ArrayList<>());
        childDirectoryList.add(directoryDo);
        childDirectoryList = childDirectoryList.stream()
                .sorted(Comparator.comparing(ReportDirectoryDo::getDirSort))
                .collect(Collectors.toList());
        parentIdDirectoryMap.put(parentId, childDirectoryList);
      }
    }

    rootDirectoryList = rootDirectoryList.stream()
            .sorted(Comparator.comparing(ReportDirectoryDo::getDirSort))
            .collect(Collectors.toList());

    // 查询所有报表 - 按条件
    ReportQueryDTO reportQueryDTO = new ReportQueryDTO();
    List<NewReportDO> reports = newReportService.queryAll(reportQueryDTO);

    Map<String,String> resources = objectAuthService.getAuthResourceList();

    // for collection don't be affected for orgAuth, so we set isManager is true and userOrgCode = null and orgCode = null
    List<ReportMarket> reportMarkets = generateReportTreeList(
        null,
        null,
        UserContextUtil.getUserInfo().getIsManager(),
        userInfo,
        rootDirectoryList,
        parentIdDirectoryMap,
        reportIdMaxCollectionIdMap,
        reports,
        resources,
        true,
            true);

    return Response.ok(reportMarkets);
  }

  @Override
  public Response<Boolean> moveReportLocation(MoveReportRequest moveReportRequest)
  {
    //check param
    List<MoveRequest> postMoveSortList = moveReportRequest.getPostMoveSortList();

    if (CollectionUtils.isEmpty(postMoveSortList)) {
      return Response.error(CodeEnum.SCRIPT_MOVE_PARAM_ERROR);
    }

    log.info("move Report Location, postMoveSortList : {}", postMoveSortList);
    // move
    for (int i = 0; i < postMoveSortList.size(); i++) {

      NewReportDO report = new NewReportDO();
      report.setId(postMoveSortList.get(i).getId());
      report.setOrderNum(postMoveSortList.get(i).getOrderNum());
      report.setDirId(postMoveSortList.get(i).getDirId());
      report.setUpdatedBy(UserContextUtil.getUserInfo().getEmail());
      report.setUpdatedAt(new Date());
      newReportService.updateReport(report);

    }
    return Response.ok(true);
  }

  @Override
  public List<ReportMarket> getReportMarket(ReportMarketRequest reportMarketRequest) {
    // 找到最大收藏id：判断是否有收藏
    Map<Long, Long> reportIdMaxCollectionIdMap = getMaxCollectionIdMap();
    log.info("collectReportIdSet : {}", reportIdMaxCollectionIdMap);

    // 查询根目录
    List<ReportDirectoryDo> directoryDoList = directoryDAOService
        .queryReportDirectoryList(ReportDirectoryDo.builder().build());

    HashMap<Long, List<ReportDirectoryDo>> parentIdDirectoryMap  = new HashMap<>();
    List<ReportDirectoryDo> rootDirectoryList = new ArrayList<>();

    for (ReportDirectoryDo directoryDo : directoryDoList) {
      Long parentId = directoryDo.getParentId();

      if (Objects.isNull(parentId)) {
        // 根目录
        rootDirectoryList.add(directoryDo);
      } else {
        // 子目录
        List<ReportDirectoryDo> childDirectoryList = parentIdDirectoryMap.getOrDefault(parentId, new ArrayList<>());
        childDirectoryList.add(directoryDo);
        childDirectoryList = childDirectoryList.stream()
                .sorted(Comparator.comparing(ReportDirectoryDo::getDirSort))
                .collect(Collectors.toList());
        parentIdDirectoryMap.put(parentId, childDirectoryList);
      }
    }

    rootDirectoryList = rootDirectoryList.stream()
        .sorted(Comparator.comparing(ReportDirectoryDo::getDirSort))
        .collect(Collectors.toList());

    // 查询所有报表 - 按条件
    ReportQueryDTO reportQueryDTO = new ReportQueryDTO();
    reportQueryDTO.setOrgCode(reportMarketRequest.getOrgCode());
    reportQueryDTO.setReportNameKeyword(reportMarketRequest.getKeyword());
    List<NewReportDO> reports = newReportService.queryAll(reportQueryDTO);

    // 查询授权信息
    Map<String,String> resources = objectAuthService.getAuthResourceList();

    // 查询子目录的子目录
    return generateReportTreeList(reportMarketRequest.getOrgCode(),
        reportMarketRequest.getKeyword(),
        UserContextUtil.getUserInfo().getIsManager(),
        UserContextUtil.getUserInfo(),
        rootDirectoryList,
        parentIdDirectoryMap,
        reportIdMaxCollectionIdMap,
        reports,
        resources,
        true,
            false);
  }

  private Map<Long, Long> getMaxCollectionIdMap() {
    UserInfo userInfo = UserContextUtil.getUserInfo();
    Collection collection = new Collection();
    collection.setUserName(userInfo.getEmail());
    List<CollectionResponse> result = collectionService.queryCollectionList(collection);

    // 找到最大收藏id
    Map<Long,Long> reportIdMaxCollectionIdMap = Maps.newHashMap();
    result.stream().forEach(p->{
      if(reportIdMaxCollectionIdMap.get(p.getReportId())==null){
        reportIdMaxCollectionIdMap.put(p.getReportId(), p.getId());
      }else {
        if(reportIdMaxCollectionIdMap.get(p.getReportId().compareTo(p.getId()))>0){
          reportIdMaxCollectionIdMap.put(p.getReportId(), p.getId());
        }
      }
    });
    return reportIdMaxCollectionIdMap;
  }

  /**
   *
   * @param orgCode current user orgCode
   * @param isManager is aiplus manager
   * @param userInfo userInfo
   * @param childDirectoryList rootDirectory list // TODO: 2024/2/7 取名随意，传入的是root
   * @param parentIdDirectoryMap rootId map to childList
   * @return
   */
  private List<ReportMarket> generateReportTreeList(
      String orgCode,
      String keyword,
      boolean isManager,
      UserInfo userInfo,
      List<ReportDirectoryDo> childDirectoryList,
      HashMap<Long, List<ReportDirectoryDo>> parentIdDirectoryMap,
      Map<Long,Long> reportIdMaxCollectionIdMap,
      List<NewReportDO> reports,
      Map<String,String> resources,
      boolean checkAuth,
      boolean isCollection) {

    List<ReportMarket> list = CollUtil.newArrayList();

    // TODO: 2024/2/7 结合页面实际的交互需求，是否需要对每一个文件夹下的子目录，以及目录下的报表全部查询出来？
      //  for循环中包含着递归、db交互、外部系统交互等，可想而知接口返回速度不会很快，需优化
      //  并且一次性返回大量的数据，后端处理逻辑复杂，并且前端需要做数据的交互逻辑处理，增加了前后端逻辑的复杂度
    for (ReportDirectoryDo rootDirectory : childDirectoryList) {

      List<ReportDirectoryDo> nextChildDirectoryList = parentIdDirectoryMap.get(rootDirectory.getId());
      List<ReportMarket> childTreeList = null;
      if (CollUtil.isNotEmpty(nextChildDirectoryList)) {

        // 继续查询子目录
        childTreeList = generateReportTreeList(orgCode, keyword,
            isManager,
            userInfo,
            nextChildDirectoryList,
            parentIdDirectoryMap,
            reportIdMaxCollectionIdMap,
            reports,
            resources,
            checkAuth,
            isCollection);
      }

      // 查询子目录下面的报表
      List<ReportMarket> children = getReportChildren(rootDirectory,
          userInfo.getEmail(),
          reportIdMaxCollectionIdMap,
          isManager,
          reports,
          resources,
          checkAuth,
          isCollection);

      if (CollUtil.isNotEmpty(childTreeList)) {
        childTreeList.addAll(children);
        children = new ArrayList<>(childTreeList);
      }

      if ((isManager || rootDirectory.getCreatedBy().equals(UserContextUtil.getUserInfo().getEmail()))
          || CollUtil.isNotEmpty(children)) {

        // use filter condition, and children is empty, we need skip it
        if ((StringUtil.isNotEmpty(orgCode) || StringUtil.isNotEmpty(keyword)) && CollUtil.isEmpty(children)) {
          continue;
        }

        // consider collection list
        if (isCollection && CollUtil.isEmpty(children)) {
          continue;
        }

        // TODO: 2024/2/7 后续和智加统一用户标识，避免循环中还需要和智加不停的交互
        UserInfo createdByInfo = getUserInfo(rootDirectory.getCreatedBy());
        ReportMarket reportMarket = ReportMarket.builder()
                .id(rootDirectory.getId().toString())
                .label(rootDirectory.getName())
                .type("directory")
                .directoryCreatedBy(Objects.nonNull(createdByInfo)? createdByInfo.getAccount(): null)
                .children(children).build();
        list.add(reportMarket);
      }
    }
    return list;
  }

  private UserInfo getUserInfo(String email){
    AiPlusUserSearchRequest request = new AiPlusUserSearchRequest();
    request.setOwnerNames(Lists.newArrayList(email));
    List<UserInfo> users = aiPlusUserService.getUserList(request);
    if(CollUtil.isNotEmpty(users)){
      return users.get(0);
    }
    return null;
  }

  /**
   * 获取报表列表
   * @param reportMarketRequest
   * @return
   */
  @Override
  public List<ReportMarket> getReportList(ReportMarketRequest reportMarketRequest)
  {
    // 查询所有报表 - 按条件
    ReportQueryDTO reportQueryDTO = new ReportQueryDTO();
    reportQueryDTO.setOrgCode(reportMarketRequest.getOrgCode());
    reportQueryDTO.setReportNameKeyword(reportMarketRequest.getKeyword());
    List<NewReportDO> reports = newReportService.queryAll(reportQueryDTO);

    // 查询授权信息
    Map<String,String> resources = objectAuthService.getAuthResourceList();

    return getReportChildren(
        null,
        UserContextUtil.getUserInfo().getEmail(),
        null,
        UserContextUtil.getUserInfo().getIsManager(),
        reports,
        resources,
        true,
        false);
  }

  @Override
  public Response<QueryIndexAndReportResponse> syncQueryReport(QueryReportRequest reportRequest) {

    // 参数校验
    engineQueryParamCheckUtil.check(reportRequest);

    ReportDetailVO detailVO = updateService
        .queryReportTemplate(reportRequest.getReportId()).getData();

    ReportRequest request = convertReportRequest(reportRequest, detailVO);

    // 报表默认排序 + 自定义排序
    if (CollUtil.isNotEmpty(reportRequest.getOrderColumns())) {
      request.setOrderColumnList(reportRequest.getOrderColumns()); // 自定义排序
    }

    // 关键字
    if (StringUtil.isNotEmpty(reportRequest.getKeyword())) {
      request.setQueryKeyword(reportRequest.getKeyword());
    }

    // 报表特殊排序 传默认值
    request.setOrderType(ReportOrderTypeEnum.DEFAULT.getType());

    // 只有仪表板可以分享
    DashBoardRelatedDTO dto = new DashBoardRelatedDTO();
    dto.setId(reportRequest.getReportId());
    dto.setType(AuthResourceTypeEnum.report.name());

    QueryIndexAndReportResponse reportResponse
        = engineDataQueryService.getData(request, Boolean.TRUE, dto).getData();

    reportResponse.setDescription(detailVO.getReportDesc());

    return Response.ok(reportResponse);
  }

  public ReportRequest convertReportRequest(QueryReportRequest reportRequest, ReportDetailVO detailVO) {
    ReportRequest report
        = JSONUtil.toBean(JSONUtil.toJsonStr(detailVO), ReportRequest.class);
    BeanUtils.copyProperties(reportRequest,report);
    return report;
  }

  private String getCountSql(String maxRows, String sql) {
    // 取出 limit 前面 SQL
    String[] sqlParts = sql.split(" limit ");
    String countSql = sqlParts[0];
    // 添加limit
    if(maxRows!=null && StringUtil.isNotEmpty(maxRows)) {
      countSql = String.format(LIMIT_SQL_TEMPLATE, countSql, maxRows);
    }
    // 添加count
    countSql = String.format(Constant.TOTAL_ROW_SQL, countSql);
    return countSql;
  }

  @Override
  public Response<QueryReportMetaInfo> queryReportMetaInfo(ExtendQueryContext queryContext, ReportRequest report) {
    // check data set is any normal from Index-Process
    DatasetInfo dataset = report.getDatasetInfoList().get(0);
    checkDataSetStatus(dataset.getDatasetId());

    ExtendQueryContext totalRowsQueryContext = new ExtendQueryContext();
    BeanUtils.copyProperties(queryContext, totalRowsQueryContext);

    // 生成计算数量SQL
    String sql = totalRowsQueryContext.getSql();
    String countSql = getCountSql(report.getMaxRows(), sql);

    totalRowsQueryContext.setSql(countSql);
    log.info("user:{} sync execute query count rows sql:{}", queryContext.getUsername(), countSql);

    QueryContext totalContext = queryService.syncExecuteQuery(totalRowsQueryContext);
    if (Status.QUERY_SUCCESS.equals(totalContext.getStatus())) {
      QueryReportMetaInfo metaInfo = QueryReportMetaInfo.builder().build();
      List<List<String>> totalResult = totalContext.getResults();
      if (!CollectionUtils.isEmpty(totalResult)) {
        List<String> list = totalResult.get(0);
        Long rowCount = Long.parseLong(list.get(0));
        metaInfo.setRowCount(rowCount);
      }
      return Response.ok(metaInfo);
    }

    log.warn("user:{} queryId:{} sync executeQuery failed. cause:{}", queryContext.getUsername(),
            totalContext.getQueryId(),
            totalContext.getMessage());

    throw new BiException(ReportErrorCode.REPORT_QUERY_FAIL,"报表查询失败，失败原因:" + totalContext.getMessage());
  }


  @Override
  public Response<QueryIndexAndReportResponse> downloadApply(DownloadApplyRequest reportRequest) {
    log.info("downloadApply start");
    QueryIndexAndReportResponse queryIndexAndReportResponse = QueryIndexAndReportResponse
        .builder()
        .status(Status.QUERY_SUCCESS.getCode())
        .build();

    UserInfo userInfo = UserContextUtil.getUserInfo();
    ReportDownloadContext downloadContext = new ReportDownloadContext();
    downloadContext.setDownloadApplyRequest(reportRequest);
    //downloadContext.setCookieValue(userInfo.getCookieId());
    downloadContext.setUserInfo(userInfo);
    downloadContext.setRequestDownloadCenter(true);

    downloadContext.setRequestSystem(RequestSystem.BI.getName());
    downloadContext.setUsername(userInfo.getEmail());
    downloadContext.setUserOrg(userInfo.getOrg().getName());
    downloadContext.setFileIdentifier(reportRequest.getName());
    downloadContext.setFileType(FileType.getFileType(reportRequest.getFileType()));
    downloadContext.setTypeCode(QueryType.QUERY_REPORT.getCode());
    downloadContext.setOssServiceFactory(ossServiceFactory);

    DownloadColumnRequest downloadColumnRequest = new DownloadColumnRequest();
    downloadColumnRequest.setId(reportRequest.getReportId());
    downloadColumnRequest.setType(reportRequest.getType());
//    reportRequest.setIsAll(false);

    // 权限校验
    if (reportRequest.getType().equalsIgnoreCase("report")) {
      // 报表
      ObjectAuthRequest request = new ObjectAuthRequest();
      request.setAuthResourceId(reportRequest.getReportId()+"");
      request.setAuthResourceType(AuthResourceTypeEnum.report.name());
      authorityCheckUtil.checkOwnerAndAuth(request);
    }else{
      // 仪表板报表卡片
      ReportCardQueryDTO cardQueryDTO = new ReportCardQueryDTO();
      cardQueryDTO.setIdList(Lists.newArrayList(reportRequest.getReportId()));
      List<DashboardReportCardDO> reportCardDOS = reportCardService.find(cardQueryDTO);
      DashboardReportCardDO reportCardDO = reportCardDOS.get(0);

      ObjectAuthRequest request = new ObjectAuthRequest();
      request.setAuthResourceId(reportCardDO.getDashboardId()+"");
      request.setAuthResourceType(AuthResourceTypeEnum.dashboard.name());
      authorityCheckUtil.checkOwnerAndAuth(request);
    }

    DownloadJob downloadJob = reportDownloadJobFactory.createDownloadJob(downloadContext);
    DownloadId downloadId = downloadManager.submit(downloadJob, downloadContext);
    queryIndexAndReportResponse.setDownloadId(downloadId.getId());
    return Response.ok(queryIndexAndReportResponse);

  }

  @Override
  public ExtendQueryContext generateQueryContext(ReportRequest report, UserInfo userInfo) {
    log.info("user:{} queryReport start to generate queryContext", userInfo.getAccount());

    ExtendQueryContext queryContext = new ExtendQueryContext();
    DatasetInfo dataset = report.getDatasetInfoList().get(0);
    log.info("report datasetInfo: {}", JSONUtil.toJsonStr(dataset));

    queryContext.setEngineName(dataset.getSqlEngine().getEngineName());
    queryContext.setClusterName(dataset.getDataSourceName());

    queryContext.setDataSource(dataset.getDataSourceName());
    queryContext.setTypeCode(QueryType.QUERY_REPORT.getCode());
    queryContext.setOperateTypeCode(OperateType.QUERY.getCode());
    queryContext.setSqlType(Constant.SQL_DQL_TYPE);
    queryContext.setUsername(userInfo.getEmail());
    queryContext.setUserOrg(userInfo.getOrg().getName());

    log.info("report fileContainSensitiveInfo: {}", report.getFileContainSensitiveInfo());

    queryContext.setFileContainSensitiveInfo(report.getFileContainSensitiveInfo());
    queryContext.setSensitiveFields(getSensitiveFields(report));

    log.info("user:{} queryReport generate queryContext finished. queryContext:{}",
            userInfo.getAccount(),
            JSONUtil.toJsonStr(queryContext));
    return queryContext;
  }

  /**
   * 执行下载的sql任务
   *
   * @param queryContext
   * @return
   */
  @Override
  public QueryContext executeDownloadQuery(ExtendQueryContext queryContext, FileType fileType)
  {
    queryContext.setOperateTypeCode(OperateType.DOWNLOAD.getCode());
    queryContext.setTypeCode(QueryType.QUERY_REPORT.getCode());
    QueryContext context = queryService.syncExecuteQuery(queryContext);

    if (context.getStatus().equals(Status.QUERY_FAILED)) {
      String errorMessage = StringUtils.isNoneEmpty(context.getMessage()) ? context.getMessage() : "下载过程异常,需要重新提交下载";
      throw new BiException(ReportErrorCode.REPORT_DOWNLOAD_EXCEPTION, errorMessage);
    }

    if (StringUtils.isNoneEmpty(context.getSizeOverflowColumn())
            && fileType.equals(FileType.EXCEL)) {
      throw new BiException(ReportErrorCode.REPORT_DOWNLOAD_EXCEPTION, "下载失败，存在数据长度超过excel单元格最大长度1048576, 推荐使用csv下载");
    }

    log.info(" finish executeDownloadQuery context : {}", context);
    return context;
  }

  /**
   * 根据 block metadata get data from backend
   *
   * @param blockMetadata
   * @return
   */
  @Override
  public List<List<String>> getDataBlockByMeta(ResultBlockMetadata blockMetadata)
  {
    return dataManage.readResultBlock(blockMetadata).getData();
  }


  private DatasetInfo getCurDatasetInfo(String jsonDatasetInfo) {
    List<DatasetInfo> datasetInfoList = new ArrayList<>();
    try {
      datasetInfoList = JSONUtil.parseArray(jsonDatasetInfo).toList(DatasetInfo.class);
    } catch (Exception e) {
      DatasetInfo datasetInfo = JSONUtil.toBean(jsonDatasetInfo, DatasetInfo.class);
      datasetInfoList.add(datasetInfo);
    }
    DatasetInfo dataset = new DatasetInfo();
    if (datasetInfoList != null && datasetInfoList.size() > 0) {
      dataset = datasetInfoList.get(0);
    }
    return dataset;
  }

  /**
   * 应前端要求将树形目录第一层级Id设为唯一 解决方案：children层id前都加上‘#’
   */
  private List<ReportMarket> getReportChildren(ReportDirectoryDo directory,
      String email,
      Map<Long,Long> reportIdMaxCollectionIdMap,
      boolean isManager,
      List<NewReportDO> allReports,
      Map<String,String> resources,
      boolean checkAuth,
      boolean isCollection) {

    List<NewReportDO> reports = null;
    if (directory != null) {
      reports = allReports.stream()
          .filter(p->p.getDirId().equals(directory.getId())).collect(Collectors.toList());
    }else{
      reports = allReports;
    }

    // 获取有权限的报表 --- 授权
    HashMap<Long, String> reportIdAuthMap = new HashMap<>();
    List<NewReportDO> authReports = getAuthReportsAndAuthTypeMap(resources,
        email,
        isManager,
        reports,
        reportIdAuthMap,
        checkAuth);

    // 继续查询子目录
    List<ReportMarket> children = CollUtil.newArrayList();
    if (CollUtil.isNotEmpty(authReports)) {
      for (NewReportDO report : authReports) {

        // 收藏场景，过滤非收藏报表 （filterReportIdSet）
        if (isCollection
            && Objects.nonNull(reportIdMaxCollectionIdMap)
            && !reportIdMaxCollectionIdMap.containsKey(report.getId())) {
          continue;
        }

        // 管理员、报表责任人 、被授予编辑权限 才可以看到被下线的报表
        if (StatusCodeEnum.OFFLINE.getCode() == report.getStatusCode()) {
          if (
                  isManager ||
                          report.getEmail().equals(email) ||
                          AuthOperateEnum.edit.name().equals(reportIdAuthMap.getOrDefault(report.getId(), AuthOperateEnum.no.name()))
          ) {
          } else {
            continue;
          }
        }

        String operate = isManager? AuthOperateEnum.edit.name()
            : reportIdAuthMap.get(report.getId());

        ReportMarket reportMarket = ReportMarket.builder()
            .id(report.getId().toString())
            .label(report.getReportName())
            .collectionId(
                Objects.nonNull(reportIdMaxCollectionIdMap)
                    && reportIdMaxCollectionIdMap.get(report.getId())!=null
                    ? reportIdMaxCollectionIdMap.get(report.getId()).toString()
                    : null)
            .statusCode(report.getStatusCode())
            .reportOwnerName(report.getOwnerName())
            .authOperate(
                StringUtils.isNotBlank(operate) ? operate : AuthOperateEnum.no.name())
            .type("report")
            .build();

        children.add(reportMarket);
      }
    }

    return children;
  }

  private List<NewReportDO> getAuthReportsAndAuthTypeMap( Map<String,String> resources,
      String email,
      boolean isManager,
      List<NewReportDO> reports,
      HashMap<Long, String> reportIdAuthMap,
      boolean checkAuth) {

    // 无需校验授权
    if(!checkAuth){
      return reports;
    }

    List<NewReportDO> newReportList = Lists.newArrayList();
    if (!isManager) {
      for (NewReportDO rp : reports) {
        if (rp.getEmail().equals(email)) {
          newReportList.add(rp);
          reportIdAuthMap.put(rp.getId(), AuthOperateEnum.edit.name());
        } else {
          ObjectAuthRequest authRequest = new ObjectAuthRequest();
          authRequest.setAuthResourceId(rp.getId().toString());
          authRequest.setAuthResourceType(AuthResourceTypeEnum.report.name());

          String resourceKey = rp.getId().toString()
              +"_"+AuthResourceTypeEnum.report.name();

          if(resources.containsKey(resourceKey)) {
            newReportList.add(rp);
            reportIdAuthMap.put(rp.getId(), resources.get(resourceKey));
          }
        }
      }
      reports = new ArrayList<>(newReportList);
    }

    return reports;
  }

  private void checkDataSetStatus(Long datasetId) {
    DatasetQueryDTO queryDTO = DatasetQueryDTO.builder().idList(Lists.newArrayList(datasetId)).build();

    List<DatasetDo> datasetDoList = datasetDAOService.query(queryDTO);
    if (CollectionUtil.isEmpty(datasetDoList)) {
      throw new BiException(ReportErrorCode.REPORT_REQUEST_INDEX_DATA_SET_ERROR,"数据集存在异常，请重新配置");
    }

    DatasetDo datasetDo = datasetDoList.get(0);
    if (!DatasetStatusEnum.ONLINE.getCode().equals(datasetDo.getStatusCode())) {
      throw new BiException(ReportErrorCode.REPORT_DATASET_STATUS_ERROR, String.format(CodeEnum.DATASET_STATUS_ERROR.code(), datasetDo.getName()));
    }
  }

  private String parseDataDate(String dataDate) {
    try {
      return DateUtil.parseDate(dataDate).toString("yyyy-MM-dd");
    } catch (Exception e) {
      log.error("parse dataDate:{} error", dataDate, e);
      return null;
    }
  }

  /**
   * Convert the object value of jdbc resultSet to string.
   *
   * @param obj the obj
   * @return the string
   */
  protected String formatValue(Object obj) {
    return StringUtil.formatValue(obj);
  }

  /**
   * 获取敏感字段中文名字符串
   * @param report
   * @return
   */
  private String getSensitiveFields(ReportRequest report) {
    log.info("report sensitiveFields: {}", report.getSensitiveFields());

    List<SensitiveComponentDTO> columnProperties
        = JSONUtil.toList(report.getSensitiveFields(), SensitiveComponentDTO.class);
    StrBuilder strBuilder = StrBuilder.create();
    for (SensitiveComponentDTO columnProperty : columnProperties) {
      strBuilder.append(columnProperty.getEnName()).append(StrUtil.COMMA);
    }
    return StrUtil.removeSuffix(strBuilder.toString(),StrUtil.COMMA);
  }

  @Override
  public Response<List<TopKReportInfoResponse>> topk(List<String> orgCodeList) {
    List<TopKReportInfoResponse> topKReportInfoResponses = newReportMapper.topk(orgCodeList).stream().map(report -> {
      TopKReportInfoResponse response = new TopKReportInfoResponse();
      response.setId(report.getId());
      response.setName(report.getReportName());
      response.setType(DataSetResourceEnums.REPORT.getCode());
      return response;
    }).collect(Collectors.toList());
    return Response.ok(topKReportInfoResponses);
  }
}
