package com.bestpay.bigdata.bi.report.sql;

import static com.bestpay.bigdata.bi.common.common.Constant.AND_RELATION;
import static com.bestpay.bigdata.bi.common.common.Constant.PAGE_QUERY_UNLIMITED;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.StrUtil;
import com.bestpay.bigdata.bi.common.enums.FieldType;
import com.bestpay.bigdata.bi.common.enums.SQLEngine;
import com.bestpay.bigdata.bi.common.enums.ScopeFilterTypeEnum;
import com.bestpay.bigdata.bi.common.error.DatasetErrorCode;
import com.bestpay.bigdata.bi.common.exception.BiException;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import com.bestpay.bigdata.bi.report.sql.bean.ReportConditionInfo;
import com.google.common.annotations.VisibleForTesting;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @create 2023-04-06-19:20
 */
@Slf4j
public class ReportSqlGenerateTool {


    /**
     * generate part sql by conditionInfos
     * @param conditionInfos condition infos
     * @return part sql
     */
    @VisibleForTesting
    public static String getFilterConditionSql(List<ReportConditionInfo> conditionInfos, SQLEngine sqlEngine) {
        String toStringFormat = "";
        if (SQLEngine.CLICKHOUSE.equals(sqlEngine)) {
            toStringFormat = " toString( %s ) ";
        } else if(SQLEngine.MYSQL.equals(sqlEngine)) {
            toStringFormat = " CAST(%s AS CHAR) ";
        } else if (SQLEngine.OCEANBASE.equals(sqlEngine)) {
            toStringFormat = " CAST(%s AS CHAR) ";
        } else if (SQLEngine.HIVE.equals(sqlEngine)) {
            toStringFormat = " CAST(%s AS CHAR) ";
        } else {
            toStringFormat = " %s ";
        }

        if (CollUtil.isEmpty(conditionInfos)) {
            return StrUtil.EMPTY;
        }

        conditionInfos = conditionInfos.stream().filter((x) -> {
            if (Objects.equals(ScopeFilterTypeEnum.IS_NULL.getCode(), x.getScopeFilterType())
                    || Objects.equals(ScopeFilterTypeEnum.IS_NOT_NULL.getCode(), x.getScopeFilterType())) {
                return true;
            } else if (Objects.isNull(x.getStringValue()) && CollUtil.isEmpty(x.getValues())) {
                return false;
            }
            return true;
        }).collect(Collectors.toList());

        if (CollUtil.isEmpty(conditionInfos)) {
            return StrUtil.EMPTY;
        }

        // process keyword
        StringBuilder sbFilter = new StringBuilder();
        List<ReportConditionInfo> keywordConditionList = conditionInfos.stream()
                .filter(conditionInfo -> ScopeFilterTypeEnum.LIKE.getCode().equals(conditionInfo.getScopeFilterType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(keywordConditionList)) {
            ArrayList<String> list = new ArrayList<>();
            sbFilter.append(" ( ");
            for (ReportConditionInfo reportConditionInfo : keywordConditionList) {
                String fieldName = reportConditionInfo.getEnName();
                String stringValue = "'" + reportConditionInfo.getStringValue() + "'";
                String scopeFilterType = reportConditionInfo.getScopeFilterType();
                list.add(StrUtil.format(ScopeFilterTypeEnum.getName(scopeFilterType), String.format(toStringFormat, fieldName), stringValue));
            }
            sbFilter.append(String.join(" OR ", list)).append(" ) ").append(AND_RELATION);
        }

        List<ReportConditionInfo> otherConditionList = conditionInfos.stream()
                .filter(conditionInfo -> !ScopeFilterTypeEnum.LIKE.getCode().equals(conditionInfo.getScopeFilterType()))
                .collect(Collectors.toList());
        // process others
        for (ReportConditionInfo filterCondition : otherConditionList) {
            String fieldName = filterCondition.getEnName();
            String scopeFilterType = filterCondition.getScopeFilterType();
            String fieldType = filterCondition.getShowTypeName();
            String stringValue = filterCondition.getStringValue();
            List<Object> values = filterCondition.getValues();

            if (StrUtil.containsIgnoreCase(fieldType,"CHARACTER")) {
                fieldType = FieldType.CHARACTER.name();
            }
            switch (FieldType.valueOf(fieldType)) {
                case CHARACTER:
                    String[] filterArr = StrUtil.splitToArray(stringValue, CharUtil.COMMA);
                    if (ArrayUtil.isNotEmpty(filterArr)) {
                        String[] wrapAll = StrUtil.wrapAll("'", "'", filterArr);
                        stringValue = StrUtil.join(StrUtil.COMMA, wrapAll);
                        sbFilter.append(StrUtil.format(ScopeFilterTypeEnum.getName(scopeFilterType), String.format(toStringFormat, fieldName), stringValue))
                                .append(AND_RELATION);
                    } else if (CollectionUtils.isNotEmpty(values)) {
                        String[] strings = new String[values.size()];
                        for (int i = 0; i < values.size(); i++) {
                            strings[i] = (String) values.get(i);
                        }
                        String[] wrapAll = StrUtil.wrapAll("'", "'", strings);
                        String str = StrUtil.join(StrUtil.COMMA, wrapAll);
                        sbFilter.append(StrUtil.format(ScopeFilterTypeEnum.getName(scopeFilterType), String.format(toStringFormat, fieldName), str))
                                .append(AND_RELATION);
                    } else {
                        if (Objects.equals(ScopeFilterTypeEnum.IS_NULL.getCode(), scopeFilterType)
                                || Objects.equals(ScopeFilterTypeEnum.IS_NOT_NULL.getCode(), scopeFilterType)) {
                            sbFilter.append(StrUtil.format(ScopeFilterTypeEnum.getName(scopeFilterType), String.format(toStringFormat, fieldName)))
                                    .append(AND_RELATION);
                        }
                    }
                    break;
                case DECIMAL:
                case DATETIME:
                    generateFilterCondition(sbFilter, filterCondition, toStringFormat);
                    break;
                default:
                    throw new BiException(DatasetErrorCode.SHOW_TYPE_NAME_ERROR,"the fieldType[" + fieldType + "] doesn't supported");
            }
        }
        if (StrUtil.isNotEmpty(sbFilter)) {
            return StrUtil.removeSuffix(sbFilter, AND_RELATION);
        }
        return StrUtil.EMPTY;
    }


    private static void generateFilterCondition(StringBuilder filter, ReportConditionInfo filterCondition, String toStringFormat) {
        String fieldName = filterCondition.getEnName();
        String scopeFilterType = filterCondition.getScopeFilterType();
        List<Object> values = filterCondition.getValues();
        String stringValue = filterCondition.getStringValue();

        String[] filterArr = StrUtil.splitToArray(stringValue, CharUtil.COMMA);
        if (ArrayUtil.isNotEmpty(filterArr)) {
            String[] wrapAll = StrUtil.wrapAll("'", "'", filterArr);
            stringValue = StrUtil.join(StrUtil.COMMA, wrapAll);
        }
        if (Objects.equals(ScopeFilterTypeEnum.INTERVAL.getCode(), scopeFilterType)
            && CollUtil.isNotEmpty(values)) {
            if (filterCondition.isValueType()) {
                filter.append(StrUtil.format(ScopeFilterTypeEnum.getName(scopeFilterType),
                        fieldName, values.get(0), values.get(1))).append(AND_RELATION);
            } else {
                filter.append(StrUtil.format(ScopeFilterTypeEnum.getName(scopeFilterType),
                        fieldName, "'"+values.get(0)+"'", "'"+values.get(1)+"'")).append(AND_RELATION);
            }
        } else {
            if (StringUtils.isNoneEmpty(stringValue)) {
                filter.append(StrUtil.format(ScopeFilterTypeEnum.getName(scopeFilterType),
                        fieldName, stringValue)).append(AND_RELATION);
            } else {
                if (Objects.equals(ScopeFilterTypeEnum.IS_NULL.getCode(), scopeFilterType)
                        || Objects.equals(ScopeFilterTypeEnum.IS_NOT_NULL.getCode(), scopeFilterType)) {
                    filter.append(StrUtil.format(ScopeFilterTypeEnum.getName(scopeFilterType), String.format(toStringFormat, fieldName)))
                            .append(AND_RELATION);
                }
            }
        }
    }

    /**
     * get map record limit offset sql need info
     * @param pageNum page number
     * @param pageSize page size
     * @param maxRows max rows
     * @return map
     */
    public static Map<String, Long> getLimitSql(Long pageNum, Long pageSize, Integer maxRows) {
        HashMap<String, Long> map = new HashMap<>();
        boolean hasMaxRows = Objects.nonNull(maxRows);

        if (Objects.nonNull(pageNum) && Objects.nonNull(pageSize)) {
            if (!(PAGE_QUERY_UNLIMITED.equals(pageNum) || PAGE_QUERY_UNLIMITED.equals(pageSize))) {
                long limit = pageSize;
                long offset = (pageNum - 1) * pageSize;

                if (hasMaxRows) {
                    limit = Math.min(pageSize, maxRows);
                    if (offset > maxRows) {
                        offset = 0;
                        limit = 0;
                    }
                }

                map.put("limit", limit);
                map.put("offset", offset);
            } else {
                if (hasMaxRows) {
                    map.put("limit", Long.valueOf(maxRows));
                    map.put("offset", Long.valueOf(0));
                }
            }
        } else {
            if (hasMaxRows) {
                map.put("limit", Long.valueOf(maxRows));
                map.put("offset", Long.valueOf(0));
            }
        }

        // 移动日志打印到调用方，或者使用更高效的日志级别
         log.info("limit {} offset {}", map.get("limit"), map.get("offset"));
        return map;
    }

    public static void main(String[] args){
        for (int i=1;i<10;i++) {
            getLimitSql(Long.valueOf(i), 20L, 20);
        }
    }

}
