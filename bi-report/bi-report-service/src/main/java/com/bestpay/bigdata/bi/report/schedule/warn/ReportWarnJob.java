package com.bestpay.bigdata.bi.report.schedule.warn;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.bestpay.bigdata.bi.backend.api.QueryService;
import com.bestpay.bigdata.bi.common.common.Constant;
import com.bestpay.bigdata.bi.common.dto.common.DatePickerDTO;
import com.bestpay.bigdata.bi.common.dto.dataset.DatasetInfo;
import com.bestpay.bigdata.bi.common.dto.warn.ReportWarnRuleDTO;
import com.bestpay.bigdata.bi.common.dto.warn.WarnConditionDTO;
import com.bestpay.bigdata.bi.common.entity.QueryContext;
import com.bestpay.bigdata.bi.common.enums.CodeEnum;
import com.bestpay.bigdata.bi.common.enums.FieldType;
import com.bestpay.bigdata.bi.common.enums.QueryType;
import com.bestpay.bigdata.bi.common.enums.Status;
import com.bestpay.bigdata.bi.common.error.ReportErrorCode;
import com.bestpay.bigdata.bi.common.exception.BiException;
import com.bestpay.bigdata.bi.common.response.ReportDetailVO;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.common.response.UserMobileResponse;
import com.bestpay.bigdata.bi.common.util.AssertUtil;
import com.bestpay.bigdata.bi.common.util.LogTraceIdGenerator;
import com.bestpay.bigdata.bi.common.util.UserContextUtil;
import com.bestpay.bigdata.bi.database.api.common.DatePickerDAOService;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardCardService;
import com.bestpay.bigdata.bi.database.api.report.ReportWarnRuleDAOService;
import com.bestpay.bigdata.bi.database.api.report.ReportWarnRuleLogDAOService;
import com.bestpay.bigdata.bi.database.api.report.ReportWarnTaskLogDAOService;
import com.bestpay.bigdata.bi.database.dao.common.DatePickerConfigDO;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardCardDO;
import com.bestpay.bigdata.bi.database.dao.warn.ReportWarnRuleDo;
import com.bestpay.bigdata.bi.database.dao.warn.ReportWarnRuleLogDo;
import com.bestpay.bigdata.bi.database.dao.warn.ReportWarnTaskLogDo;
import com.bestpay.bigdata.bi.report.bean.warn.DingTalkConfig;
import com.bestpay.bigdata.bi.report.bean.warn.ReportWarnDTO;
import com.bestpay.bigdata.bi.report.bean.warn.WarnConfig;
import com.bestpay.bigdata.bi.report.beforeSQL.BeforeSQLService;
import com.bestpay.bigdata.bi.report.beforeSQL.QueryParamHandlerService;
import com.bestpay.bigdata.bi.report.beforeSQL.util.DatePickerUtil;
import com.bestpay.bigdata.bi.report.enums.common.WarnSourceTypeEnum;
import com.bestpay.bigdata.bi.report.enums.report.PolymerizationEnum;
import com.bestpay.bigdata.bi.report.enums.report.WarnRuleEnum;
import com.bestpay.bigdata.bi.report.request.report.QueryReportRequest;
import com.bestpay.bigdata.bi.report.request.report.ReportRequest;
import com.bestpay.bigdata.bi.report.schedule.sender.bean.DingTalkSubscribeContent;
import com.bestpay.bigdata.bi.report.schedule.sender.bean.DingTalkTypeBean;
import com.bestpay.bigdata.bi.report.schedule.sender.bean.EmailSubscribeContent;
import com.bestpay.bigdata.bi.report.schedule.sender.bean.EmailTypeBean;
import com.bestpay.bigdata.bi.report.schedule.sender.handler.SubscribeTypeSender;
import com.bestpay.bigdata.bi.report.schedule.subscribe.enums.SubscribeType;
import com.bestpay.bigdata.bi.report.schedule.subscribe.sender.SMSSubscribeContent;
import com.bestpay.bigdata.bi.report.schedule.subscribe.sender.SMSTypeBean;
import com.bestpay.bigdata.bi.report.schedule.warn.bean.ReportRuleMessage;
import com.bestpay.bigdata.bi.report.schedule.warn.bean.ReportWarnMessage;
import com.bestpay.bigdata.bi.report.service.report.ReportProcessService;
import com.bestpay.bigdata.bi.report.service.report.ReportUpdateService;
import com.bestpay.bigdata.bi.report.service.report.ReportWarnService;
import com.bestpay.bigdata.bi.report.sql.SemanticManager;
import com.bestpay.bigdata.bi.report.sql.bean.ReportColumnConfig;
import com.bestpay.bigdata.bi.report.sql.bean.ReportConditionInfo;
import com.bestpay.bigdata.bi.report.sql.bean.ReportSqlInfo;
import com.bestpay.bigdata.bi.report.sql.bean.SingleReportSqlInfo;
import com.bestpay.bigdata.bi.report.sql.bean.SqlResult;
import com.bestpay.bigdata.bi.thirdparty.api.AiplusService;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.jetbrains.annotations.NotNull;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * ClassName: ReportWarnJob
 * Package: com.bestpay.bigdata.bi.report.schedule
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/8/4 17:15
 * @Version 1.0
 */
@Slf4j
@Component
public class ReportWarnJob implements Job {
    @Resource
    private ReportWarnRuleDAOService reportWarnRuleDAOService;

    @Resource
    private ReportWarnTaskLogDAOService reportWarnTaskLogDAOService;

    @Resource
    private ReportWarnRuleLogDAOService reportWarnRuleLogDAOService;

    @Resource
    private DispatchQueryExecutor dispatchQueryExecutor;

    @Reference
    private QueryService queryService;

    @Resource
    private List<SubscribeTypeSender> subscribeTypeSenderList;

    @Resource
    private SemanticManager semanticManager;

    @Resource
    private DatePickerUtil datePickerUtil;

    @Resource
    private DatePickerDAOService pickerDAOService;

    @Resource
    private AiplusService aiplusService;

    @Resource
    private ReportUpdateService updateService;

    @Resource
    private ReportProcessService reportProcessService;

    @Resource
    private QueryParamHandlerService queryParamHandlerService;

    @Resource
    private BeforeSQLService beforeSQLService;

    @Resource
    private DashboardCardService cardService;

    @Resource
    private ReportWarnService reportWarnService;

    @Override
    public void execute(JobExecutionContext jobExecutionContext){
        String traceId = LogTraceIdGenerator.generateTraceId();
        MDC.put(LogTraceIdGenerator.TRACE_ID, traceId);

        ReportWarnTaskLogDo reportWarnTaskLogDo = new ReportWarnTaskLogDo();
        ReportWarnMessage reportWarnMessage = new ReportWarnMessage();
        try {
            JobDataMap jobDataMap = jobExecutionContext.getJobDetail().getJobDataMap();

            ReportWarnDTO reportWarn = (ReportWarnDTO) jobDataMap.get("reportWarn");
            log.info("reportWarn : {}", JSONUtil.toJsonStr(reportWarn));
            String code = reportWarn.getCode();
            log.info("execute report warn job {} , report id : {}", code, reportWarn.getReportId());
            reportWarnTaskLogDo.setStartTime(new Date());
            reportWarnTaskLogDo.setWarnCode(reportWarn.getCode());

            // get dataset info from report db
            reportWarnTaskLogDo.setCreatedBy(reportWarn.getCreatedBy());
            reportWarnTaskLogDo.setUpdatedBy(reportWarn.getCreatedBy());
            // false 没改 true 改了，改到预警信息 在真正执行的时候再去做校验，这样有助于服务启动的时候
            Boolean judgeWarnModifyFlag = reportWarnService.judgeWarnModify(reportWarn).getData();
            if (judgeWarnModifyFlag){
                String format = StrUtil.format("judge report warn modify fail, warn code : {}, report id : {}", reportWarn.getCode(), reportWarn.getReportId());
                log.info("judge report warn modify fail, warn code : {}, report id : {}", reportWarn.getCode(), reportWarn.getReportId());
                throw new BiException(ReportErrorCode.REPORT_WARN_BASE_INFO_CHANGED,format);
            }

            // AILAB-47538 修复数据集权限需要获取用户信息bug, 这里用创建人, 因为创建人就是责任人
            UserContextUtil.preHandler(aiplusService.getUserInfoByEmail(reportWarn.getCreatedBy()));


            // 获取报表配置详情
            ReportDetailVO detailVO = getReportDetailVO(reportWarn);

            log.info("execute report warn job {} , get dataset info : {}", code,  JSONUtil.toJsonStr(detailVO.getDatasetInfoList()));

            // generate base sql with no date format sqlContent
            QueryReportRequest reportRequest = new QueryReportRequest();
//            reportRequest.setReportId(reportWarn.getReportId());
            reportRequest.setQueryConditions(new ArrayList<>());
            SqlResult sqlResult = getSqlResult(reportRequest, detailVO);
            Map<String, String> uuidAliasMap = sqlResult.getUuidAliasMap();
            log.info("execute report warn job {}, uuidAliasMap : {}", code, JSONUtil.toJsonStr(uuidAliasMap));
            reportWarnMessage.setUuidAliasMap(uuidAliasMap);

            // generate List<ReportConditionInfo> conditionInfos
            List<ReportWarnRuleDo> reportWarnRuleDoList = reportWarnRuleDAOService
                .query(ReportWarnRuleDTO.builder().warnCodeList(Lists.newArrayList(reportWarn.getCode()))
                .build());

            log.info("execute report warn job {}, rule size : {}", code, reportWarnRuleDoList.size());

            // Overall implementation of early warning sql
            Integer warnRule = reportWarn.getWarnRule();
            boolean isAnyNeedWarn = false;
            if (WarnRuleEnum.ALL_RULE.getCode() == warnRule) {
                isAnyNeedWarn = asyncExecuteAllRule(reportWarnRuleDoList,
                    uuidAliasMap,
                    sqlResult,
                    detailVO.getDatasetInfoList().get(0),
                    queryService,
                    reportWarnRuleLogDAOService,
                    semanticManager);

            } else {
                isAnyNeedWarn = syncExecuteArbitraryRule(reportWarnRuleDoList,
                    uuidAliasMap,
                    sqlResult,
                    detailVO.getDatasetInfoList().get(0),
                    queryService,
                    reportWarnRuleLogDAOService,
                    semanticManager);

            }
            reportWarnMessage.setAnyNeedWarn(isAnyNeedWarn);

            // if isAnyNeedWarn is true, then ready to warn
            log.info("是否满足告警条件：{}",isAnyNeedWarn);
            if (isAnyNeedWarn) {
                executeWarn(reportWarn.getWarnConfig(),
                    subscribeTypeSenderList,
                    detailVO,
                    reportWarn,
                    reportWarnMessage);
            }
        } catch (Throwable e) {

            log.error("执行report warn 告警任务发生错误", e);
            reportWarnMessage.setExceptionMessage(ExceptionUtils.getStackTrace(e));

        }finally {
            reportWarnTaskLogDo.setTraceId(MDC.get(LogTraceIdGenerator.TRACE_ID));
            reportWarnTaskLogDo.setEndTime(new DateTime());
            reportWarnTaskLogDo.setMessage(JSONUtil.toJsonStr(reportWarnMessage));
            reportWarnTaskLogDAOService.insert(reportWarnTaskLogDo);
            MDC.remove(LogTraceIdGenerator.TRACE_ID);
        }
    }


    @NotNull
    private ReportDetailVO getReportDetailVO(ReportWarnDTO reportWarn) {
        ReportDetailVO detailVO = null;
        // 报表市场报表
        if(WarnSourceTypeEnum.REPORT.getCode().equals(reportWarn.getWarnSourceType())){
            detailVO
                = updateService.queryReportTemplate(Long.valueOf(reportWarn.getReportId())).getData();
        }else{

            List<DashboardCardDO> cards = cardService.findByCode(null, reportWarn.getReportId());
            if (CollUtil.isEmpty(cards)) {
                throw new BiException(ReportErrorCode.REPORT_NOT_EXISTS,String.format("报表不存在, code=%s", reportWarn.getReportId()));
            }

            // 仪表板报表
            Response<ReportDetailVO> data
                = reportProcessService.handlerDashboardReportCardInfo(cards.get(0).getCardId());
            detailVO = data.getData();
        }

        if (detailVO == null) {
            throw new BiException(ReportErrorCode.REPORT_NOT_EXISTS,String.format("报表不存在, reportId=%s, warnSourceType=%s",
                reportWarn.getReportId(), reportWarn.getWarnSourceType()));
        }
        return detailVO;
    }

    private SqlResult getSqlResult(QueryReportRequest reportRequest, ReportDetailVO detailVO){
        ReportRequest report = convertReportRequest(reportRequest,detailVO);

        // 查询前参数处理
        queryParamHandlerService.handlerParam(report);

        SqlResult sqlResult = beforeSQLService
            .buildSqlResult(report, false, UserContextUtil.getUserInfo(), true);
        return sqlResult;
    }

    public ReportRequest convertReportRequest(QueryReportRequest reportRequest, ReportDetailVO detailVO) {
        ReportRequest report
            = JSONUtil.toBean(JSONUtil.toJsonStr(detailVO), ReportRequest.class);
        BeanUtils.copyProperties(reportRequest,report);
        return report;
    }

    private void executeWarn(WarnConfig warnConfig, List<SubscribeTypeSender> subscribeTypeSenderList,
        ReportDetailVO report, ReportWarnDTO reportWarn, ReportWarnMessage reportWarnMessage) {

        String topic = "大数据BI平台报表预警 报表名称: %s, 预警任务名称: %s, 预警任务code : %s ";
        topic = String.format(topic, report.getReportName(), reportWarn.getName(), reportWarn.getCode());

        List<String> senderErrorMessageList = new ArrayList<>();

        for (String warnType : warnConfig.getWarnTypeList()) {
            SubscribeTypeSender sender = getSubscribeTypeSender(Integer.parseInt(warnType), subscribeTypeSenderList);
            AssertUtil.notNull(sender, CodeEnum.ENUM_CODE_ERROR);

            if (SubscribeType.EMAIL.getCode() == Integer.parseInt(warnType)) {
                EmailSubscribeContent subscribeContent = new EmailSubscribeContent();
                subscribeContent.setMessage(warnConfig.getMessage());

                List<String> emailList = warnConfig.getEmailList();
                for (String email : emailList) {
                    EmailTypeBean subscribeTypeBean = new EmailTypeBean();
                    subscribeTypeBean.setSubEmail(email);
                    subscribeTypeBean.setSubTopic(topic);
                    subscribeTypeBean.setSubMsg(warnConfig.getMessage());
                    try {
                        sender.subscribeSend(subscribeTypeBean, subscribeContent);
                    } catch (Throwable e) {
                        log.error("report warn 告警发送邮件错误", e);
                        senderErrorMessageList.add(ExceptionUtils.getStackTrace(e));
                    }
                }
            } else if (SubscribeType.DING_TALK.getCode() == Integer.parseInt(warnType)){
                DingTalkSubscribeContent subscribeContent = new DingTalkSubscribeContent();
                subscribeContent.setMessage(warnConfig.getMessage());
                subscribeContent.setTitle(topic);
                for (DingTalkConfig dingTalkConfig : warnConfig.getDingTalkList()) {
                    DingTalkTypeBean dingTalkTypeBean = new DingTalkTypeBean();
                    dingTalkTypeBean.setMessage(warnConfig.getMessage());
                    dingTalkTypeBean.setSecret(dingTalkConfig.getSecret());
                    dingTalkTypeBean.setWebHook(dingTalkConfig.getWebhook());
                    try {
                        sender.subscribeSend(dingTalkTypeBean, subscribeContent);
                    } catch (Throwable e) {
                        log.error("report warn 告警发送钉钉错误", e);
                        senderErrorMessageList.add(ExceptionUtils.getStackTrace(e));
                    }
                }
            } else {
                //  短信 则拿到邮箱去查短信
                UserMobileResponse mobileResponse = aiplusService.getUserMobileByEmail(warnConfig.getEmailList());
                List<String> userMobileList = mobileResponse.getUserMobileList();
                senderErrorMessageList.addAll(mobileResponse.getSenderErrorMessageList());
                if (CollUtil.isNotEmpty(userMobileList)){
                    String message = warnConfig.getMessage();
                    SMSSubscribeContent smsSubscribeContent = new SMSSubscribeContent();
                    smsSubscribeContent.setMessage(message);
                    smsSubscribeContent.setUserMobileList(userMobileList);
                    try {
                        sender.subscribeSend(new SMSTypeBean(),smsSubscribeContent);
                    }catch (Throwable e){
                        senderErrorMessageList.add(e.getMessage());
                        log.info("report warn 告警短信发送异常", e);
                    }
                }else {
                    log.warn("报表名称: {}, 预警任务名称:{}, 预警任务code ::{},此次告警任务，无手机号，不可发送", report.getReportName(), reportWarn.getName(), reportWarn.getCode());
                    String errorMsg = "报表名称: %s, 预警任务名称: %s, 预警任务code : %s ,此次告警任务，无手机号，不可发送";
                    String format = String.format(errorMsg, report.getReportName(), reportWarn.getName(), reportWarn.getCode());
                    senderErrorMessageList.add(format);
                }
            }
        }
        reportWarnMessage.setSenderMessage(senderErrorMessageList);
    }

    private SubscribeTypeSender getSubscribeTypeSender(int warnType, List<SubscribeTypeSender> subscribeTypeSenderList) {
        for (SubscribeTypeSender subscribeTypeSender : subscribeTypeSenderList) {
            if (warnType == subscribeTypeSender.getSubscribeTypeCode()) {
                return subscribeTypeSender;
            }
        }
        return null;
    }

    private boolean syncExecuteArbitraryRule(List<ReportWarnRuleDo> reportWarnRuleDoList,
                                             Map<String, String> uuidAliasMap,
                                             SqlResult baseSqlResult,
                                             DatasetInfo curDatasetInfo,
                                             QueryService queryService,
                                             ReportWarnRuleLogDAOService reportWarnRuleLogDAOService,
                                             SemanticManager semanticManager) {

        for (ReportWarnRuleDo warnRuleDo : reportWarnRuleDoList) {
            if (judgeReportRule(warnRuleDo,
                uuidAliasMap,
                baseSqlResult,
                curDatasetInfo,
                queryService,
                reportWarnRuleLogDAOService,
                semanticManager)) {

                // Any rule satisfies automatic exit
                return true;
            }
        }
        return false;
    }


    private boolean asyncExecuteAllRule(List<ReportWarnRuleDo> reportWarnRuleDoList,
                                        Map<String, String> uuidAliasMap,
                                        SqlResult sqlResult,
                                        DatasetInfo curDatasetInfo,
                                        QueryService queryService,
                                        ReportWarnRuleLogDAOService reportWarnRuleLogDAOService,
                                        SemanticManager semanticManager) {
        List<Future<Boolean>> submitTaskList = new ArrayList<>();
        for (ReportWarnRuleDo warnRuleDo : reportWarnRuleDoList) {
            RuleExecuteTask ruleExecuteTask = new RuleExecuteTask(warnRuleDo, uuidAliasMap, sqlResult,
                    curDatasetInfo, queryService, reportWarnRuleLogDAOService, semanticManager);
            submitTaskList.add(dispatchQueryExecutor.getExecutorService().submit(ruleExecuteTask));
        }

        for (Future<Boolean> booleanFuture : submitTaskList) {
            // As long as a rule is not satisfied, it ends automatically.
            try {
                // Blocking waiting
                if (!booleanFuture.get()) {
                    return false;
                }
            } catch (Exception e) {
                log.error("异步获取rule执行发生错误 ", e);
                return false;
            }
        }
        return true;
    }

    class RuleExecuteTask implements Callable<Boolean> {
        private ReportWarnRuleDo reportWarnRuleDo;
        private Map<String, String> uuidAliasMap;
        private SqlResult baseSqlResult;
        private DatasetInfo curDatasetInfo;
        private QueryService queryService;
        private ReportWarnRuleLogDAOService reportWarnRuleLogDAOService;
        private SemanticManager semanticManager;
        private String traceId;

        public RuleExecuteTask(ReportWarnRuleDo reportWarnRuleDo, Map<String, String> uuidAliasMap,
                       SqlResult baseSqlResult, DatasetInfo curDatasetInfo, QueryService queryService,
                               ReportWarnRuleLogDAOService reportWarnRuleLogDAOService, SemanticManager semanticManager) {
            this.reportWarnRuleDo = reportWarnRuleDo;
            this.uuidAliasMap = uuidAliasMap;
            this.baseSqlResult = baseSqlResult;
            this.curDatasetInfo = curDatasetInfo;
            this.queryService = queryService;
            this.reportWarnRuleLogDAOService = reportWarnRuleLogDAOService;
            this.semanticManager = semanticManager;
            this.traceId = MDC.get(LogTraceIdGenerator.TRACE_ID);
        }

        @Override
        public Boolean call(){

            try {
                MDC.put(LogTraceIdGenerator.TRACE_ID, traceId);

                return judgeReportRule(reportWarnRuleDo,
                    uuidAliasMap,
                    baseSqlResult,
                    curDatasetInfo,
                    queryService,
                    reportWarnRuleLogDAOService,
                    semanticManager);

            }catch (Exception e){
                log.error("report warn ruleExecuteTask fail", e);
                throw e;
            }finally {
                MDC.remove(LogTraceIdGenerator.TRACE_ID);
            }

        }
    }


    private boolean judgeReportRule(ReportWarnRuleDo reportWarnRuleDo,
        Map<String, String> uuidAliasMap,
        SqlResult baseSqlResult,
        DatasetInfo curDatasetInfo,
        QueryService queryService,
        ReportWarnRuleLogDAOService reportWarnRuleLogDAOService,
        SemanticManager semanticManager) {

        boolean result = false;
        ReportRuleMessage message = new ReportRuleMessage();
        ReportWarnRuleLogDo reportWarnRuleLogDo = new ReportWarnRuleLogDo();
        try {

            reportWarnRuleLogDo.setStartTime(new Date());
            reportWarnRuleLogDo.setRuleId(reportWarnRuleDo.getId());
            reportWarnRuleLogDo.setWarnCode(reportWarnRuleDo.getWarnCode());
            reportWarnRuleLogDo.setCreatedBy(reportWarnRuleDo.getCreatedBy());
            reportWarnRuleLogDo.setUpdatedBy(reportWarnRuleDo.getUpdatedBy());

            List<WarnConditionDTO> conditionDescList
                = JSONUtil.toList(reportWarnRuleDo.getConditionDesc(), WarnConditionDTO.class);

            WarnConditionDTO expectationDesc = JSONUtil
                .toBean(reportWarnRuleDo.getExpectationDesc(), WarnConditionDTO.class);

            List<ReportConditionInfo> reportConditionInfos
                = convertRuleFilterConditions(conditionDescList, expectationDesc, uuidAliasMap);

            log.info("execute warn rule job , rule id : {}, warn code : {}, reportConditionInfos : {}",
                    reportWarnRuleDo.getId(), reportWarnRuleDo.getWarnCode(),
                JSONUtil.toJsonStr(reportConditionInfos));

            message.setReportConditionInfos(reportConditionInfos);

            // generate single execute sql
            String sql = generateExecuteSqlContent(reportConditionInfos,
                baseSqlResult,
                curDatasetInfo,
                semanticManager);

            log.debug("execute warn rule job,rule id : {}, warn code : {}, generate sql : {}",
                reportWarnRuleDo.getId(),
                reportWarnRuleDo.getWarnCode(),
                sql);

            // execute sql
            QueryContext queryContext = executeSql(sql,
                curDatasetInfo.getDataSourceName(),
                curDatasetInfo.getSqlEngine().getEngineName(),
                queryService);

            reportWarnRuleLogDo.setQueryId(queryContext.getQueryId());
            reportWarnRuleLogDo.setStatus(queryContext.getStatus().getDesc());
            List<List<String>> results = queryContext.getResults();
            // get result, if result > 0 represent trigger this rule
            if (!CollectionUtils.isEmpty(results)) {
                List<String> data = results.get(0);
                if (!CollectionUtils.isEmpty(data) && !StringUtils.isBlank(data.get(0))) {
                    long num = Long.parseLong(data.get(0));
                    if (num > 0) {
                        result = true;
                    }
                }
            } else {
                if (queryContext.getStatus().getCode() != Status.QUERY_SUCCESS.getCode()) {
                    message.setBackendMessage(queryContext.getMessage());
                }
            }
        } catch (Throwable e) {
            log.error("执行 report warn rule 发生错误", e);
            message.setOtherMessage(ExceptionUtils.getStackTrace(e));
            reportWarnRuleLogDo.setStatus("失败");
        } finally {
            reportWarnRuleLogDo.setMessage(JSONUtil.toJsonStr(message));
            reportWarnRuleLogDo.setEndTime(new DateTime());
            reportWarnRuleLogDo.setTraceId(MDC.get(LogTraceIdGenerator.TRACE_ID));
            log.info("insert into reportWarnRuleLog");
            reportWarnRuleLogDAOService.insert(reportWarnRuleLogDo);
        }


        return result;
    }


    private QueryContext executeSql(String sql, String databaseSource, String engineName, QueryService queryService) {
        int sqlType = Constant.SQL_DQL_TYPE;

        QueryContext queryContext = new QueryContext(sql, engineName, "<EMAIL>", sqlType);
        queryContext.setDataSource(databaseSource);
        queryContext.setClusterName(databaseSource);

        queryContext.setUserOrg("大数据部门");
        queryContext.setUsername("<EMAIL>");
        queryContext.setTypeCode(QueryType.QUERY_REPORT.getCode());
        queryContext.setUseCache(false);
        queryContext = queryService.syncExecuteQuery(queryContext);
        return queryContext;
    }


    private String generateExecuteSqlContent(List<ReportConditionInfo> reportConditionInfos,
        SqlResult baseSqlResult,
        DatasetInfo curDatasetInfo,
        SemanticManager semanticManager) {

        log.info("report warn job generateExecuteSqlContent sqlResult : {}", baseSqlResult);
        ReportSqlInfo reportSqlInfo = new ReportSqlInfo();
        reportSqlInfo.setSqlEngine(curDatasetInfo.getSqlEngine());
        reportSqlInfo.setMaxRows(10);
        reportSqlInfo.setPageNum(1L);
        reportSqlInfo.setPageSize(10L);
        String sql = baseSqlResult.nestedParseSql(baseSqlResult.getSqlContentWithNoDateFormat());
        reportSqlInfo.setLogicView(sql);
        SingleReportSqlInfo sqlInfo = new SingleReportSqlInfo();
        ReportColumnConfig index = new ReportColumnConfig();
        index.setPolymerization(PolymerizationEnum.COUNT.getMsg());
        // Fixed writing method for scene judgment
        index.setOriginEnName("1");
        index.setTypeName("CHARACTER");
        index.setShowTypeName("CHARACTER");
        sqlInfo.setIndexFields(Lists.newArrayList(index));
        sqlInfo.setFilterConditions(reportConditionInfos);
        sqlInfo.setDatabase(curDatasetInfo.getDataBaseName());
        sqlInfo.setTable(null);
        reportSqlInfo.setSqlInfo(sqlInfo);
        return semanticManager.semanticAnalysis(reportSqlInfo).getSql();
    }


    private List<ReportConditionInfo> convertRuleFilterConditions(List<WarnConditionDTO> conditionDescList,
        WarnConditionDTO expectationDesc,
        Map<String, String> uuidAliasMap) {

        List<ReportConditionInfo> reportConditionInfos = new ArrayList<>();
        // single rule convert filter conditions
        for (WarnConditionDTO conditionDesc : conditionDescList) {
            ReportConditionInfo conditionInfo = new ReportConditionInfo();
            BeanUtils.copyProperties(conditionDesc, conditionInfo);
            String alias = uuidAliasMap.get(conditionDesc.getUuid());
            conditionInfo.setEnName(alias);
            conditionInfo.setOriginEnName(alias);
            conditionInfo.setTypeName(conditionDesc.getShowTypeName()); //todo just think don't need type convert

            if(FieldType.DATETIME.name().equalsIgnoreCase(conditionDesc.getShowTypeName())) {

                DatePickerConfigDO pickerConfigDO
                    = pickerDAOService.select(conditionDesc.getDatePickerId());

                DatePickerDTO datePickerDTO = pickerConfigDO.getDTO();
                BeanUtils.copyProperties(conditionDesc, datePickerDTO);

                Map<String, List<String>> timesMap = datePickerUtil
                    .getTimes(Lists.newArrayList(datePickerDTO), Lists.newArrayList());

                if(timesMap.get(conditionDesc.getUuid())==null){
                    continue;
                }

                conditionInfo.setValues(Lists.newArrayList(timesMap.get(conditionDesc.getUuid())));
                conditionInfo.setStringValue(null);
                conditionInfo.setValueType(true);
            }

            reportConditionInfos.add(conditionInfo);
        }

        ReportConditionInfo conditionInfo = new ReportConditionInfo();
        expectationDesc.setShowTypeName(FieldType.DECIMAL.name());
        BeanUtils.copyProperties(expectationDesc, conditionInfo);
        String alias = uuidAliasMap.get(expectationDesc.getUuid());
        conditionInfo.setEnName(alias);
        conditionInfo.setOriginEnName(alias);
        conditionInfo.setTypeName(expectationDesc.getShowTypeName());
        reportConditionInfos.add(conditionInfo);

        return reportConditionInfos;
    }
}
