package com.bestpay.bigdata.bi.report.service.impl.embed;

import static com.bestpay.bigdata.bi.common.constant.BIConstant.EMAIL;
import static com.bestpay.bigdata.bi.common.constant.BIConstant.appKeyName;
import static com.bestpay.bigdata.bi.common.constant.BIConstant.appKeySalt;
import static java.util.Objects.requireNonNull;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.api.RedisService;
import com.bestpay.bigdata.bi.common.common.Constant;
import com.bestpay.bigdata.bi.common.config.ApolloRefreshConfig;
import com.bestpay.bigdata.bi.common.constant.BIConstant;
import com.bestpay.bigdata.bi.common.entity.Org;
import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.common.enums.CodeEnum;
import com.bestpay.bigdata.bi.common.enums.DataScreenStatusEnum;
import com.bestpay.bigdata.bi.common.enums.StatusCodeEnum;
import com.bestpay.bigdata.bi.common.error.AppEmbedErrorCode;
import com.bestpay.bigdata.bi.common.error.DashboardErrorCode;
import com.bestpay.bigdata.bi.common.error.UserErrorCode;
import com.bestpay.bigdata.bi.common.exception.BiException;
import com.bestpay.bigdata.bi.common.response.PageQueryVO;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.common.util.AssertUtil;
import com.bestpay.bigdata.bi.common.util.CollectionUtil;
import com.bestpay.bigdata.bi.common.util.DateUtil;
import com.bestpay.bigdata.bi.common.util.StringUtil;
import com.bestpay.bigdata.bi.common.util.UserContextUtil;
import com.bestpay.bigdata.bi.database.api.common.AppEmbedService;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardDaoService;
import com.bestpay.bigdata.bi.database.api.report.component.NewReportService;
import com.bestpay.bigdata.bi.database.bean.AuthResourceTypeEnum;
import com.bestpay.bigdata.bi.database.bean.EmbedPageQueryDTO;
import com.bestpay.bigdata.bi.database.bean.dashboard.Dashboard;
import com.bestpay.bigdata.bi.database.dao.common.AppEmbedDO;
import com.bestpay.bigdata.bi.database.dao.datascreen.DataScreenMainDO;
import com.bestpay.bigdata.bi.database.dao.report.NewReportDO;
import com.bestpay.bigdata.bi.report.bean.embed.AppEmbedDetail;
import com.bestpay.bigdata.bi.report.bean.embed.AppKeyInfo;
import com.bestpay.bigdata.bi.report.bean.embed.ShareParam;
import com.bestpay.bigdata.bi.report.constant.CommonConstant;
import com.bestpay.bigdata.bi.report.enums.dashboard.DashboardPublishTypeEnum;
import com.bestpay.bigdata.bi.report.enums.embed.AppEmbedTypeEnum;
import com.bestpay.bigdata.bi.report.enums.embed.NetworkTypeEnum;
import com.bestpay.bigdata.bi.report.request.embed.AppEmbedListRequest;
import com.bestpay.bigdata.bi.report.request.embed.AppEmbedObjectListRequest;
import com.bestpay.bigdata.bi.report.request.embed.AppEmbedRequest;
import com.bestpay.bigdata.bi.report.response.embed.AppEmbedResourceItem;
import com.bestpay.bigdata.bi.report.response.embed.AppEmbedOrgGroupVO;
import com.bestpay.bigdata.bi.report.response.embed.AppEmbedVO;
import com.bestpay.bigdata.bi.report.service.auth.ObjectAuthService;
import com.bestpay.bigdata.bi.report.service.datascreen.DataScreenMainService;
import com.bestpay.bigdata.bi.report.service.embed.MobileEmailDecryptService;
import com.bestpay.bigdata.bi.report.service.embed.ReportAppEmbedService;
import com.bestpay.bigdata.bi.report.service.report.UrlGenerateService;
import com.bestpay.bigdata.bi.report.util.*;
import com.bestpay.bigdata.bi.thirdparty.api.AiPlusUserService;
import com.bestpay.bigdata.bi.thirdparty.api.AiplusService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @author: bj
 * @date: 2022/09/15
 */
@Slf4j
@Service
public class ReportAppEmbedServiceImpl implements ReportAppEmbedService {
    @Resource
    private AppEmbedService appEmbedService;
    @Resource
    private AiplusService aiplusService;
    @Resource
    private ApolloRefreshConfig apolloRefreshConfig;
    @Resource
    private RedisService redisService;
    @Resource
    private DashboardDaoService dashboardDaoService;
    @Resource
    private UrlGenerateService urlGenerateService;
    @Resource
    private MobileEmailDecryptService mobileEmailDecryptService;
    @Resource
    private AiPlusUserService aiPlusUserService;

    @Value("${appKey.expire.time}")
    private String expireTimeByHour;

    @Resource
    private AuthorityCheckUtil authorityCheckUtil;

    @Resource
    private ObjectAuthService objectAuthService;
    @Resource
    private NewReportService newReportService;
    @Resource
    private DataScreenMainService dataScreenMainService;

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Response<Long> insertAppEmbed(AppEmbedRequest appEmbedRequest) {
        UserInfo userInfo = UserContextUtil.getUserInfo();

        AppEmbedDO appEmbedDO = new AppEmbedDO();

        // check param
        requireNonNull(appEmbedRequest.getIsPublishMobile(), "isPublishMobile can not be null");
        checkParam(appEmbedRequest);

        // package param
        packageRequestParamToDo(appEmbedDO, appEmbedRequest);

        appEmbedDO.setIsPublishMobile(appEmbedRequest.getIsPublishMobile());
        appEmbedDO.setTaskCode(generateTaskCode());
        appEmbedDO.setStatusCode(appEmbedRequest.getStatusCode()!=null
            ? appEmbedRequest.getStatusCode() : StatusCodeEnum.OFFLINE.getCode());
        appEmbedDO.setCreatedAt(new Date());
        appEmbedDO.setUpdatedAt(new Date());
        appEmbedDO.setCreatedBy(userInfo.getEmail());
        appEmbedDO.setUpdatedBy(userInfo.getEmail());

        UserInfo user = aiplusService.getUserInfoByEmail(appEmbedDO.getEmail());
        appEmbedDO.setOwnerName(user.getAccount());
        appEmbedDO.setOwnerNameCh(user.getNickName());

        AppEmbedDO insert = appEmbedService.insert(appEmbedDO);

        return Response.ok(insert.getId());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Response<String> completeAppEmbed(AppEmbedRequest appEmbedRequest)
    {
        appEmbedRequest.setStatusCode(StatusCodeEnum.ONLINE.getCode());
        Long emdId = insertAppEmbed(appEmbedRequest).getData();

        AppEmbedRequest embedRequest = AppEmbedRequest.builder()
            .id(emdId)
            .embedType(appEmbedRequest.getEmbedType())
            .build();

        return getEncryptCodeUrl(embedRequest);
    }

    private void checkParam(AppEmbedRequest appEmbedRequest) {

        if (appEmbedRequest.getIsPublishMobile().equals(DashboardPublishTypeEnum.MOBILE.getCode())) {
            // check mobile request param
            requireNonNull(appEmbedRequest.getTaskName(), "task name can not be null");
            requireNonNull(appEmbedRequest.getDirId(), "dir id can not be null");
            requireNonNull(appEmbedRequest.getEmbedObjectName(), "embed object name can not be null");
            requireNonNull(appEmbedRequest.getEmbedObjectId(), "embed object id can not be null");
            requireNonNull(appEmbedRequest.getEmail(), "email 责任人邮箱 can not be null");
            requireNonNull(appEmbedRequest.getOrgCode(), "orgCode can not be null");
            requireNonNull(appEmbedRequest.getEmbedType(), "embedType can not be null");
            if (appEmbedRequest.getTaskName().length() > 50) {
                throw new BiException(AppEmbedErrorCode.APP_EMBED_PARAM_ERROR,"任务名称不能超过50个字符");
            }
        }
    }


    private void packageRequestParamToDo(AppEmbedDO appEmbedDO, AppEmbedRequest appEmbedRequest) {
        if (appEmbedRequest.getIsPublishMobile().equals(DashboardPublishTypeEnum.MOBILE.getCode())) {
            appEmbedDO.setTaskName(appEmbedRequest.getTaskName());
            appEmbedDO.setEmbedObjectName(appEmbedRequest.getEmbedObjectName());
            appEmbedDO.setEmbedObjectId(appEmbedRequest.getEmbedObjectId());
            appEmbedDO.setOrgCode(appEmbedRequest.getOrgCode());
            appEmbedDO.setOwnerName(appEmbedRequest.getOwnerName());
            appEmbedDO.setOwnerNameCh(appEmbedRequest.getOwnerNameCh());
            appEmbedDO.setEmail(appEmbedRequest.getEmail());
            appEmbedDO.setEmbedType(appEmbedRequest.getEmbedType());
            appEmbedDO.setNetworkType(appEmbedRequest.getNetworkType());
        } else {
            BeanUtils.copyProperties(appEmbedRequest, appEmbedDO);
        }
    }

    @Override
    public Response<PageQueryVO<AppEmbedVO>> queryAppEmbedListV2(AppEmbedListRequest request){
        // 默认值
        Integer pageNum = request.getPageNum();
        Integer pageSize = request.getPageSize();
        request.setPageSize(Objects.nonNull(pageSize)?pageSize:10);
        request.setPageNum(Objects.nonNull(pageNum)?pageNum:1);
        requireNonNull(request.getIsPublishMobile(), "isPublishMobile can not be null");

        EmbedPageQueryDTO pageQueryDTO = new EmbedPageQueryDTO();
        BeanUtils.copyProperties(request, pageQueryDTO);
        pageQueryDTO.setEmbedTypeList(Lists.newArrayList(AppEmbedTypeEnum.REPORT.getCode(),AppEmbedTypeEnum.DASHBOARD.getCode()));
        Page<AppEmbedDO> appEmbedDOS = PageHelper.startPage(pageNum, pageSize);
        pageQueryDTO.setKeyWord(ReportUtil.escapeLike(request.getKeyWord()));
        List<AppEmbedDO> appEmbedDOList = appEmbedService.queryAppEmbedList(pageQueryDTO);
        PageQueryVO<AppEmbedVO> page = new PageQueryVO<>();
        page.setPageNum(pageNum);
        page.setPageSize(pageSize);
        page.setTotalSize(appEmbedDOS.getTotal());
        if (CollUtil.isEmpty(appEmbedDOList)){
            page.setTotalSize(0L);
            page.setData(Lists.newArrayList());
            return Response.ok(page);
        }
        // 获取组织列表
        List<Org> orgList = getOrgList();
        // appEmbed to appEmbedVO
        List<AppEmbedVO> appEmbedVOS = Lists.newArrayList();
        for (AppEmbedDO appEmbedDO : appEmbedDOList) {
            AppEmbedVO appEmbedVO = new AppEmbedVO();
            BeanUtils.copyProperties(appEmbedDO, appEmbedVO);
            if (request.getIsPublishMobile().equals(DashboardPublishTypeEnum.PC.getCode())) {
                appEmbedVO.setNetworkTypeName(NetworkTypeEnum.getNameByCode(appEmbedDO.getNetworkType()));
            } else {
                // mobile need show directory name by dashboard id
                if (appEmbedDO.getEmbedType().equals(AppEmbedTypeEnum.DASHBOARD.getCode())) {
                    Dashboard dashboard = dashboardDaoService.getById(appEmbedDO.getEmbedObjectId());
                    if (Objects.nonNull(dashboard)) {
                        appEmbedVO.setDirectoryName(dashboard.getDirName());
                        appEmbedVO.setDirectoryId(dashboard.getDirId());
                    }
                }
            }

            UserInfo userInfo =  aiPlusUserService.getUserInfoByExactMatchEmail(appEmbedDO.getEmail(), false);;
            boolean existFlag = Objects.nonNull(userInfo);
            appEmbedVO.setOwnerName(existFlag && userInfo.getAccount()!=null
                ? userInfo.getAccount() : CommonConstant.UNKNOWN);

            appEmbedVO.setOwnerNameCh( existFlag &&userInfo.getNickName()!=null
                ? userInfo.getNickName() : CommonConstant.UNKNOWN);

            appEmbedVO.setEmbedTypeName(AppEmbedTypeEnum.getNameByCode(appEmbedDO.getEmbedType()));
            appEmbedVO.setOrgName(getOrgName(orgList, appEmbedDO.getOrgCode()));
            appEmbedVO.setStatusName(StatusCodeEnum.getNameByCode(appEmbedDO.getStatusCode()));
            appEmbedVO.setUpdatedAt(DateUtil.format(appEmbedDO.getUpdatedAt(), DateUtil.YMDHM));
            appEmbedVO.setCreatedAt(DateUtil.format(appEmbedDO.getCreatedAt(), DateUtil.YMDHM));
            appEmbedVOS.add(appEmbedVO);
        }
        // 返回数据
        page.setData(appEmbedVOS);
        return Response.ok(page);

    }

    public Response<PageQueryVO<AppEmbedVO>> queryAppEmbedListV2Old(AppEmbedListRequest request){
        // 默认值
        if(Objects.isNull(request.getPageNum())){
            request.setPageNum(1);
        }

        if(Objects.isNull(request.getPageSize())){
            request.setPageSize(10);
        }

        requireNonNull(request.getIsPublishMobile(), "isPublishMobile can not be null");

        log.info("应用嵌入列表多条件查询, request = {}", JSONUtil.toJsonStr(request));

        EmbedPageQueryDTO pageQueryDTO = new EmbedPageQueryDTO();
        BeanUtils.copyProperties(request, pageQueryDTO);
        pageQueryDTO.setOffset((request.getPageNum() - 1) * request.getPageSize());
        pageQueryDTO.setEmbedTypeList(Lists.newArrayList(AppEmbedTypeEnum.REPORT.getCode(),
                AppEmbedTypeEnum.DASHBOARD.getCode()));

        log.info("应用嵌入列表多条件查询, mysql query request = {}", JSONUtil.toJsonStr(pageQueryDTO));

        // 获取总行数
        Long totalSize = appEmbedService.count(pageQueryDTO);

        log.info("应用嵌入列表多条件查询, totalSize = {}", totalSize);

        PageQueryVO<AppEmbedVO> page = new PageQueryVO<>();
        page.setPageNum(request.getPageNum());
        page.setPageSize(request.getPageSize());
        page.setTotalSize(0L);
        page.setData(Lists.newArrayList());

        // 无数据
        if(totalSize==0){
            log.info("应用嵌入列表多条件查询, countByMultiCondition() is zero");
            return Response.ok(PageQueryVO.empty());
        }

        // 总行数
        page.setTotalSize(totalSize);

        // 分页查询
        List<AppEmbedDO> appEmbedDOList = appEmbedService.pageQuery(pageQueryDTO);

        log.info("应用嵌入列表多条件查询, appEmbedDOList={}", JSONUtil.toJsonStr(appEmbedDOList));

        // 无数据
        if(CollectionUtil.isEmpty(appEmbedDOList)){
            log.info("应用嵌入列表多条件查询, pageQueryByMultiCondition() is empty");
            return Response.ok(PageQueryVO.empty());
        }

        log.info("应用嵌入列表多条件查询, appEmbedDOList={}", JSONUtil.toJsonStr(appEmbedDOList));

        // 获取组织列表
        List<Org> orgList = getOrgList();

        log.info("应用嵌入列表多条件查询, orgList={}", JSONUtil.toJsonStr(orgList));

        // appEmbed to appEmbedVO
        List<AppEmbedVO> appEmbedVOS = Lists.newArrayList();
        for (AppEmbedDO appEmbedDO : appEmbedDOList) {
            AppEmbedVO appEmbedVO = new AppEmbedVO();
            BeanUtils.copyProperties(appEmbedDO, appEmbedVO);
            if (request.getIsPublishMobile().equals(DashboardPublishTypeEnum.PC.getCode())) {
                appEmbedVO.setNetworkTypeName(NetworkTypeEnum.getNameByCode(appEmbedDO.getNetworkType()));
            } else {
                // mobile need show directory name by dashboard id
                if (appEmbedDO.getEmbedType().equals(AppEmbedTypeEnum.DASHBOARD.getCode())) {
                    Dashboard dashboard = dashboardDaoService.getById(appEmbedDO.getEmbedObjectId());
                    log.info("DEBUG mobile need show directory name by dashboard id, dashboard : {} ", dashboard);
                    if (Objects.nonNull(dashboard)) {
                        appEmbedVO.setDirectoryName(dashboard.getDirName());
                        appEmbedVO.setDirectoryId(dashboard.getDirId());
                    }
                }
            }

            UserInfo userInfo = aiPlusUserService.getUserInfoByExactMatchEmail(appEmbedDO.getEmail(), false);
            appEmbedVO.setOwnerName(userInfo!=null&&userInfo.getAccount()!=null
                    ? userInfo.getAccount() : "unknown");

            appEmbedVO.setOwnerNameCh(userInfo!=null&&userInfo.getNickName()!=null
                    ? userInfo.getNickName() : "unknown");

            appEmbedVO.setEmbedTypeName(AppEmbedTypeEnum.getNameByCode(appEmbedDO.getEmbedType()));
            appEmbedVO.setOrgName(getOrgName(orgList, appEmbedDO.getOrgCode()));
            appEmbedVO.setStatusName(StatusCodeEnum.getNameByCode(appEmbedDO.getStatusCode()));
            appEmbedVO.setUpdatedAt(DateUtil.format(appEmbedDO.getUpdatedAt(), DateUtil.YMDHM));
            appEmbedVO.setCreatedAt(DateUtil.format(appEmbedDO.getCreatedAt(), DateUtil.YMDHM));
            appEmbedVOS.add(appEmbedVO);
        }

        log.info("应用嵌入列表多条件查询, appEmbedVOS={}", JSONUtil.toJsonStr(appEmbedVOS));

        // 返回数据
        page.setData(appEmbedVOS);
        return Response.ok(page);

    }


    private List<Org> getOrgList() {
        List<Org> orgList = aiplusService.getOrgList();
        if (CollectionUtil.isEmpty(orgList)) {
            log.error("getOrgList, orgList is isEmpty");
            return Lists.newArrayList();
        }

        return orgList;
    }

    private String getOrgName(List<Org> orgList, String orgCode){
        if(CollectionUtil.isEmpty(orgList)){
            return "";
        }

        for (Org org : orgList) {
            if (Objects.equals(org.getCode(), orgCode)) {
                return org.getName();
            }
        }

        return "";
    }

    @Override
    public Response<Map<String, Object>> getAppKey(AppEmbedRequest appEmbedRequest){
        String embedObjectName = StringUtil.isNotEmpty(appEmbedRequest.getEmbedObjectName())
                ? appEmbedRequest.getEmbedObjectName() : appEmbedRequest.getDashboardName();

        if(embedObjectName==null||appEmbedRequest.getPlatform()==null){
            return Response.error(CodeEnum.APP_EMBED_APP_KEY_REQUEST_PARAMETER_ERROR);
        }

        // make new appkey
        AppKeyInfo appKeyInfo = AppKeyInfo.builder()
                .appKeySalt(appKeySalt)
                .embedObjectName(embedObjectName)
                .platform(appEmbedRequest.getPlatform())
                .timeStamp(System.currentTimeMillis())
                .build();
        log.info("new appKey info  : {}", appKeyInfo);
        String appKey = Base64Util.encryptBASE64(RSAUtil.encryptBase64(JSONUtil.toJsonStr(appKeyInfo)).getBytes());

        Map map=new HashMap();
        map.put(appKeyName,appKey);

        return Response.ok(map);
    }

    @Override
    public Response<String> getKey(Long id){
        AppEmbedDO appEmbedDO = appEmbedService.queryById(id);

        AppKeyInfo appKeyInfo = AppKeyInfo.builder()
            .timeStamp(System.currentTimeMillis())
            .platform(appEmbedDO.getPlatform())
            .embedObjectName(appEmbedDO.getEmbedObjectName())
            .appKeySalt("bigdata-bi-appKey:")
            .build();

        String appKey = new String(Base64Util.encryptBASE64(RSAUtil
            .encryptBase64(JSONUtil.toJsonStr(appKeyInfo)).getBytes()));

        ShareParam shareParam = new ShareParam();
        shareParam.setAppKey(appKey);

        return Response.ok(Base64Util
            .encryptBASE64(JSONUtil.toJsonStr(shareParam).getBytes()));
    }


    @Override
    public Response<AppEmbedDO> queryAppEmbedById(Long id) {
        return Response.ok(appEmbedService.queryById(id));
    }

    @Override
    public Response<AppEmbedVO> queryAppEmbedAuthById(String str) {
        return Response.ok(checkCustomCookie(str));
    }

    @Override
    public AppEmbedVO checkCustomCookie(String str) {

        JSONObject jsonObject = JSONUtil.parseObj(str);

        // compatible old url
        boolean isCompatibleUrl = false;
        if (Objects.nonNull(jsonObject.getJSONObject("headers"))) {
            isCompatibleUrl = true;
        }

        if (isCompatibleUrl) {
            Long id = Convert.toLong(jsonObject.get("id"));
            String appKey = jsonObject.getJSONObject("headers").getStr(appKeyName);
            log.info("queryAppEmbed id:{},headers:{}", id, appKey);

            AppEmbedDO appEmbed = appEmbedService.queryById(id);
            AssertUtil.notNull(appEmbed, CodeEnum.APP_EMBED_NULL_ERROR);
            String md5 = DigestUtil.md5Hex(appKeySalt + appEmbed.getEmbedObjectName() + appEmbed.getPlatform());
            if (ObjectUtil.equal(md5, appKey)) {
                AppEmbedVO appEmbedVO = new AppEmbedVO();
                BeanUtils.copyProperties(appEmbed, appEmbedVO);
                return appEmbedVO;
            } else {
                // check Verify the identity information of the other party
                getAppEmbedStatusCode(appEmbed);
                checkAppInfo(appEmbed, appKey);
                AppEmbedVO appEmbedVO = new AppEmbedVO();
                BeanUtils.copyProperties(appEmbed, appEmbedVO);
                return appEmbedVO;
            }
        } else {
            // first decryption key that contains outside platform custom parameter
            String shareParamJsonStr = null;
            try {
                if (Objects.isNull(jsonObject.get("key"))) {
                    throw new BiException(AppEmbedErrorCode.APP_URL_NOT_COMPLETE_ERROR,"链接url缺失!");
                }
                shareParamJsonStr = new String(Base64Util.decryBASE64(jsonObject.get("key").toString()));
            } catch (Exception e) {
                log.error("queryAppEmbedAuthById fail", e);
                throw new BiException(AppEmbedErrorCode.APP_DECRY_ERROR,"解密报错!");
            }
            ShareParam shareParam = JSONUtil.toBean(shareParamJsonStr, ShareParam.class);
            log.info("decryBASE64 shareParam : {}", shareParam);
            // check Verify the identity information of the other party
            String id = null;
            try {
                id = new String(Base64Util.decryBASE64(jsonObject.get("id").toString()));
            } catch (Exception e) {
                log.error("queryAppEmbedAuthById fail", e);
                throw new BiException(AppEmbedErrorCode.APP_SERVER_INTERNAL_ERROR,"调用服务内部错误!");
            }
            AppEmbedDO appEmbed = appEmbedService.queryById(Long.parseLong(JSONUtil.parseObj(id).get("id").toString()));
            AssertUtil.notNull(appEmbed, CodeEnum.APP_EMBED_NULL_ERROR);
            String embedType = appEmbed.getEmbedType();
            Map<String, Integer> stringIntegerMap = getAppEmbedStatusCode(appEmbed);
            Integer embedStatusCode  = stringIntegerMap.get(embedType);
            String tipStr = "已下线，请联系责任人~";
            String msg  = AppEmbedTypeEnum.getNameByCode(embedType) + tipStr;

            checkAppInfo(appEmbed, shareParam.getAppKey());
            checkPublishType(appEmbed);

            AppEmbedVO appEmbedVO = new AppEmbedVO();
            BeanUtils.copyProperties(appEmbed, appEmbedVO);
            appEmbedVO.setEmbedStatusCode(embedStatusCode);
            appEmbedVO.setEmbedMsg( StatusCodeEnum.OFFLINE.getCode() == embedStatusCode ?msg : StrUtil.EMPTY);

            if(appEmbed.getEmbedType().equals(BIConstant.OBJECT_TYPE_NEW_DATASCREEN)){
                DataScreenMainDO dataScreen = dataScreenMainService.queryById(appEmbed.getEmbedObjectId());
                AssertUtil.notNull(dataScreen, CodeEnum.DATA_SCREEN_NOT_EXIST);
                appEmbedVO.setDataScreenUUID(dataScreen.getDataScreenUuid());
                if(CollUtil.isNotEmpty(shareParam.getParam())) {
                    appEmbedVO.setVersionType(shareParam.getParam().get("version"));
                }
            }

            return appEmbedVO;
        }
    }


    private void checkPublishType(AppEmbedDO appEmbed) {
        if(!AppEmbedTypeEnum.DASHBOARD.getCode().equalsIgnoreCase(appEmbed.getEmbedType())){
            return;
        }

        if(!AppEmbedTypeEnum.NEW_DATASCREEN.getCode().equalsIgnoreCase(appEmbed.getEmbedType())){
            return;
        }

        if(DashboardPublishTypeEnum.MOBILE.getCode() != appEmbed.getIsPublishMobile()){
            return;
        }

        Dashboard dashboard = dashboardDaoService.getById(appEmbed.getEmbedObjectId());
        if(DashboardPublishTypeEnum.MOBILE.getCode()!=dashboard.getIsPublishMobile()){
            throw new BiException(DashboardErrorCode.DASHBOARD_NOT_PUBLISH_MOBILE,"仪表板没有发布移动端!");
        }
    }


    private void checkAppInfo(AppEmbedDO appEmbed, String appKey) {
        // 校验 仪表板和报表基础信息
//        checkDashboardAndReport(appEmbed);
        // check Verify the identity information of the other party
        AppKeyInfo appKeyInfo = null;
        try {
            appKeyInfo = JSONUtil.toBean(RSAUtil.decryptBase64(new String(Base64Util.decryBASE64(appKey))), AppKeyInfo.class);
        } catch (Exception e) {
            log.error("error, appKey = {}", appKey,e);
            throw new BiException(AppEmbedErrorCode.APP_EMBED_APP_KEY_ERROR,"appKey错误，请重新获取！");
        }
        log.info("use RSA decry appKeyInfo : {}", appKeyInfo);
        AssertUtil.notNull(appEmbed, CodeEnum.APP_EMBED_NULL_ERROR);
        if (!appKeyInfo.getEmbedObjectName().equals(appEmbed.getEmbedObjectName()) ||
                (appEmbed.getPlatform()!=null && !appKeyInfo.getPlatform().equals(appEmbed.getPlatform()))) {
            throw new BiException(AppEmbedErrorCode.APP_VERIFY_IDENTITY_ERROR,"调用方信息有误!");
        }
        // check appKey is any expire
        long currentTimeMillis = System.currentTimeMillis();
        if (currentTimeMillis - appKeyInfo.getTimeStamp() > 1000 * 60 * 60 * Integer.parseInt(expireTimeByHour)) {
            throw new BiException(AppEmbedErrorCode.APP_EMBED_APP_KEY_ERROR,"appKey已经过期，请重新获取!");
        }
    }

    /**
     * 校验 仪表板和报表基础信息
     * @param appEmbed 入参
     * return key  报表，仪表板，数据大屏  value 状态
     */
    private Map<String,Integer> getAppEmbedStatusCode(AppEmbedDO appEmbed){
        Map<String, Integer> map = new HashMap<>();
        String embedType = appEmbed.getEmbedType();

        long embedObjectId = Long.parseLong(String.valueOf(appEmbed.getEmbedObjectId()));
        if (BIConstant.OBJECT_TYPE_DASHBOARD.equals(embedType)
            || BIConstant.OBJECT_TYPE_DATASCREEN.equals(embedType)
            || AppEmbedTypeEnum.DASHBOARD_SUBSCRIBE.getCode().equals(embedType)) {
            Dashboard dashboard = dashboardDaoService.getById(embedObjectId);
            // 校验仪表板和仪表板状态
            AssertUtil.notNull(dashboard, CodeEnum.DASHBOARD_NOT_EXISTS);
            map.put(embedType, dashboard.getStatusCode());
        } else if (BIConstant.OBJECT_TYPE_REPORT.equals(embedType)) {
            NewReportDO report = newReportService.getReportById(embedObjectId);
            AssertUtil.notNull(report, CodeEnum.REPORT_NOT_EXIST);
            map.put(BIConstant.OBJECT_TYPE_REPORT, report.getStatusCode());
        } else if (BIConstant.OBJECT_TYPE_NEW_DATASCREEN.equals(embedType)) {
            DataScreenMainDO dataScreen = dataScreenMainService.queryById(embedObjectId);
            AssertUtil.notNull(dataScreen, CodeEnum.DATA_SCREEN_NOT_EXIST);
            Integer statusCode = null;
            DataScreenStatusEnum statusEnum = DataScreenStatusEnum.getByCode(dataScreen.getStatus());
            switch (statusEnum){
                case SAVED:
                case PUBLISHED:
                    statusCode=StatusCodeEnum.ONLINE.getCode();
                    break;
                case OFFLINE:
                    statusCode=StatusCodeEnum.OFFLINE.getCode();
                    break;
                default:
                    statusCode=StatusCodeEnum.DELETE.getCode();
                    break;
            }
            map.put(BIConstant.OBJECT_TYPE_NEW_DATASCREEN, statusCode);
        } else {
            throw new BiException(AppEmbedErrorCode.APP_EMBED_CONF_ERROR,"嵌入类型不存在");
        }

        return map;
    }

    @Override
    public Response<Boolean> updateAppEmbedById(AppEmbedRequest appEmbedRequest) {
        UserInfo userInfo = UserContextUtil.getUserInfo();

        AppEmbedDO dbappembed = appEmbedService.queryById(appEmbedRequest.getId());
        authorityCheckUtil.checkOwner(EMAIL, dbappembed.getEmail());

        requireNonNull(appEmbedRequest.getId(), "id can not be null");
        AppEmbedDO appEmbedDO = new AppEmbedDO();
        BeanUtils.copyProperties(appEmbedRequest, appEmbedDO);
        appEmbedDO.setUpdatedAt(new Date());
        appEmbedDO.setUpdatedBy(userInfo.getEmail());

        if(StringUtil.isNotEmpty(appEmbedDO.getEmail())) {
            UserInfo user = aiplusService.getUserInfoByEmail(appEmbedDO.getEmail());
            appEmbedDO.setOwnerName(user.getAccount());
            appEmbedDO.setOwnerNameCh(user.getNickName());
        }

        appEmbedService.update(appEmbedDO);

        return Response.ok(true);
    }


    /**
     * 应用嵌入获取url
     *
     * @param appEmbedRequest request
     * @return url
     */
    @Override
    public Response<String> getEncryptCodeUrl(AppEmbedRequest appEmbedRequest) {
        log.info("getEncryptCodeUrl : {}", appEmbedRequest);
        Long id = appEmbedRequest.getId();
        requireNonNull(id, "id can not be null");

        AppEmbedDO appEmbed = appEmbedService.queryById(id);
        if ((Objects.nonNull(appEmbedRequest.getEmbedType())) &&
                !appEmbedRequest.getEmbedType().equalsIgnoreCase(AppEmbedTypeEnum.DATASCREEN.getCode())) {
            // dataScreen skip
            authorityCheckUtil.checkOwner(EMAIL, appEmbed.getEmail());
        }

        // get all url
        String resultUrl = urlGenerateService.embedObjectUrl(appEmbed);

        if ((Objects.nonNull(appEmbedRequest.getEmbedType())) && (
                appEmbedRequest.getEmbedType().equalsIgnoreCase(AppEmbedTypeEnum.DATASCREEN.getCode()) ||
                    appEmbedRequest.getEmbedType().equalsIgnoreCase(AppEmbedTypeEnum.NEW_DATASCREEN.getCode()))) {
            String key = urlGenerateService.embedObjectUrlNoExpireParamKey(appEmbed);
            return Response.ok(resultUrl + key);
        }

        return Response.ok(resultUrl);
    }

    /**
     * 任务编码创建
     * 每次新建自动+1
     */
    public String generateTaskCode() {
        String key="BI-AppEmbed-TaskCode";
        String taskCodePrefix="T";
        try {
            long code = redisService.incr(key);
            AppEmbedDO appEmbedDO =appEmbedService.queryMaxRecord();
            if(appEmbedDO !=null&&code<Long.parseLong(appEmbedDO.getTaskCode().substring(1))){
                redisService.setIncr(key,Long.parseLong(appEmbedDO.getTaskCode().substring(1)));
                code = redisService.incr(key);
            }
            return taskCodePrefix + String.format(Constant.APP_EMBED_CODE_FORMAT,code);
        } catch (Exception e) {
            log.error(Throwables.getStackTraceAsString(e));
            throw new BiException(AppEmbedErrorCode.APP_EMBED_TASK_CODE_ERROR,"生成应用嵌入任务code错误!");
        }

    }

    /**
     * 查询嵌入对象
     * @param request
     * @return
     */
    @Override
    public Response<List<AppEmbedOrgGroupVO>> queryAppEmbedObjectList(AppEmbedObjectListRequest request){

        // 获取用户信息
        UserInfo user = getUserInfo(request);

        try {
            //将用户信息放入全局缓存
            UserContextUtil.preHandler(user);
            EmbedPageQueryDTO pageQuery = new EmbedPageQueryDTO();

            String embedType = request.getEmbedType();
            pageQuery.setEmbedType(embedType);
            pageQuery.setIsPublishMobile(request.getIsPublishMobile());
            pageQuery.setStatusCode(StatusCodeEnum.ONLINE.getCode());

            if (StringUtil.isNotEmpty(request.getKeyWord())) {
                pageQuery.setKeyWord(request.getKeyWord());
            }
            List<AppEmbedDO> appEmbeds = appEmbedService.pageQuery(pageQuery);
            if (CollUtil.isEmpty(appEmbeds)) {
                return Response.ok(Lists.newArrayList());
            }

            Map<String, String> resources = objectAuthService.getAuthResourceList();

            List<AppEmbedDetail> appEmbedDetails = Lists.newLinkedList();

            for (AppEmbedDO appEmbed : appEmbeds) {
                //校验仪表板权限，并且补全数据
                if (AppEmbedTypeEnum.DASHBOARD.getCode().equals(appEmbed.getEmbedType())){
                    // filter by current user org code with org auth
                    Dashboard dashboard = dashboardDaoService.getById(appEmbed.getEmbedObjectId());
                    // 校验有权限的仪表板
                    if (isAuth(resources, dashboard, user)) {
                        AppEmbedDetail appEmbedDetail = getAppEmbedDetail(appEmbed,dashboard);
                        appEmbedDetails.add(appEmbedDetail);
                    }
                }
            }

            // 按照组织编码分组应用嵌入对象详情
            Map<String, List<AppEmbedResourceItem>> groupedMap = getGroupedMap(
                appEmbedDetails);

            //获取智加的所有组织
            List<Org> orgList = getOrgList();

            // 生成应用嵌入对象信息列表
            List<AppEmbedOrgGroupVO> appEmbedOrgGroupVos = organizeAppEmbedVosByOrg(
                orgList, groupedMap,embedType);

            return Response.ok(appEmbedOrgGroupVos);
        } finally {
            UserContextUtil.removeHandler();
        }
    }

    /**
     * 根据组织列表和按组织编码分组的应用嵌入对象详情映射，生成应用嵌入对象信息列表
     *
     * @param orgList 组织列表，包含多个组织的信息
     * @param groupedMap 按组织编码分组的应用嵌入对象详情映射，键为组织编码，值为应用嵌入对象详情列表
     * @return 应用嵌入对象信息列表
     */
    private List<AppEmbedOrgGroupVO> organizeAppEmbedVosByOrg(List<Org> orgList,
        Map<String, List<AppEmbedResourceItem>> groupedMap,String embedType) {
        List<AppEmbedOrgGroupVO> appEmbedOrgGroupVos = new LinkedList<>();
        //按照智加返回组织的顺序添加到最终list中
        orgList.forEach(org -> {
            AppEmbedOrgGroupVO appEmbedOrgGroupVO = new AppEmbedOrgGroupVO();
            List<AppEmbedResourceItem> appEmbedDetailList = groupedMap.get(org.getCode());
            if (CollUtil.isEmpty(appEmbedDetailList)) {
                return;
            }
            appEmbedOrgGroupVO.setOrgName(org.getName());
            appEmbedOrgGroupVO.setOrgCode(org.getCode());
            appEmbedOrgGroupVO.setEmbedType(embedType);
            appEmbedOrgGroupVO.setAppEmbedResourceItems(appEmbedDetailList);

            //如果是移动端优先展示的组织，将其添加到列表的开头
            if (org.getCode().equals(apolloRefreshConfig.getAppEmbedMobileOrderFirstOrgCode())) {
                appEmbedOrgGroupVos.add(0, appEmbedOrgGroupVO);
            }else {
                appEmbedOrgGroupVos.add(appEmbedOrgGroupVO);
            }
        });
        return appEmbedOrgGroupVos;
    }

    /**
     *
     * @param appEmbedDetails 应用嵌入对象列表请求对象，包含请求相关信息
     * @return 返回根据请求获取到的用户信息对象
     */
    private Map<String, List<AppEmbedResourceItem>> getGroupedMap(List<AppEmbedDetail> appEmbedDetails) {
        //对仪表板数据按照组织orgCode字段进行分组并排序
        // 1. 按 orgCode 分组，并对每组内部按 objectName 排序（处理 null 值）
        // 分组依据OrgCode
        // 内部排序依据ObjectName
        // 忽略大小写排序
        Map<String, List<AppEmbedResourceItem>> groupedMap = appEmbedDetails.stream()
            .collect(Collectors.groupingBy(
                AppEmbedDetail::getOrgCode,
                Collectors.collectingAndThen(
                    Collectors.toList(),
                    list -> list.stream()
                        .sorted(Comparator
                            .comparing(
                                AppEmbedDetail::getObjectName,
                                Comparator.nullsLast(String::compareToIgnoreCase)
                            )
                        )
                        .map(this::convertToEmbedObjectDetail)
                        .collect(Collectors.toList())
                )
            ));
        return groupedMap;
    }


    /**
     * 将AppEmbedDetail转换为AppEmbedObjectDetail
     * @param detail
     * @return
     */
    private AppEmbedResourceItem convertToEmbedObjectDetail(AppEmbedDetail detail) {
        AppEmbedResourceItem objectDetail = new AppEmbedResourceItem();
        // 属性复制
        objectDetail.setObjectId(detail.getObjectId());
        objectDetail.setObjectName(detail.getObjectName());
        objectDetail.setUrl(detail.getUrl());
        objectDetail.setOwnerName(detail.getOwnerName());
        objectDetail.setUpdateTime(detail.getUpdateTime());
        return objectDetail;
    }

    /**
     * 根据应用嵌入数据对象补全应用嵌入详情信息
     *
     * @param appEmbed 应用嵌入数据对象，包含应用嵌入的基本信息
     * @return 补全后的应用嵌入详情对象
     */
    private AppEmbedDetail getAppEmbedDetail(AppEmbedDO appEmbed,Dashboard dashboard) {
        //补全数据
        AppEmbedDetail appEmbedDetail = new AppEmbedDetail();
        appEmbedDetail.setObjectId(appEmbed.getEmbedObjectId());
        appEmbedDetail.setObjectName(dashboard.getName());
        appEmbedDetail.setOwnerName(dashboard.getOwnerEn());
        appEmbedDetail.setUpdateTime(DateUtil.format(Date.from(dashboard.getUpdatedAt())));

        // 应用嵌入跳转连接
        appEmbedDetail.setUrl(urlGenerateService.embedObjectUrl(appEmbed)
            + urlGenerateService.embedObjectUrlParamKey(appEmbed));

        appEmbedDetail.setEmbedType(appEmbed.getEmbedType());
        //组织信息来源于仪表板，不能来源于订阅任务
        appEmbedDetail.setOrgCode(dashboard.getOrgCode());
        return appEmbedDetail;
    }

    /**
     * 获取用户信息
     * @param request
     * @return
     */
    private UserInfo getUserInfo(AppEmbedObjectListRequest request) {
        UserInfo user = null;
        if(StringUtil.isNotEmpty(request.getEmail())){
            log.debug("email={}", request.getEmail());

            if(StringUtil.isNotEmpty(apolloRefreshConfig.getAppWhiteList())
                    && Arrays.asList(apolloRefreshConfig.getAppWhiteList().split(",")).contains(request.getEmail())){
                // 查询用户信息
                user = aiplusService.getUserInfoByEmail(request.getEmail());
            }else{
                // 解密出邮箱
                String decryptEmail = mobileEmailDecryptService.decryptEmail(request.getEmail());
                if(StringUtil.isNotEmpty(decryptEmail)){
                   // 查询用户信息,通过邮箱查询用户信息
                  user = aiPlusUserService.getUserInfoByExactMatchEmail(decryptEmail, true);
                }
            }
        }

        if(user==null){
            throw new BiException(UserErrorCode.ILLEGAL_ACCOUNT,"无效用户");
        }

        log.debug("当前用户为 user={}", JSONUtil.toJsonStr(user));
        return user;
    }

    private boolean isAuth(Map<String,String> resources,Dashboard dashboard, UserInfo user) {

        log.debug("isAuth dashboard id ={}", dashboard.getId());

        //是否管理员
        if (null != user.getIsManager() && user.getIsManager()){
            return true;
        }

        // 责任人
        if (dashboard.getOwnerEmail().equals(user.getEmail())) {
            return true;
        }

        // 资源授权信息
        String resourceKey = dashboard.getId() +"_"+AuthResourceTypeEnum.dashboard.name();
        // 最低查看权限就可以
        if(resources.containsKey(resourceKey)) {
            return true;
        }

        return false;
    }
}
