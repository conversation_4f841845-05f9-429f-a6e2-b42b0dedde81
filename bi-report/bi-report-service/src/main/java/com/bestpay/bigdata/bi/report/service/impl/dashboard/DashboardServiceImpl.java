package com.bestpay.bigdata.bi.report.service.impl.dashboard;

import static com.bestpay.bigdata.bi.common.constant.BIConstant.OBJECT_TYPE_DASHBOARD;
import static com.bestpay.bigdata.bi.common.constant.BIConstant.OBJECT_TYPE_DATASCREEN;
import static com.bestpay.bigdata.bi.common.enums.NewCardTypeEnum.EMBED_TEXT_INDEX;
import static java.util.Objects.requireNonNull;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.bean.AiPlusUserSearchRequest;
import com.bestpay.bigdata.bi.common.bean.MoveRequest;
import com.bestpay.bigdata.bi.common.common.ChartTypeEnum;
import com.bestpay.bigdata.bi.common.config.AuthConfig;
import com.bestpay.bigdata.bi.common.constant.BIConstant;
import com.bestpay.bigdata.bi.common.dto.dashboard.IndexCardQueryDTO;
import com.bestpay.bigdata.bi.common.dto.report.QueryReportConditionInfo;
import com.bestpay.bigdata.bi.common.entity.ColumnName;
import com.bestpay.bigdata.bi.common.entity.PermissionInfo;
import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.common.enums.CodeEnum;
import com.bestpay.bigdata.bi.common.enums.CookieNameEnum;
import com.bestpay.bigdata.bi.common.enums.IndexCardTypeEnum;
import com.bestpay.bigdata.bi.common.enums.NewCardTypeEnum;
import com.bestpay.bigdata.bi.common.enums.StatusCodeEnum;
import com.bestpay.bigdata.bi.common.enums.UserSourceEnum;
import com.bestpay.bigdata.bi.common.error.DashboardErrorCode;
import com.bestpay.bigdata.bi.common.error.UserErrorCode;
import com.bestpay.bigdata.bi.common.exception.BiException;
import com.bestpay.bigdata.bi.common.response.InsightAiResponse;
import com.bestpay.bigdata.bi.common.response.NorthStarResultResponse;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.common.util.AssertUtil;
import com.bestpay.bigdata.bi.common.util.CookiesUtil;
import com.bestpay.bigdata.bi.common.util.HttpUtils;
import com.bestpay.bigdata.bi.common.util.StringUtil;
import com.bestpay.bigdata.bi.common.util.UserContextUtil;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardCardService;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardConfigService;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardDaoService;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardDirectoryDAOService;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardIndexTextCardService;
import com.bestpay.bigdata.bi.database.bean.AuthOperateEnum;
import com.bestpay.bigdata.bi.database.bean.AuthResourceTypeEnum;
import com.bestpay.bigdata.bi.database.bean.dashboard.DashBoardConfigDO;
import com.bestpay.bigdata.bi.database.bean.dashboard.DashBoardConfigDTO;
import com.bestpay.bigdata.bi.database.bean.dashboard.Dashboard;
import com.bestpay.bigdata.bi.database.bean.dashboard.DashboardQuery;
import com.bestpay.bigdata.bi.database.bean.subscribe.ObjectSubScribeDO;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardCardDO;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardDirectoryDo;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardIndexTextCardDO;
import com.bestpay.bigdata.bi.report.enums.common.PermissionEnums;
import com.bestpay.bigdata.bi.report.enums.dashboard.DashboardTypeCodeEnum;
import com.bestpay.bigdata.bi.report.enums.dashboard.DisplayTypeEnum;
import com.bestpay.bigdata.bi.report.enums.report.FieldDisplayTypeEnum;
import com.bestpay.bigdata.bi.report.request.auth.ObjectAuthRequest;
import com.bestpay.bigdata.bi.report.request.dashboard.DashBoardCopyRequest;
import com.bestpay.bigdata.bi.report.request.dashboard.DashboardAnalysisByAIParamRequest;
import com.bestpay.bigdata.bi.report.request.dashboard.DashboardAnalysisByAIRequest;
import com.bestpay.bigdata.bi.report.request.dashboard.DashboardPublishRequest;
import com.bestpay.bigdata.bi.report.request.dashboard.MoveDashboardRequest;
import com.bestpay.bigdata.bi.report.request.tablecard.TableCardDataRequest;
import com.bestpay.bigdata.bi.report.response.common.QueryIndexAndReportResponse;
import com.bestpay.bigdata.bi.report.response.dashboard.DashboardAnalysisByAIResponse;
import com.bestpay.bigdata.bi.report.response.dashboard.DashboardDirectoryResponse;
import com.bestpay.bigdata.bi.report.response.dashboard.DashboardResponse;
import com.bestpay.bigdata.bi.report.response.dashboard.DashboardVo;
import com.bestpay.bigdata.bi.report.service.auth.ObjectAuthService;
import com.bestpay.bigdata.bi.report.service.dashboard.DashboardCardDataQueryService;
import com.bestpay.bigdata.bi.report.service.dashboard.DashboardService;
import com.bestpay.bigdata.bi.report.service.handler.DashboardHandler;
import com.bestpay.bigdata.bi.report.service.handler.IndexTextDashboardHandler;
import com.bestpay.bigdata.bi.report.service.report.AccessStatisticsService;
import com.bestpay.bigdata.bi.report.service.subscribe.DashboardSubService;
import com.bestpay.bigdata.bi.report.util.DashboardTableCardRedisUtil;
import com.bestpay.bigdata.bi.report.util.JsoupUtil;
import com.bestpay.bigdata.bi.thirdparty.api.AiPlusUserService;
import com.bestpay.bigdata.bi.thirdparty.api.AiplusService;
import com.facebook.presto.jdbc.internal.google.api.client.util.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.opencsv.CSVWriter;
import java.io.StringWriter;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * @author: laiyao
 * @date: 2022/07/26
 */
@Slf4j
@Service
public class DashboardServiceImpl implements DashboardService {

  public static final String DASHBOARD_DETAIL_PAGE = "detail";
  @Resource
  private DashboardDaoService dashboardDaoService;
  @Resource
  private AiplusService aiplusService;
  @Resource
  private DashboardDirectoryDAOService directoryService;
  @Resource
  private DashboardSubService dashboardSubService;
  @Resource
  private AccessStatisticsService statisticsService;
  @Resource
  private AiPlusUserService aiPlusUserService;
  @Resource
  private ObjectAuthService objectAuthService;
  @Resource
  private DashboardConfigService dashboardConfigService;
  @Resource
  private DashboardCardService dashboardCardService;
  @Resource
  private Map<String, DashboardHandler> dashboardHandlerMap;
  @Resource
  private DashboardConfigService configService;
  @Resource
  private DashboardIndexTextCardService indexTextCardService;
  @Resource
  private IndexTextDashboardHandler indexTextDashboardHandler;
  @Resource
  private AuthConfig authConfig;

  @Override
  public Response<Long> insert(Dashboard dashboard) {

    DashboardQuery filter = new DashboardQuery();
    filter.setName(dashboard.getName());
    filter.setDisplayType(dashboard.getDisplayType());
    List<Dashboard> dashboards = dashboardDaoService.queryByCondition(filter);
    if (Objects.equals(DisplayTypeEnum.DASHBOARD.getCode(), dashboard.getDisplayType())) {
      if (!CollectionUtils.isEmpty(dashboards)) {
        log.warn("insert dashboard failed. the dashboard name:{} already exists",
            dashboard.getName());
        return Response.error(CodeEnum.DASHBOARD_EXISTS);
      }
    }

    // for dashboard and data screen
    Long maxDirSort = dashboardDaoService.getMaxDirSort(dashboard.getDisplayType(),
        dashboard.getDirId());
    Long next = Optional.ofNullable(maxDirSort).orElse(0L) + 1;

    dashboard.setOrderNum(next);

    Instant now = Instant.now();
    dashboard.setCreatedAt(now);
    dashboard.setUpdatedAt(now);

    UserInfo user = aiplusService.getUserInfoByEmail(dashboard.getOwnerEmail());
    dashboard.setOwner(user.getNickName());
    dashboard.setOwnerEn(user.getAccount());

    if (Objects.isNull(dashboard.getTypeCode())) {
      dashboard.setTypeCode(DashboardTypeCodeEnum.DASHBOARD.getCode());
    }
    try {
      Long id = dashboardDaoService.insert(dashboard);
      return Response.ok(id);
    } catch (Exception e) {
      log.warn("insert dashboard failed. the dashboard name:{} already exists", dashboard.getName(),
          e);
      throw e;
    }
  }


  @Override
  public Response<List<DashboardVo>> query(DashboardQuery dashboardQuery,
      HttpServletRequest request) {
    // get user role from aiPlus
    String cookieValue = CookiesUtil.getCookieValue(request.getCookies(),
        CookieNameEnum.BIGDATA_AI_PLUS_USER_ID.code());

    UserInfo userInfo = UserContextUtil.getUserInfo();
    log.info("userInfo content is {}", userInfo);

    if (Objects.isNull(userInfo) || Objects.isNull(userInfo.getOrg().getCode())) {
      throw new BiException(UserErrorCode.ILLEGAL_ACCOUNT,"账号信息异常，请求失败");
    }

    //get user org from aiplus
    Boolean isManager = aiplusService.getCurrentUserPermission(cookieValue);
    log.info("get current user permission in BI, plat permission isManager : {}", isManager);

    if (DisplayTypeEnum.DASHBOARD.getCode().equals(dashboardQuery.getDisplayType())) {
      return Response.ok(getDashBoardList(isManager, dashboardQuery));
    } else if (DisplayTypeEnum.DATA_BIG_SCREEN.getCode().equals(dashboardQuery.getDisplayType())) {
      return Response.ok(getDataScreenList(isManager, dashboardQuery, true));
    }

    return Response.ok(Collections.emptyList());
  }

  /**
   * no-tree structure , just dashboard list
   *
   * @param dashboardQuery
   * @return
   */
  @Override
  public Response<List<DashboardVo>> queryList(DashboardQuery dashboardQuery) {
    UserInfo userInfo = UserContextUtil.getUserInfo();
    log.info("userInfo content is {}", userInfo);
    AssertUtil.notNull(userInfo, CodeEnum.ILLEGAL_ACCOUNT);
    AssertUtil.notNull(userInfo.getOrg().getCode(), CodeEnum.ILLEGAL_ACCOUNT);

    //get user org from aiplus
    Boolean isManager = aiplusService.getCurrentUserPermission(userInfo.getCookieId());
    log.info("get current user permission in BI, plat permission isManager : {}", isManager);

    Map<String, String> resources = objectAuthService.getAuthResourceList();
    if (DisplayTypeEnum.DASHBOARD.getCode().equals(dashboardQuery.getDisplayType())) {
      List<DashboardVo> children = getChildren(isManager, dashboardQuery, resources, false);
      children = children.stream().filter(c -> c.getStatusCode() == StatusCodeEnum.ONLINE.getCode())
          .collect(Collectors.toList());
      return Response.ok(children);
    } else if (DisplayTypeEnum.DATA_BIG_SCREEN.getCode().equals(dashboardQuery.getDisplayType())) {
      return Response.ok(getDataScreenList(isManager, dashboardQuery, false));
    }
    return Response.ok(Collections.emptyList());
  }

  /**
   * get dashboard list by auth
   *
   * @param isManager is platform manager
   * @return
   */
  private List<DashboardVo> getDashBoardList(boolean isManager,
      DashboardQuery dashboardQuery) {
    // 仪表板目录
    List<DashboardDirectoryDo> directoryDoList
        = directoryService.queryDashboardDirectoryList(DashboardDirectoryDo.builder().build());

    HashMap<Long, List<DashboardDirectoryDo>> parentIdDirectoryMap = new HashMap<>();
    Map<String, String> resources = objectAuthService.getAuthResourceList();

    List<DashboardDirectoryDo> rootDirectoryList = new ArrayList<>();
    for (DashboardDirectoryDo directoryDo : directoryDoList) {
      Long parentId = directoryDo.getParentId();
      // 根目录
      if (Objects.isNull(parentId)) {
        rootDirectoryList.add(directoryDo);
      } else {
        // 子目录
        List<DashboardDirectoryDo> childDirectoryList = parentIdDirectoryMap.getOrDefault(parentId,
            new ArrayList<>());
        childDirectoryList.add(directoryDo);
        childDirectoryList = childDirectoryList.stream()
            .sorted(Comparator.comparing(DashboardDirectoryDo::getDirSort))
            .collect(Collectors.toList());
        parentIdDirectoryMap.put(parentId, childDirectoryList);
      }
    }

    rootDirectoryList = rootDirectoryList.stream()
        .sorted(Comparator.comparing(DashboardDirectoryDo::getDirSort))
        .collect(Collectors.toList());

    return generateDashboardTreeList(isManager,
        rootDirectoryList,
        parentIdDirectoryMap,
        dashboardQuery,
        resources);
  }


  /**
   * @param isManager            is aiplus manager
   * @param childDirectoryList   rootDirectory list
   * @param parentIdDirectoryMap rootId map to childList
   * @return
   */
  private List<DashboardVo> generateDashboardTreeList(
      boolean isManager,
      List<DashboardDirectoryDo> childDirectoryList,
      HashMap<Long, List<DashboardDirectoryDo>> parentIdDirectoryMap,
      DashboardQuery dashboardQuery,
      Map<String, String> resources) {

    String keyword = dashboardQuery.getKeyword();
    String orgCode = dashboardQuery.getOrgCode();

    List<DashboardVo> list = CollUtil.newArrayList();

    for (DashboardDirectoryDo rootDirectory : childDirectoryList) {

      List<DashboardDirectoryDo> nextChildDirectoryList = parentIdDirectoryMap.get(
          rootDirectory.getId());
      List<DashboardVo> childTreeList = null;
      // 递归遍历子目录
      if (CollUtil.isNotEmpty(nextChildDirectoryList)) {
        childTreeList = generateDashboardTreeList(isManager,
            nextChildDirectoryList,
            parentIdDirectoryMap,
            dashboardQuery,
            resources);
      }

      dashboardQuery.setDirId(rootDirectory.getId());

      // 获取当前目录下的仪表板
      List<DashboardVo> children = getChildren(isManager,
          dashboardQuery,
          resources,
          true);

      if (CollUtil.isNotEmpty(childTreeList)) {
        childTreeList.addAll(children);
        children = new ArrayList<>(childTreeList);
      }

      // directory
      if (isManager
          || rootDirectory.getCreatedBy().equals(UserContextUtil.getUserInfo().getEmail())
          || CollUtil.isNotEmpty(children)) {

        DashboardVo dashboardVo = new DashboardVo();
        UserInfo userInfo = getUserInfo(rootDirectory.getCreatedBy());
        dashboardVo.setOwnerName(userInfo != null ? userInfo.getAccount() : "unknown");

        // use filter condition, and children is empty, we need skip it
        if ((StringUtil.isNotEmpty(orgCode) || StringUtil.isNotEmpty(keyword))
            && CollectionUtils.isEmpty(children)) {
          continue;
        }

        dashboardVo.setId(rootDirectory.getId().toString());
        dashboardVo.setLabel(rootDirectory.getName());
        dashboardVo.setOrderNum(rootDirectory.getDirSort());
        dashboardVo.setOrgCode(rootDirectory.getOrgCode());
        dashboardVo.setChildren(children);
        dashboardVo.setType("directory");
        list.add(dashboardVo);
      }
    }
    return list;
  }

  private UserInfo getUserInfo(String email) {
    AiPlusUserSearchRequest request = new AiPlusUserSearchRequest();
    request.setOwnerNames(Lists.newArrayList(email));
    List<UserInfo> users = aiPlusUserService.getUserList(request);
    if (CollUtil.isNotEmpty(users)) {
      return users.get(0);
    }
    return null;
  }


  /**
   * get data screen list by auth
   *
   * @param isManager      is platform manager
   * @param dashboardQuery
   * @return
   */
  private List<DashboardVo> getDataScreenList(boolean isManager,
      DashboardQuery dashboardQuery,
      boolean isCheckAuth) {

    List<DashboardVo> dashboardVos = new ArrayList<>();
    List<Dashboard> dashboards = dashboardDaoService.queryByCondition(dashboardQuery);
    log.info("dashboard list size {}", dashboards.size());
    if (CollectionUtils.isEmpty(dashboards)) {
      return Collections.emptyList();
    }

    List<Dashboard> filterDashboards = new ArrayList<>();
    Map<Long, String> dashboardIdAuthMap = new HashMap<>();

    // 获取用户有权限的数据大屏及对应的权限
    getDataScreenByAuth(isManager, dashboards, filterDashboards, dashboardIdAuthMap, isCheckAuth);

    for (Dashboard dashboard : filterDashboards) {
      String operate
          = isManager ? AuthOperateEnum.edit.name()
          : dashboardIdAuthMap.get(dashboard.getId());

      DashboardVo vo = new DashboardVo();
      vo.setId(dashboard.getId().toString());
      vo.setStatusCode(dashboard.getStatusCode());
      vo.setLabel(dashboard.getName());
      vo.setOwnerName(dashboard.getOwnerEn());
      vo.setChildren(ListUtil.empty());
      vo.setType("dashboard");
      vo.setAuthOperate(StringUtils.isNotBlank(operate) ? operate : AuthOperateEnum.no.name());
      dashboardVos.add(vo);
    }
    return dashboardVos;
  }

  /**
   * @param isManager          是否是管理员
   * @param dashboards         带过滤的数据大屏列表
   * @param dashboardIdAuthMap 某用户的仪表板，对应授权等级（查看or编辑）
   * @param filterDashboards   过滤后的有权的仪表板
   * @param isCheckAuth        是否校验权限
   */
  private void getDataScreenByAuth(boolean isManager,
      List<Dashboard> dashboards,
      List<Dashboard> filterDashboards,
      Map<Long, String> dashboardIdAuthMap,
      boolean isCheckAuth) {

    if (!isManager && isCheckAuth) {
      // 资源授权信息
      Map<String, String> resources = objectAuthService.getAuthResourceList();
      // auth check
      for (Dashboard dashboard : dashboards) {
        if (dashboard.getOwnerEmail().equals(UserContextUtil.getUserInfo().getEmail())) {
          filterDashboards.add(dashboard);
          dashboardIdAuthMap.put(dashboard.getId(), AuthOperateEnum.edit.name());
        } else {
          ObjectAuthRequest authRequest = new ObjectAuthRequest();
          authRequest.setAuthResourceId(dashboard.getId().toString());
          authRequest.setAuthResourceType(AuthResourceTypeEnum.dashboard.name());
          String resourceKey = dashboard.getId().toString()
              + "_" + AuthResourceTypeEnum.dashboard.name();

          if (resources.containsKey(resourceKey)) {
            filterDashboards.add(dashboard);
            dashboardIdAuthMap.put(dashboard.getId(), resources.get(resourceKey));
          }
        }
      }
    } else {
      filterDashboards.addAll(dashboards);
    }
  }


  /**
   * 应前端要求将树形目录第一层级Id设为唯一 解决方案：children层id前都加上‘#’
   */
  private List<DashboardVo> getChildren(boolean isManager,
      DashboardQuery dashboardQuery,
      Map<String, String> resources,
      boolean isCheckAuth) {
    UserInfo userInfo = UserContextUtil.getUserInfo();
    Map<String, Boolean> modifyPermissionMap = new HashMap<>(1);
    for (PermissionInfo permissionInfo : userInfo.getPermissionInfoList()) {
      if (permissionInfo.getPermissionLevel()
          .equals(PermissionEnums.PERMISSION_LEVEL_MODIFY.getCode())
          && BIConstant.PAGE_BI_DASHBOARD_RESOURCE.equals(permissionInfo.getResource())) {
        modifyPermissionMap.put(BIConstant.MODIFY_PERMISSION, Boolean.TRUE);
      }
    }
    List<Dashboard> dashboards = dashboardDaoService.queryByCondition(dashboardQuery);
    // judge auth
    Map<Long, String> dashboardIdAuthMap = new HashMap<>();
    List<Dashboard> newDashboardList = Lists.newArrayList();

    // 获取有权限的仪表板，及权限
    getDashBoardByAuthAnd(isManager, resources,
        dashboards, dashboardIdAuthMap, newDashboardList, isCheckAuth);

    List<DashboardVo> children = CollUtil.newArrayList();
    if (CollUtil.isNotEmpty(newDashboardList)) {
      for (Dashboard dashboard : newDashboardList) {
        DashboardVo vo = new DashboardVo();
        vo.setId(dashboard.getId().toString());
        vo.setLabel(dashboard.getName());
        vo.setStatusCode(dashboard.getStatusCode());
        vo.setIsPublishMobile(dashboard.getIsPublishMobile());

        // 获取用户账户
        AiPlusUserSearchRequest request = new AiPlusUserSearchRequest();
        request.setOwnerNames(Lists.newArrayList(dashboard.getOwnerEmail()));
        List<UserInfo> userInfos = aiPlusUserService.getUserList(request);
        vo.setOwnerName(CollUtil.isNotEmpty(userInfos) ? userInfos.get(0).getAccount() : "unknown");
        vo.setOrderNum(dashboard.getOrderNum());
        vo.setType("dashboard");

        if (!isManager) {
          vo.setAuthOperate(dashboardIdAuthMap.get(dashboard.getId()));
        } else {
          vo.setAuthOperate(AuthOperateEnum.edit.name());
        }
        // 状态如果是待发布
        if (dashboard.getStatusCode() == StatusCodeEnum.OFFLINE.getCode()) {
          // 如果不是管理员
          if (!isManager) {
            Boolean modifyPermissionFlag = modifyPermissionMap.get(BIConstant.MODIFY_PERMISSION);
            // 如果没有智加编辑权限
            if (Objects.isNull(modifyPermissionFlag)) {
              //  如果没有BI编辑权限
              if (!AuthOperateEnum.edit.name().equals(vo.getAuthOperate())) {
                if (!dashboard.getOwnerEmail().equals(userInfo.getEmail())) {
                  continue;
                }

              }
            }
          }
        }
        children.add(vo);
      }
    }
    return children;
  }

  /**
   * @param isManager          是否是管理员
   * @param resources          资源授权信息
   * @param dashboards         带过滤的仪表板列表
   * @param dashboardIdAuthMap 某用户的仪表板，对应授权等级（查看or编辑）
   * @param newDashboardList   过滤后的有权的仪表板
   * @param isCheckAuth        是否校验权限
   *
   *  批量对仪表板进行权限校验
   *      1. 管理员无需校验
   *      2. 非管理员，且isCheckAuth=false，无需校验
   *      3. 非管理员，且isCheckAuth=true， 根据当前用户是否仪表板的ownerEmail进行校验
   *      4. 非管理员，且isCheckAuth=true，且资源授权信息不为空，且仪表板的ownerEmail在资源授权信息中
   */
  private void getDashBoardByAuthAnd(boolean isManager,
      Map<String, String> resources,
      List<Dashboard> dashboards,
      Map<Long, String> dashboardIdAuthMap,
      List<Dashboard> newDashboardList,
      boolean isCheckAuth) {

    if (!isManager && isCheckAuth) {
      for (Dashboard dashboard : dashboards) {
        if (dashboard.getOwnerEmail().equals(UserContextUtil.getUserInfo().getEmail())) {
          newDashboardList.add(dashboard);
          dashboardIdAuthMap.put(dashboard.getId(), AuthOperateEnum.edit.name());
        } else {
          String resourceKey = dashboard.getId().toString()
              + "_" + AuthResourceTypeEnum.dashboard.name();

          if (resources.containsKey(resourceKey)) {
            newDashboardList.add(dashboard);
            dashboardIdAuthMap.put(dashboard.getId(), resources.get(resourceKey));
          }
        }
      }
    } else {
      newDashboardList.addAll(dashboards);
    }
  }

  @Override
  @Transactional
  public Response<Boolean> deleteById(Long id, String email) {

    // 删除仪表板/数据大屏
    Dashboard dashboard = new Dashboard();
    dashboard.setId(id);
    dashboard.setStatusCode(StatusCodeEnum.DELETE.getCode());
    dashboard.setUpdatedAt(Instant.now());
    dashboard.setUpdatedBy(email);
    dashboardDaoService.updateById(dashboard);

    DashBoardConfigDTO build = DashBoardConfigDTO.builder()
        .dashboardId(id)
        .statusCode(StatusCodeEnum.DELETE.getCode())
        .updatedBy(email)
        .build();

    dashboardConfigService.updateStatus(build);

    // 删除订阅
    ObjectSubScribeDO objectSubScribeDO = new ObjectSubScribeDO();
    objectSubScribeDO.setObjectId(id);
    objectSubScribeDO.setStatusCode(StatusCodeEnum.DELETE.getCode());
    objectSubScribeDO.setUpdatedBy(email);
    objectSubScribeDO.setUpdatedAt(new Date());
    dashboardSubService.updateByDashboardId(objectSubScribeDO);
    return Response.ok();
  }

  @Override
  public Response<Dashboard> queryById(Long id, String source) {
    Dashboard dashboard = dashboardDaoService.getById(id);

    if (dashboard == null) {
      return Response.ok();
    }

    if (DASHBOARD_DETAIL_PAGE.equals(source)) {
      String objectType = Objects.equals(DisplayTypeEnum.DASHBOARD.getCode(),
          dashboard.getDisplayType()) ? OBJECT_TYPE_DASHBOARD : OBJECT_TYPE_DATASCREEN;

      Map<Long, Long> accessCountMobileMap = statisticsService
          .countByType(objectType, Lists.newArrayList(id), UserSourceEnum.MOBILE.getCode());

      Map<Long, Long> accessCountMap = statisticsService
          .countByType(objectType, Lists.newArrayList(id), UserSourceEnum.PC.getCode());

      // 仪表板|数据大屏访问量 - pc
      dashboard.setUserAccessCount(
          accessCountMap.get(id) == null ? 0L : accessCountMap.get(id));

      // 仪表板|数据大屏访问量 - mobile
      dashboard.setUserAccessMobileCount(
          accessCountMobileMap.get(id) == null ? 0L : accessCountMobileMap.get(id));
    }

    try {
      UserInfo info = aiplusService.getUserInfoByEmail(dashboard.getOwnerEmail());
      dashboard.setOwnerName(info.getAccount() != null ? info.getAccount() : "unknown");
    } catch (Exception e) {
      dashboard.setOwnerName("unknown");
      dashboard.setOwnerEmail("unknown");
    }


    return Response.ok(dashboard);
  }


  @Override
  public Response<Boolean> updateById(Dashboard dashboard) {
    if (StringUtils.isNotBlank(dashboard.getName())) {
      DashboardQuery filter = new DashboardQuery();
      filter.setName(dashboard.getName());
      filter.setDisplayType(dashboard.getDisplayType());
      List<Dashboard> dashboards = dashboardDaoService.queryByCondition(filter);
      if (Objects.equals(DisplayTypeEnum.DASHBOARD.getCode(), dashboard.getDisplayType())) {
        if (!CollectionUtils.isEmpty(dashboards)) {
          Optional<Dashboard> first = dashboards.stream()
              .filter(record -> !record.getId().equals(dashboard.getId())).findFirst();
          if (first.isPresent()) {
            log.warn("insert dashboard failed. the dashboard name:{} already exists",
                dashboard.getName());
            return Response.error(CodeEnum.DASHBOARD_EXISTS);
          }
        }
      }
    }

    UserInfo user = aiplusService.getUserInfoByEmail(dashboard.getOwnerEmail());
    dashboard.setOwner(user.getNickName());
    dashboard.setOwnerName(user.getAccount());
    dashboard.setOwnerEn(user.getAccount());

    try {
      dashboardDaoService.updateById(dashboard);
    } catch (Exception e) {
      log.warn("update dashboard failed. the dashboard name:{} already exists", dashboard.getName(),
          e);
      return Response.error(CodeEnum.DASHBOARD_DATA_ERROR);
    }

    return Response.ok();
  }


  @Override
  public Response<Boolean> moveDashboardLocation(MoveDashboardRequest moveDashboardRequest,
      HttpServletRequest httpServletRequest) {

    //check param
    List<MoveRequest> postMoveSortList = moveDashboardRequest.getPostMoveSortList();

    if (CollectionUtils.isEmpty(postMoveSortList)) {
      return Response.error(CodeEnum.DASHBOARD_MOVE_PARAM_ERROR);
    }

    log.info("move Dashboard Location, postMoveSortList : {}", postMoveSortList);
    // move
    for (int i = 0; i < postMoveSortList.size(); i++) {
      Dashboard dashboard = new Dashboard();
      dashboard.setId(postMoveSortList.get(i).getId());
      dashboard.setOrderNum(postMoveSortList.get(i).getOrderNum());
      dashboard.setDirId(postMoveSortList.get(i).getDirId());
      dashboardDaoService.updateById(dashboard);
    }

    return Response.ok(true);
  }


  @Override
  public Response<List<DashboardDirectoryResponse>> queryDashboardDirectoryList(
      Dashboard dashboard) {

    requireNonNull(dashboard.getDisplayType(), "display type can not be null");
    requireNonNull(dashboard.getIsPublishMobile(), "isPublishMobile can not be null");
    DashboardQuery filter = new DashboardQuery();
    filter.setIsPublishMobile(dashboard.getIsPublishMobile());
    filter.setDisplayType(dashboard.getDisplayType());

    List<Dashboard> dashboards = dashboardDaoService.queryByCondition(filter);

    List<DashboardDirectoryResponse> directoryResponses = new ArrayList<>();
    List<Long> idList = new ArrayList<>();

    for (Dashboard db : dashboards) {
      DashboardDirectoryResponse response = new DashboardDirectoryResponse();
      if (!idList.contains(db.getDirId())) {
        response.setDirectoryId(db.getDirId());
        response.setDirectoryName(db.getDirName());
        directoryResponses.add(response);
        idList.add(db.getDirId());
      }
    }
    return Response.ok(directoryResponses);
  }


  @Override
  public Response<List<DashboardResponse>> queryDashboardListByDirId(Dashboard dashboard) {
    requireNonNull(dashboard.getDisplayType(), "display type can not be null");
    requireNonNull(dashboard.getIsPublishMobile(), "isPublishMobile can not be null");
    requireNonNull(dashboard.getDirId(), "dirId can not be null");

    DashboardQuery filter = new DashboardQuery();
    filter.setIsPublishMobile(dashboard.getIsPublishMobile());
    filter.setDisplayType(dashboard.getDisplayType());
    filter.setDirId(dashboard.getDirId());

    List<Dashboard> dashboards = dashboardDaoService.queryByCondition(filter);

    List<DashboardResponse> dashboardResponses = new ArrayList<>();

    for (Dashboard db : dashboards) {
      DashboardResponse response = new DashboardResponse();
      response.setDashboardId(db.getId());
      response.setDashboardName(db.getName());
      dashboardResponses.add(response);
    }

    return Response.ok(dashboardResponses);
  }

  @Override
  public Integer publish(DashboardPublishRequest request) {
    Dashboard dashboard = buildDashboard(request.getId(), StatusCodeEnum.ONLINE);
    dashboard.setIsPublishMobile(request.getIsPublishMobile());
    int byId = dashboardDaoService.updateById(dashboard);
    AssertUtil.isTrue(byId > 0, CodeEnum.DASHBOARD_NOT_EXISTS);
    return byId;
  }

  @Override
  public Integer offLine(Long id) {
    int byId = dashboardDaoService.updateById(buildDashboard(id, StatusCodeEnum.OFFLINE));
    AssertUtil.isTrue(byId > 0, CodeEnum.DASHBOARD_NOT_EXISTS);
    return byId;
  }

  @Transactional
  @Override
  public Long copy(DashBoardCopyRequest request) {
    /**
     * 1、复制主表信息：t_dashboard
     */
    Long oldDashboardId = request.getId();
    Dashboard dashboard = copyDashboardMainInfo(request, oldDashboardId);

    /**
     * 2、new dashboard id
     */
    Long newDashboardId = dashboard.getId();

    /**
     * 3、复制样式信息
     */
    DashBoardConfigDO newConfig = configService.queryByDashboardId(oldDashboardId);
    newConfig.setDashboardId(newDashboardId);
    configService.insert(newConfig);

    /**
     * 4、复制引用指标：t_dashboard_index_text_card， index_card_type：embedText
     */
    Map<Long, Long> embedTextIdMap = copyEmbedText(oldDashboardId, newDashboardId);

    /**
     * 5、复制卡片信息
     */
    List<DashboardCardDO> dashboardCardDOList
        = dashboardCardService.findById(oldDashboardId, null, null);

    if (CollUtil.isEmpty(dashboardCardDOList)) {
      return newDashboardId;
    }

    /**
     * 5.1、复制卡片信息: t_dashboard_card
     */
    // key是老的PID value是新的PID
    Map<Long, Long> pIdMap = new HashMap<>();

    // 卡片主信息
    copyDashboardCardInfo(newDashboardId, dashboardCardDOList, pIdMap);

    /**
     * 5.2、复制卡片详细信息，各个卡片详细表
     */
    // 卡片详细信息
    copyCardDetailInfo(oldDashboardId, newDashboardId, embedTextIdMap, dashboardCardDOList, pIdMap);
    return newDashboardId;
  }

  private void copyCardDetailInfo(Long oldDashboardId, Long newDashboardId, Map<Long, Long> embedTextIdMap,
        List<DashboardCardDO> dashboardCardDOList, Map<Long, Long> pIdMap) {
        //   插入的集合 要判断其他卡片要在filter之前插入
        List<DashboardCardDO> childDashboardCardDOList = sortDashboardCardDOList(
            dashboardCardDOList);
        Map<String, String> cardCodeMap = Maps.newHashMap();

        for (DashboardCardDO dashboardCardDO : childDashboardCardDOList) {
          // new code
          String newCardCode = DashboardTableCardRedisUtil.getCardCode(dashboardCardDO
              .getCardType());

          cardCodeMap.put(dashboardCardDO.getCardUniqueKey(), newCardCode);

          DashboardHandler dashboardHandler
              = dashboardHandlerMap.get(dashboardCardDO.getCardType());

          // -1L 代表的是不改变父层
          Long newCardId = dashboardHandler.copyAndInsert(oldDashboardId,
              dashboardCardDO.getCardUniqueKey(),
              dashboardCardDO.getCardId(),
              newDashboardId,
              cardCodeMap,
              embedTextIdMap,
              null,IndexCardTypeEnum.EMBED_DASHBOARD.getCode());

          dashboardCardDO.setCardUniqueKey(newCardCode);
          dashboardCardDO.setCardId(newCardId);
          dashboardCardDO.setDashboardId(newDashboardId);

          // 赋值一些基础属性
          dashboardCardBaseAttributeAssignment(dashboardCardDO);
          dashboardCardDO.setPid(pIdMap.get(dashboardCardDO.getPid()));
        }

        dashboardCardService.batchInsert(childDashboardCardDOList);
    }

    private void copyDashboardCardInfo(Long newDashboardId,
        List<DashboardCardDO> dashboardCardDOList,
        Map<Long, Long> pIdMap) {

    List<DashboardCardDO> parentDashboardCardList = dashboardCardDOList.stream()
        .filter(d -> NewCardTypeEnum.TAB.getCode().equals(d.getCardType()))
        .collect(Collectors.toList());

    for (DashboardCardDO dashboardCardDO : parentDashboardCardList) {
      DashboardCardDO newCardDo = new DashboardCardDO();
      BeanUtils.copyProperties(dashboardCardDO, newCardDo);
      Long oldPid = dashboardCardDO.getId();
      // 赋值一些基础属性
      newCardDo.setDashboardId(newDashboardId);
      dashboardCardBaseAttributeAssignment(newCardDo);
      Long newPid = dashboardCardService.insert(newCardDo);
      pIdMap.put(oldPid, newPid);
    }
  }

  private Dashboard copyDashboardMainInfo(DashBoardCopyRequest request, Long oldDashboardId) {
    Dashboard dashboard = dashboardDaoService.getById(oldDashboardId);
    AssertUtil.notNull(dashboard, CodeEnum.DASHBOARD_NOT_EXISTS);
    Integer displayType = dashboard.getDisplayType();
    String displayTypeName = DisplayTypeEnum.getName(displayType);
    AssertUtil.notNull(displayTypeName, CodeEnum.DASHBOARD_DISPLAY_TYPE_ERROR);
    String dashboardName = request.getDashboardName();
    if (Objects.equals(DisplayTypeEnum.DASHBOARD.getCode(), displayType)) {
      Long dirId = request.getDirId();
      AssertUtil.notNull(dirId, CodeEnum.DASHBOARD_DIR_NOT_BLANK);
      DashboardDirectoryDo dashboardDirectoryDo = directoryService.queryDbById(dirId);
      AssertUtil.notNull(dashboardDirectoryDo, CodeEnum.DASHBOARD_DIR_ID_NOT_EXIST);
      // 校验是否存在仪表板名称
      isExistDashboardName(dashboardName, displayType);
      dashboard.setDirId(dirId);
    }
    dashboard.setName(dashboardName);
    // 属性赋值
    dashboardAttributeAssignment(dashboard);
    // 一定要在这里去insert  要不然下面这一行拿到的就是老得ID
    dashboardDaoService.insert(dashboard);
    return dashboard;
  }

  private Map<Long, Long> copyEmbedText(Long oldDashboardId, Long newDashboardId) {
    IndexCardQueryDTO queryDTO = new IndexCardQueryDTO();
    queryDTO.setDashboardId(oldDashboardId);
    queryDTO.setIndexCardType(IndexCardTypeEnum.EMBED_TEXT.getCode());

    // 引入文本
    List<DashboardIndexTextCardDO> embedTextList = indexTextCardService.find(queryDTO);

    Map<Long, Long> embedTextIdMap = Maps.newHashMap();
    for (DashboardIndexTextCardDO indexTextCardDO : embedTextList) {
      // 引用指标和指标文存在一张表里面
      Long newIndexTextId = indexTextDashboardHandler.copyAndInsert(oldDashboardId,
          null,
          indexTextCardDO.getId(),
          newDashboardId,
          Maps.newHashMap(),
          Maps.newHashMap(),
          null, EMBED_TEXT_INDEX.getCode());
      embedTextIdMap.put(indexTextCardDO.getId(), newIndexTextId);
    }
    return embedTextIdMap;
  }


  /**
   * 排序
   *
   * @param dashboardCardDOList cards
   * @return 其他不问，如果有。则先插入 indexText  text filter 这三个进行顺序插入
   */
  private List<DashboardCardDO> sortDashboardCardDOList(List<DashboardCardDO> dashboardCardDOList) {
    List<DashboardCardDO> childDashboardCardDOList = new ArrayList<>();
    List<DashboardCardDO> filterChildDashboardCardDOList = new ArrayList<>();
    for (DashboardCardDO d : dashboardCardDOList) {
      if (!NewCardTypeEnum.TAB.getCode().equals(d.getCardType())
          && !NewCardTypeEnum.FILTER.getCode().equals(d.getCardType())) {
        childDashboardCardDOList.add(d);
      }
      if (NewCardTypeEnum.FILTER.getCode().equals(d.getCardType())) {
        filterChildDashboardCardDOList.add(d);
      }
    }
    Collections.sort(childDashboardCardDOList, Comparator.comparing(DashboardCardDO::getCardType));
    childDashboardCardDOList.addAll(filterChildDashboardCardDOList);
    return childDashboardCardDOList;
  }

  /**
   * 赋值一些基础属性
   *
   * @param dashboardCardDO 卡片信息
   */
  private void dashboardCardBaseAttributeAssignment(DashboardCardDO dashboardCardDO) {
    // 时间  数据库已经有默认的了
    UserInfo userInfo = UserContextUtil.get();
    String email = userInfo.getEmail();
    dashboardCardDO.setId(null);
    dashboardCardDO.setCreatedBy(email);
    dashboardCardDO.setUpdatedBy(email);
    dashboardCardDO.setStatusCode(StatusCodeEnum.ONLINE.getCode());
  }

  /**
   * 属性赋值
   *
   * @param dashboard 仪表板对象
   */
  private void dashboardAttributeAssignment(Dashboard dashboard) {
    dashboard.setId(null);
    dashboard.setStatusCode(StatusCodeEnum.OFFLINE.getCode());
    UserInfo userInfo = UserContextUtil.get();
    String email = userInfo.getEmail();
    dashboard.setCreatedBy(email);
    dashboard.setUpdatedBy(email);
    dashboard.setOwner(userInfo.getNickName());
    dashboard.setOwnerEmail(email);
    dashboard.setOwnerEn(userInfo.getAccount());
    dashboard.setUpdatedAt(Instant.now());
    Long maxDirSort = dashboardDaoService.getMaxDirSort(dashboard.getDisplayType(),
        dashboard.getDirId());
    Long next = Optional.ofNullable(maxDirSort).orElse(0L) + 1;
    dashboard.setOrderNum(next);
  }

  /**
   * 校验是否存在仪表板名称
   *
   * @param dashboardName 仪表板名称
   * @param displayType   展示类型   详情见 DisplayTypeEnum
   */
  private void isExistDashboardName(String dashboardName, Integer displayType) {
    DashboardQuery filter = new DashboardQuery();
    filter.setName(dashboardName);
    filter.setDisplayType(displayType);
    List<Dashboard> dashboards = dashboardDaoService.queryByCondition(filter);
    if (CollUtil.isNotEmpty(dashboards)) {
      throw new BiException(DashboardErrorCode.DASHBOARD_NAME_EXISTS,"名称重复，请重新输入");
    }
  }

  /**
   * 构建 Dashboard
   *
   * @param id             仪表板ID
   * @param statusCodeEnum 仪表板状态枚举
   * @return Dashboard
   */
  private Dashboard buildDashboard(Long id, StatusCodeEnum statusCodeEnum) {
    Dashboard dashboard = new Dashboard();
    dashboard.setId(id);
    Instant instant = Instant.now();
    dashboard.setUpdatedAt(instant);
    UserInfo userInfo = UserContextUtil.getUserInfo();
    dashboard.setUpdatedBy(userInfo.getEmail());
    dashboard.setStatusCode(statusCodeEnum.getCode());
    return dashboard;
  }
}
