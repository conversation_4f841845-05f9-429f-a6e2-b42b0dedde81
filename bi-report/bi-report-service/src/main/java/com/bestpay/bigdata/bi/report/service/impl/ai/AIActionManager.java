package com.bestpay.bigdata.bi.report.service.impl.ai;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/6/10
 *
 * insight AI调用管理
 */
@Slf4j
@Component
public class AIActionManager {

  private static final int MAX_THREADS = 2;
  private static final int QUEUE_CAPACITY = 50;

  private BlockingQueue<Runnable> taskQueue;
  private ExecutorService executor;

  @PostConstruct
  private void init() {
    taskQueue = new LinkedBlockingQueue<>(QUEUE_CAPACITY);
    executor = Executors.newFixedThreadPool(MAX_THREADS);

    executor.execute(() -> {
      while (true) {
        try {
          Runnable task = taskQueue.take(); // 从队列中获取任务，如果队列为空，则阻塞等待
          executor.execute(task); // 执行任务
        } catch (Exception e) {
          log.error("AI action task failed: ", e);
        }
      }
    });
  }

  public boolean submit(Runnable task) {
    return taskQueue.offer(task);
  }

  @PreDestroy
  private void stop() {
    executor.shutdown();
  }
}