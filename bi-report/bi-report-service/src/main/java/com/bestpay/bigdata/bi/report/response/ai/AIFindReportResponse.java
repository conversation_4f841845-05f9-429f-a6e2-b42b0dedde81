package com.bestpay.bigdata.bi.report.response.ai;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @create 2025-06-11
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel("AI智能找报表响应")
public class AIFindReportResponse {

    @ApiModelProperty(value = "系统信息")
    private String message;

    @ApiModelProperty(value = "AI侧回答")
    private String answer;

    @ApiModelProperty(value = "AI侧回复数据")
    private List<AIFindReportOutputResponse> output;
}
