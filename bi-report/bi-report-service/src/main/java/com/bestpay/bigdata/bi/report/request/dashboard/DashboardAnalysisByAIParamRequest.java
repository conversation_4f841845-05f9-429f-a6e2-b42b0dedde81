package com.bestpay.bigdata.bi.report.request.dashboard;

import com.bestpay.bigdata.bi.common.dto.dashboard.CardInfoDTO;
import com.bestpay.bigdata.bi.report.request.tablecard.TableCardDataRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * @author:tangye
 * @description:
 * @createTime:2025/6/6 13:42
 * @version:1.0
 */
@Data
@Builder
@ApiModel(description = "仪表盘AI解读报告参数对象")
public class DashboardAnalysisByAIParamRequest {

    @ApiModelProperty(value = "卡片名称")
    private String cardName;

    @ApiModelProperty(value = "图表类型")
    private String chartType;

    @ApiModelProperty(value = "图表和表格的数据(csv格式)")
    private String data;

    @ApiModelProperty(value = "文本(包括指标文本，带引用指标文本内容)")
    private String indexText;

    @ApiModelProperty(value = "过滤器内容")
    private String filter;

}
