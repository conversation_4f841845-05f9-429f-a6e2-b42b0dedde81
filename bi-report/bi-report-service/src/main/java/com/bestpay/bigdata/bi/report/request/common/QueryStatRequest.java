package com.bestpay.bigdata.bi.report.request.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel(description = "查询统计请求参数")
public class QueryStatRequest {

    @NotNull(message = "ID不能为空")
    @ApiModelProperty(value = "id")
    private Long id;

    @NotNull(message = "查询类型不能为空")
    @ApiModelProperty(value = "1,数据探查，2,报表中心3,openapi")
    private Integer typeCode;

}
