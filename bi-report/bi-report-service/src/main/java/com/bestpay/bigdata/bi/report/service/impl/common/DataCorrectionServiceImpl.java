package com.bestpay.bigdata.bi.report.service.impl.common;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.bean.UserInfoRequest;
import com.bestpay.bigdata.bi.common.common.ChartTypeEnum;
import com.bestpay.bigdata.bi.common.dto.common.TableFontStyleRequest;
import com.bestpay.bigdata.bi.common.dto.dashboard.ReportCardQueryDTO;
import com.bestpay.bigdata.bi.common.dto.dashboard.newcard.CoordinateAxisConfigRequest;
import com.bestpay.bigdata.bi.common.dto.dashboard.newcard.CoordinateAxisXRequest;
import com.bestpay.bigdata.bi.common.dto.dashboard.newcard.CoordinateAxisYRequest;
import com.bestpay.bigdata.bi.common.dto.dashboard.newcard.DataLabelConfigRequest;
import com.bestpay.bigdata.bi.common.dto.dashboard.newcard.TableDataStyle;
import com.bestpay.bigdata.bi.common.dto.report.BasicFormat;
import com.bestpay.bigdata.bi.common.dto.report.TableConfiguration;
import com.bestpay.bigdata.bi.common.dto.report.ThemeStyle;
import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.database.api.common.QueryStatService;
import com.bestpay.bigdata.bi.database.api.common.TmpUserOrgService;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardReportCardService;
import com.bestpay.bigdata.bi.database.api.datascreen.ReportComponentDaoService;
import com.bestpay.bigdata.bi.database.api.report.component.NewReportStyleService;
import com.bestpay.bigdata.bi.database.api.subscribe.ObjectSubscribeService;
import com.bestpay.bigdata.bi.database.dao.common.UserOrgDO;
import com.bestpay.bigdata.bi.database.bean.subscribe.ObjectSubScribeDO;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardReportCardDO;
import com.bestpay.bigdata.bi.database.dao.datascreen.AxisAndChartFieldDO;
import com.bestpay.bigdata.bi.database.dao.report.component.NewReportStyleDO;
import com.bestpay.bigdata.bi.report.request.common.QueryStatRequest;
import com.bestpay.bigdata.bi.report.schedule.subscribe.enums.SubscribeType;
import com.bestpay.bigdata.bi.report.service.common.DataCorrectionService;
import com.bestpay.bigdata.bi.thirdparty.api.AiPlusUserService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Author：Song
 * @Date：2024/11/28 9:48
 * @Desc:
 */
@Slf4j
@Service
public class DataCorrectionServiceImpl implements DataCorrectionService {

    @Resource
    private ReportComponentDaoService reportComponentDaoService;
    @Resource
    private NewReportStyleService newReportStyleService;

    @Resource
    private DashboardReportCardService dashboardReportCardService;

    @Resource
    private ObjectSubscribeService objectSubscribeService;

    @Resource
    private QueryStatService queryStatService;

    @Resource
    private AiPlusUserService aiPlusUserService;

    @Resource
    private TmpUserOrgService tmpUserOrgService;

    @Override
    public int correctQueryStatOrg(QueryStatRequest queryStatRequest) {
        long curr = System.currentTimeMillis();
        // 查询有11w数据，只用到userName，考虑到数据态度加载到内存中，所以只返回了id和userName
        Set<String> query = queryStatService.queryGtId(queryStatRequest.getId(), queryStatRequest.getTypeCode());
        log.info("queryStatService查询数量：{},查询耗时：{}", query.size(), System.currentTimeMillis() - curr);
        int num = 0;
        List<UserOrgDO> userOrgDOList = new ArrayList<>(query.size());
        if (CollUtil.isNotEmpty(query)){
            Map<String,String> email2Org = new HashMap<>();
            UserInfoRequest request = new UserInfoRequest();
            List<UserInfo> userInfos = aiPlusUserService.getUserList(request);
            if (CollUtil.isNotEmpty(userInfos)){
                for (UserInfo userInfo : userInfos) {
                    email2Org.put(userInfo.getEmail(),userInfo.getOrg().getName());
                }
            }
            for (String email : query) {
                String org = email2Org.get(email);
                if (StrUtil.isNotBlank(org)){
                    UserOrgDO userOrgDO = new UserOrgDO();
                    userOrgDO.setUserOrg(org);
                    userOrgDO.setUsername(email);
                    userOrgDOList.add(userOrgDO);
                }
            }
            // 分批插入，每500条插入一次
            List<List<UserOrgDO>> partitions = Lists.partition(userOrgDOList, 500);
            for (List<UserOrgDO> partition : partitions) {
                tmpUserOrgService.batchInsert(partition);
            }
        }
        log.info("queryStatService更新耗时：{}", System.currentTimeMillis() - curr);
        return num;
    }

    @Override
    public void tableStyle() {
        // 查询报表的数量
        List<NewReportStyleDO> newReportStyleDOS = newReportStyleService.queryAll();
        log.info("报表数量：{}", CollUtil.size(newReportStyleDOS));
        int num = 1;
        for (NewReportStyleDO newReportStyleDO : newReportStyleDOS) {
            String tableConfiguration = newReportStyleDO.getTableConfiguration();
            TableConfiguration configuration = JSONUtil.toBean(tableConfiguration, TableConfiguration.class);

            BasicFormat basicFormat = configuration.getBasicFormat();
            // 构建默认的basicFormat数据
            basicFormat = buildDefaultBasicFormat(basicFormat);
            configuration.setBasicFormat(basicFormat);
            // 主题相关 之前有个列宽，但是那个去掉了
            ThemeStyle themeStyle = buildThemeStyle();
            configuration.setThemeStyle(themeStyle);
            String configurationJsonStr = JSONUtil.toJsonStr(configuration);
            newReportStyleDO.setTableConfiguration(configurationJsonStr);
            // 会存在这个字段为空的情况，如果为空，
            String tableFontStyle = newReportStyleDO.getTableFontStyle();
            TableFontStyleRequest tableDataStyle2 = JSONUtil.toBean(tableFontStyle, TableFontStyleRequest.class);
            TableDataStyle tableDataStyle = tableDataStyle2.getTableDataStyle();
            if (Objects.nonNull(tableDataStyle) && Objects.nonNull(tableDataStyle.getFontSize())){
                // 填充线的默认属性
                fillTableDataLineStyle(tableDataStyle);
            }else {
                // 构建默认的 表格数据样式，完整的所有的属性
                tableDataStyle = buildDefaultTableDataStyle();
                tableDataStyle2.setTableDataStyle(tableDataStyle);
            }
            String tableDataStyleJsonStr = JSONUtil.toJsonStr(tableDataStyle2);
            newReportStyleDO.setTableFontStyle(tableDataStyleJsonStr);
            newReportStyleService.updateTableFontStyleAndTableConfigurationById(newReportStyleDO);
            log.info("第{}条报表的数据更新成功", num++);
        }


        List<DashboardReportCardDO> dashboardReportCardDOS = dashboardReportCardService.find(new ReportCardQueryDTO());
        log.info("看板数量：{}", CollUtil.size(dashboardReportCardDOS));
        num = 1;
        for (DashboardReportCardDO dashboardReportCardDO : dashboardReportCardDOS) {

            String tableConfiguration = dashboardReportCardDO.getTableConfiguration();
            if (StrUtil.isNotBlank(tableConfiguration)){
                TableConfiguration configuration = JSONUtil.toBean(tableConfiguration, TableConfiguration.class);
                BasicFormat basicFormat = configuration.getBasicFormat();
                // 构建默认的basicFormat数据
                basicFormat = buildDefaultBasicFormat(basicFormat);
                configuration.setBasicFormat(basicFormat);
                // 主题相关 之前有个列宽，但是那个去掉了
                ThemeStyle themeStyle = buildThemeStyle();
                configuration.setThemeStyle(themeStyle);
                String configurationJsonStr = JSONUtil.toJsonStr(configuration);
                dashboardReportCardDO.setTableConfiguration(configurationJsonStr);
            }
            // 会存在这个字段为空的情况，如果为空，
            String tableFontStyle = dashboardReportCardDO.getTableFontStyle();
            TableFontStyleRequest tableDataStyle2 = JSONUtil.toBean(tableFontStyle, TableFontStyleRequest.class);
            TableDataStyle tableDataStyle = tableDataStyle2.getTableDataStyle();
            if (Objects.nonNull(tableDataStyle) && Objects.nonNull(tableDataStyle.getFontSize())){
                // 填充线的默认属性
                fillTableDataLineStyle(tableDataStyle);
            }else {
                // 构建默认的 表格数据样式，完整的所有的属性
                tableDataStyle = buildDefaultTableDataStyle();
                tableDataStyle2.setTableDataStyle(tableDataStyle);
            }
            String tableDataStyleJsonStr = JSONUtil.toJsonStr(tableDataStyle2);
            dashboardReportCardDO.setTableFontStyle(tableDataStyleJsonStr);
            dashboardReportCardService.updateTableFontStyleAndTableConfigurationById(dashboardReportCardDO);

            log.info("第{}条仪表板报表条数据更新成功", num++);
        }


    }

    @Override
    public void rotationAngleAndOffset() {
        List<DashboardReportCardDO> dashboardReportCardDOS = dashboardReportCardService.find(new ReportCardQueryDTO());
        log.info("更新数据标签旋转角度和偏移量的看板数量：{}", CollUtil.size(dashboardReportCardDOS));
        for (DashboardReportCardDO dashboardReportCardDO : dashboardReportCardDOS) {
            String coordinateAxisConfig = dashboardReportCardDO.getCoordinateAxisConfig();
            CoordinateAxisConfigRequest coordinateAxisConfigRequest = JSONUtil.toBean(coordinateAxisConfig, CoordinateAxisConfigRequest.class);
            DataLabelConfigRequest dataLabelConfig = coordinateAxisConfigRequest.getDataLabelConfig();
            dataLabelConfig.setLabelRotationAngle(0);
            dataLabelConfig.setXLabelOffset(0);
            dataLabelConfig.setYLabelOffset(0);
            String jsonStr = JSONUtil.toJsonStr(coordinateAxisConfigRequest);
            DashboardReportCardDO cardDO = new DashboardReportCardDO();
            cardDO.setId(dashboardReportCardDO.getId());
            cardDO.setCoordinateAxisConfig(jsonStr);
            //   更新数据标签旋转角度和偏移量的看板数量
            dashboardReportCardService.update(cardDO);

        }

    }
    @Transactional
    @Override
    public void graphicsConf() {
        // 柱状图 条形图。只针对这几种类型进行数据订正
        List<Integer> colAndBarList = Stream.of(ChartTypeEnum.COLUMN_CHART.getCode(),ChartTypeEnum.BAR_CHART.getCode()).collect(Collectors.toList());
        List<DashboardReportCardDO> colAndBarCardList = dashboardReportCardService.queryByChartTypes(colAndBarList);
        log.info("柱状图条形图更新数据：{}", CollUtil.size(colAndBarCardList));
        // 这些数据需要更新。图例配置信息。坐标轴信息，数据标签信息。  t_dashboard_report_card  chart_field  coordinate_axis_config
        for (DashboardReportCardDO dashboardReportCardDO : colAndBarCardList) {
                // 处理图表属性
            String chartField = dealChartField(dashboardReportCardDO.getChartField());
                // 订正坐标轴信息
            CoordinateAxisConfigRequest axisConfigRequest = correctCoordinateAxisConfigRequest(dashboardReportCardDO.getCoordinateAxisConfig());
            String jsonStr = JSONUtil.toJsonStr(axisConfigRequest);
            DashboardReportCardDO cardDO = new DashboardReportCardDO();
            cardDO.setId(dashboardReportCardDO.getId());
            cardDO.setChartField(chartField);
            cardDO.setCoordinateAxisConfig(jsonStr);
            dashboardReportCardService.update(cardDO);
        }
        List<Integer> pieDonutList = Stream.of(ChartTypeEnum.PIE_CHART.getCode(),ChartTypeEnum.DONUT_CHART.getCode()).collect(Collectors.toList());
        // 饼图 环形图只针对这几种类型进行数据订正
        List<DashboardReportCardDO> pieAndDonutCardList = dashboardReportCardService.queryByChartTypes(pieDonutList);
        log.info("饼图环形图更新数据：{}", CollUtil.size(pieAndDonutCardList));
        for (DashboardReportCardDO dashboardReportCardDO : pieAndDonutCardList) {
            String coordinateAxisConfig = dashboardReportCardDO.getCoordinateAxisConfig();
            // 处理环形图图或者饼图的数据标签
            String jsonStr = buildDataLabel(coordinateAxisConfig);
            DashboardReportCardDO cardDO = new DashboardReportCardDO();
            cardDO.setId(dashboardReportCardDO.getId());
            cardDO.setCoordinateAxisConfig(jsonStr);
            dashboardReportCardService.update(cardDO);
        }
        // 数据大屏 柱状图和条形图修改
       List<AxisAndChartFieldDO> colANdBarDataScreen = reportComponentDaoService.queryByChartTypeList(colAndBarList);
        for (AxisAndChartFieldDO axisAndChartFieldDO : colANdBarDataScreen) {
            // 处理坐标轴
            String coordinateAxisConfig = axisAndChartFieldDO.getCoordinateAxisConfig();
            if (StrUtil.isNotBlank(coordinateAxisConfig)) {
                CoordinateAxisConfigRequest coordinateAxisConfigRequest = correctCoordinateAxisConfigRequest(coordinateAxisConfig);
                reportComponentDaoService.updateCoordinateAxisConfigById(axisAndChartFieldDO.getReportId(),JSONUtil.toJsonStr(coordinateAxisConfigRequest));
            }
            String chartField = axisAndChartFieldDO.getChartField();
            if (StrUtil.isNotBlank(chartField) && !Objects.equals("{}",chartField)) {
                String chartFieldJsonStr = dealChartField(chartField);
                newReportStyleService.updateChartFieldById(axisAndChartFieldDO.getReportConfId(),chartFieldJsonStr);
            }
        }
        // 数据大屏  饼图和环形图订正数据标签
        List<AxisAndChartFieldDO> pieAndDonutDataScreen = reportComponentDaoService.queryByChartTypeList(pieDonutList);
        for (AxisAndChartFieldDO axisAndChartFieldDO : pieAndDonutDataScreen) {
            String coordinateAxisConfig = axisAndChartFieldDO.getCoordinateAxisConfig();
            if (StrUtil.isNotBlank(coordinateAxisConfig)) {
                String jsonStr = buildDataLabel(coordinateAxisConfig);
                reportComponentDaoService.updateCoordinateAxisConfigById(axisAndChartFieldDO.getReportId(),jsonStr);
            }
        }
    }



    @Transactional
    @Override
    public String dashboardAndReportSupportPage() {
        List<Integer> listTableList = Stream.of(ChartTypeEnum.LIST_TABLE.getCode()).collect(Collectors.toList());
        List<DashboardReportCardDO> dashboardReportCardDOS = dashboardReportCardService.queryByChartTypes(listTableList);
        int dashboardReportCardSize = dashboardReportCardDOS.size();
        log.info("需要仪表板配置大小：{}", dashboardReportCardSize);
        int countUpdateDashboard = 0;
        List<Long> dashboardReportCardIds = new ArrayList<>();
        if (dashboardReportCardSize > 0) {
            for (DashboardReportCardDO dashboardReportCardDO : dashboardReportCardDOS) {
                TableConfiguration tableConfiguration = JSONUtil.toBean(dashboardReportCardDO.getTableConfiguration(), TableConfiguration.class);
                BasicFormat basicFormat = tableConfiguration.getBasicFormat();
                basicFormat.setPageEnabled(Boolean.FALSE);
                basicFormat.setPageSize(10);
                tableConfiguration.setBasicFormat(basicFormat);
                DashboardReportCardDO toUpdate = new DashboardReportCardDO();
                toUpdate.setId(dashboardReportCardDO.getId());
                toUpdate.setTableConfiguration(JSONUtil.toJsonStr(tableConfiguration));
                int updateFlag = dashboardReportCardService.updateTableConfigurationById(toUpdate);
                if (updateFlag > 0) {
                    countUpdateDashboard++;
                }else {
                    dashboardReportCardIds.add(toUpdate.getId());
                }

            }
        }
       List<NewReportStyleDO> reportStyleList =  newReportStyleService.queryTableConfiguration();
        int reportConfSize = reportStyleList.size();
        log.info("需要更新的报表配置大小：{}", reportConfSize);
        int countUpdateReport = 0;
        List<Long> dashboardReportIds = new ArrayList<>();
        if (reportConfSize> 0) {
            for (NewReportStyleDO newReportStyleDO : reportStyleList) {
                TableConfiguration tableConfiguration = JSONUtil.toBean(newReportStyleDO.getTableConfiguration(), TableConfiguration.class);
                BasicFormat basicFormat = tableConfiguration.getBasicFormat();
                basicFormat.setPageSize(10);
                tableConfiguration.setBasicFormat(basicFormat);
                NewReportStyleDO styleDO = new NewReportStyleDO();
                styleDO.setId(newReportStyleDO.getId());
                styleDO.setTableConfiguration(JSONUtil.toJsonStr(tableConfiguration));
                int updateFlag = newReportStyleService.updateTableConfigurationById(styleDO);
                if (updateFlag > 0) {
                    countUpdateReport++;
                }else {
                    dashboardReportIds.add(styleDO.getId());
                }
            }
        }
        String dashboardReportCardTip = "需要仪表板配置数量:" + countUpdateDashboard + ",已经更新的仪表板配置数量："+ countUpdateDashboard + ",更新失败的仪表板的ID："+ dashboardReportCardIds;
        String reportCardTip = "\n需要更新的报表配置数量:" + countUpdateReport + ",已经更新的报表配置数量："+ countUpdateReport + ",更新失败的报表的ID："+ dashboardReportIds;
        return dashboardReportCardTip + "," + reportCardTip;
    }

    private static String buildDataLabel(String coordinateAxisConfig) {
        CoordinateAxisConfigRequest axisConfigRequest = JSONUtil.toBean(coordinateAxisConfig, CoordinateAxisConfigRequest.class);
        DataLabelConfigRequest dataLabelConfig = axisConfigRequest.getDataLabelConfig();
        dataLabelConfig.setBold(false);
        dataLabelConfig.setItalic(false);
        axisConfigRequest.setDataLabelConfig(dataLabelConfig);
        return JSONUtil.toJsonStr(axisConfigRequest);
    }

    private static String dealChartField(String chartField) {
        // 将 JSON 字符串转换为 Map 对象
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Object> jsonObj;
        try {
            jsonObj = objectMapper.readValue(chartField, HashMap.class);
            // 向 Map 对象中添加属性
            jsonObj.put("fontSize", 12);
            jsonObj.put("labelColor", StrUtil.EMPTY);
            jsonObj.put("bold", false);
            jsonObj.put("italic", false);
            // 将 Map 对象转换回 JSON 字符串     图例的信息
            return objectMapper.writeValueAsString(jsonObj);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    @NotNull
    private static CoordinateAxisConfigRequest correctCoordinateAxisConfigRequest(String coordinateAxisConfig) {
        CoordinateAxisConfigRequest axisConfigRequest = JSONUtil.toBean(coordinateAxisConfig, CoordinateAxisConfigRequest.class);
        CoordinateAxisXRequest coordinateAxisX = axisConfigRequest.getCoordinateAxisX();
        if (Objects.isNull(coordinateAxisX)) {
            coordinateAxisX = new CoordinateAxisXRequest();
        }
        coordinateAxisX.setShowXAxis(true);
        //TODO  自动的时候 给什么值
        coordinateAxisX.setLabelCharacterLength(0);
        coordinateAxisX.setLabelRotationAngle(0);
        coordinateAxisX.setItalic(false);
        coordinateAxisX.setBold(false);
        coordinateAxisX.setLabelColor(StrUtil.EMPTY);
        coordinateAxisX.setFontSize(12);
        axisConfigRequest.setCoordinateAxisX(coordinateAxisX);
        CoordinateAxisYRequest coordinateAxisY = axisConfigRequest.getCoordinateAxisY();
        coordinateAxisY.setFontSize(12);
        coordinateAxisY.setBold(false);
        coordinateAxisY.setItalic(false);
        coordinateAxisY.setLabelColor(StrUtil.EMPTY);
        axisConfigRequest.setCoordinateAxisY(coordinateAxisY);
        DataLabelConfigRequest dataLabelConfig = axisConfigRequest.getDataLabelConfig();
        dataLabelConfig.setBold(false);
        dataLabelConfig.setItalic(false);
        axisConfigRequest.setDataLabelConfig(dataLabelConfig);
        return axisConfigRequest;
    }

    @NotNull
    private static BasicFormat buildDefaultBasicFormat(BasicFormat basicFormat) {
        if (Objects.isNull(basicFormat)){
            basicFormat = new BasicFormat();
            basicFormat.setIsMergeCell(Boolean.FALSE);
            basicFormat.setIsWrap(Boolean.FALSE);
            basicFormat.setAlignmentType("");
        }
        // 填充序列号的默认值
        fillSequenceColumnDefaultValue(basicFormat);
        return basicFormat;
    }

    private static void fillSequenceColumnDefaultValue(BasicFormat basicFormat) {
        basicFormat.setShowSequenceColumn(Boolean.FALSE);
        basicFormat.setStyleSelection("num");
        basicFormat.setColumnWidthConfigGroup("Adaptive");
    }

    /**
     * 填充线的默认属性
     * @param tableDataStyle 表格样式
     */
    private static void fillTableDataLineStyle(TableDataStyle tableDataStyle) {
        tableDataStyle.setBold(Boolean.FALSE);
        tableDataStyle.setItalic(Boolean.FALSE);
        tableDataStyle.setStrikethrough(Boolean.FALSE);
        tableDataStyle.setUnderline(Boolean.FALSE);
    }

    private static TableDataStyle buildDefaultTableDataStyle() {
        TableDataStyle tableDataStyle;
        tableDataStyle = new TableDataStyle();
        tableDataStyle.setFillColor("");
        tableDataStyle.setFontSize(12);
        tableDataStyle.setFontFillColor("rgb(96, 98, 102)");
        fillTableDataLineStyle(tableDataStyle);
        return tableDataStyle;
    }

    @NotNull
    private static ThemeStyle buildThemeStyle() {
        ThemeStyle themeStyle = new ThemeStyle();
        themeStyle.setColorConfig("rgba(235, 238, 245, 1)");
        themeStyle.setZebraStripes(Boolean.FALSE);
        return themeStyle;
    }

  /**
   * 订阅配置订正,并且刷新所有的updateAt字段的时间
   **
   * @return
   */
  @Transactional
  @Override
  public String subscribeConfig() {

      //初始的所有数据,一共78个用户数据
      String sourceDate = "{\"054561100220901004\":\"<EMAIL>\",\"01131652271437544477\":\"<EMAIL>\",\"281214010535249744\":\"<EMAIL>\",\"062221623337642463\":\"<EMAIL>\",\"122958674926352851\":\"<EMAIL>\",\"0707201204833397\":\"<EMAIL>\",\"2327661215941128\":\"<EMAIL>\",\"040404596620381123\":\"<EMAIL>\",\"02176665426127885549\":\"<EMAIL>\",\"02190703274926252777\":\"<EMAIL>\",\"061369515424474098\":\"<EMAIL>\",\"100614\":\"<EMAIL>\",\"101901\":\"<EMAIL>\",\"0539285645943042\":\"<EMAIL>\",\"040615384324080339\":\"<EMAIL>\",\"100773\":\"<EMAIL>\",\"2630472131677691\":\"<EMAIL>\",\"250822133336622845\":\"<EMAIL>\",\"054032200426197169\":\"<EMAIL>\",\"101865\":\"<EMAIL>\",\"101282\":\"<EMAIL>\",\"250358516120480093\":\"<EMAIL>\",\"0221181030884802\":\"<EMAIL>\",\"030168482228762525\":\"<EMAIL>\",\"036823661126200845\":\"<EMAIL>\",\"040342554636768912\":\"<EMAIL>\",\"101915\":\"<EMAIL>\",\"133325100329259014\":\"<EMAIL>\",\"021956400721162720\":\"<EMAIL>\",\"171055342020296305\":\"<EMAIL>\",\"262249291739221880\":\"<EMAIL>\",\"251105566937011\":\"<EMAIL>\",\"250542682021705334\":\"<EMAIL>\",\"35320959581141029\":\"<EMAIL>\",\"01614317481228999\":\"<EMAIL>\",\"3310533021548214\":\"<EMAIL>\",\"02190662004537618854\":\"<EMAIL>\",\"535267433024368987\":\"<EMAIL>\",\"100160\":\"<EMAIL>\",\"105732113035836737\":\"<EMAIL>\",\"102022\":\"<EMAIL>\",\"021633224826373792\":\"<EMAIL>\",\"2053053515850321\":\"<EMAIL>\",\"154806502023840458\":\"<EMAIL>\",\"2809554323753483\":\"<EMAIL>\",\"224202692224344742\":\"<EMAIL>\",\"323058626421306510\":\"<EMAIL>\",\"05041064681141965\":\"<EMAIL>\",\"02231337045435318700\":\"<EMAIL>\",\"100759\":\"<EMAIL>\",\"085810122326039972\":\"<EMAIL>\",\"2936091604853819\":\"<EMAIL>\",\"036445475835750187\":\"<EMAIL>\",\"034302322421569216\":\"<EMAIL>\",\"100233\":\"<EMAIL>\",\"102135\":\"<EMAIL>\",\"156112482535651476\":\"<EMAIL>\",\"102568643427507899\":\"<EMAIL>\",\"221714104129471424\":\"<EMAIL>\",\"100239\":\"<EMAIL>\",\"261646135621491581\":\"<EMAIL>\",\"141166606920396866\":\"<EMAIL>\",\"036104391138574\":\"<EMAIL>\",\"01265034231224354002\":\"<EMAIL>\",\"102111\":\"<EMAIL>\",\"2018403724649918\":\"<EMAIL>\",\"036650002123424328\":\"<EMAIL>\",\"160537080936265549\":\"<EMAIL>\",\"100091\":\"<EMAIL>\",\"1009330833678923\":\"<EMAIL>\",\"275430495826074322\":\"<EMAIL>\",\"100802\":\"<EMAIL>\",\"261645595233071095\":\"<EMAIL>\",\"216242201520173499\":\"<EMAIL>\",\"031353143126248497\":\"<EMAIL>\",\"0610515424659768\":\"<EMAIL>\",\"102225\":\"<EMAIL>\",\"080569174323270905\":\"<EMAIL>\"}";
      Map<String, String> sourceDateMap = JSONUtil.parseObj(sourceDate) .toBean(Map.class);
      log.info("初始数据用户数量：{}，具体数据：{}", sourceDateMap.size(),sourceDate);

      //获取数据库中历史钉钉推送的任务信息，status_code类型非9的
      int dingTalkPrivateCode = SubscribeType.DING_TALK_PRIVATE.getCode();
      List<ObjectSubScribeDO> objectSubScribeDos = objectSubscribeService.queryBySubType(
        String.valueOf(dingTalkPrivateCode));

    // 遍历数据库中的数据，然后从sourceFileData获取对应的email，然后更新数据库中的数据
    Date now = new Date();
    for (ObjectSubScribeDO objectSubScribeDo : objectSubScribeDos) {
      JSONObject subConfigJson = JSONUtil.parseObj(objectSubScribeDo.getSubConfig());
      //移除不需要的字段
      subConfigJson.remove("appKey");
      subConfigJson.remove("appSecret");
      subConfigJson.remove("robotCode");

      List<String> userIdList = subConfigJson.getJSONArray("userIdList").toList(String.class);

      //组装对以userId的email，多个email用逗号分隔
      StringBuilder emailStringBuilder = new StringBuilder();
      for (String userId : userIdList) {
        String email = sourceDateMap.get(userId);
        if (StringUtils.isBlank(email)) {
          log.error("subCode is " + objectSubScribeDo.getSubCode() + ",userId " + userId + " is not email");
        } else {
          emailStringBuilder.append(email).append(",");
        }
      }
      //处理尾行,
      if (emailStringBuilder.length() != 0) {
        subConfigJson.putOpt("subEmail", emailStringBuilder.substring(0, emailStringBuilder.length() - 1));
      }

        //将组装后的subConfigJson设置到数据库中
        ObjectSubScribeDO newObjectSubScribeDO = new ObjectSubScribeDO();
        newObjectSubScribeDO.setId(objectSubScribeDo.getId());
        newObjectSubScribeDO.setUpdatedAt(now);
        newObjectSubScribeDO.setSubConfig(subConfigJson.toString());
        objectSubscribeService.updateById(newObjectSubScribeDO);
    }

    //刷新所有非9的订阅配置的updataAt字段的时间，防止订阅配置序列化的问题，进行刷新
    List<ObjectSubScribeDO> objectSubScribeDoAll = objectSubscribeService.queryBySubType(null);
    for (ObjectSubScribeDO objectSubScribeDo : objectSubScribeDoAll) {
      //刷新最小updateAt设置到数据库中
        ObjectSubScribeDO newObjectSubScribeDO = new ObjectSubScribeDO();
        newObjectSubScribeDO.setId(objectSubScribeDo.getId());
        newObjectSubScribeDO.setUpdatedAt(now);
        objectSubscribeService.updateById(newObjectSubScribeDO);
    }
    return "OK";
  }


}
