package com.bestpay.bigdata.bi.report.request.datascreen;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("数据大屏预览请求")
public class DataScreenPreviewRequest {
    @ApiModelProperty("数据大屏ID")
    @NotNull(message = "数据大屏ID不能为空")
    private Long dataScreenId;
}
