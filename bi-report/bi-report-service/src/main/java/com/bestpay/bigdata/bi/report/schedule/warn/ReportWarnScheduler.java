package com.bestpay.bigdata.bi.report.schedule.warn;

import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.dto.warn.ReportWarnQueryDTO;
import com.bestpay.bigdata.bi.common.enums.StatusCodeEnum;
import com.bestpay.bigdata.bi.common.error.ScheduleErrorCode;
import com.bestpay.bigdata.bi.common.exception.BiException;
import com.bestpay.bigdata.bi.common.util.LogTraceIdGenerator;
import com.bestpay.bigdata.bi.database.api.report.ReportWarnDAOService;
import com.bestpay.bigdata.bi.database.dao.warn.ReportWarnDo;
import com.bestpay.bigdata.bi.report.bean.warn.ReportWarnDTO;
import com.bestpay.bigdata.bi.report.bean.warn.WarnConfig;
import com.bestpay.bigdata.bi.report.schedule.BaseScheduler;
import com.bestpay.bigdata.bi.report.schedule.ScheduleConfig;
import com.bestpay.bigdata.bi.report.schedule.subscribe.enums.TaskTypeEnum;
import com.bestpay.bigdata.bi.report.service.report.ReportWarnService;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.quartz.CronScheduleBuilder;
import org.quartz.JobBuilder;
import org.quartz.JobDataMap;
import org.quartz.JobDetail;
import org.quartz.JobKey;
import org.quartz.SchedulerException;
import org.quartz.Trigger;
import org.quartz.TriggerBuilder;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;


/**
 * ClassName: ReportScheduler
 * Package: com.bestpay.bigdata.bi.report.schedule
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/8/4 17:13
 * @Version 1.0
 */
@Slf4j
@Component
public class ReportWarnScheduler extends BaseScheduler
{

    @Resource
    private ReportWarnDAOService reportWarnDAOService;

    @Resource
    private ReportWarnService reportWarnService;

    private static Map<String, Long> cacheWarnUpdateDateMap = new ConcurrentHashMap<>();

    /**
     * 采用内存缓存 处于调度的job key用于编辑删除操作
     */
    protected static Map<String, JobKey> cacheJobMap = new ConcurrentHashMap<>();

    @PostConstruct
    private void init() {

        while (!startFlag.get()) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }

        try {
            log.info("report quartz scheduler start success !!!");
            ReportWarnQueryDTO reportWarnQueryDTO = ReportWarnQueryDTO.builder()
                    .statusCode(StatusCodeEnum.ONLINE.getCode()).build();
            List<ReportWarnDo> reportWarnDoList = reportWarnDAOService.query(reportWarnQueryDTO);
            log.info("query report warn list size : {}", reportWarnDoList.size());

            List<ReportWarnDTO> readyToWarnList  = reportWarnDoList.stream().map(this::reportWarnDoToDTO).collect(Collectors.toList());

            for (ReportWarnDTO reportWarnDTO : readyToWarnList) {
                addReportQuartzJob(reportWarnDTO);
            }
            log.info("添加到quart完成");
        } catch (Throwable e) {
            log.info("report scheduler init error" , e);
        } finally {
            // add manage report warn job
            addReportWarnManageJob();
        }
    }



    private ReportWarnDTO reportWarnDoToDTO(ReportWarnDo reportWarnDo) {
        ReportWarnDTO reportWarnDTO = new ReportWarnDTO();
        BeanUtils.copyProperties(reportWarnDo, reportWarnDTO);
        ScheduleConfig scheduleConfig = JSONUtil.toBean(reportWarnDo.getScheduleConfig(), ScheduleConfig.class);
        WarnConfig warnConfig = JSONUtil.toBean(reportWarnDo.getWarnConfig(), WarnConfig.class);
        reportWarnDTO.setScheduleConfig(scheduleConfig);
        reportWarnDTO.setWarnConfig(warnConfig);
        reportWarnDTO.setWarnRule(reportWarnDo.getWarnRule());
        return reportWarnDTO;
    }


    public void addReportQuartzJob(Long id) {
        try {
            log.info("report quartz scheduler start success !!!");
            // 查询任务详情
            ReportWarnQueryDTO reportWarnQueryDTO = ReportWarnQueryDTO.builder()
                .id(id)
                .statusCode(StatusCodeEnum.ONLINE.getCode()).build();

            List<ReportWarnDo> reportWarnDoList = reportWarnDAOService.query(reportWarnQueryDTO);
            log.info("query report warn list size : {}", reportWarnDoList.size());

            List<ReportWarnDTO> readyToWarnList  = reportWarnDoList.stream().map(this::reportWarnDoToDTO).collect(Collectors.toList());
            // 添加到执行队列
            addOnceReportQuartzJob(readyToWarnList.get(0));
        } catch (Exception e) {
            log.error("quartz调度任务发生异常 ", e);
        }
    }

    protected void addOnceReportQuartzJob(ReportWarnDTO reportWarnDTO) {
        try {
            log.info("add ReportQuartzJob, reportWarnDTO : {}", JSONUtil.toJsonStr(reportWarnDTO));
            Map<String, Object> map = new HashMap<>();
            map.put("reportWarn", reportWarnDTO);
            ScheduleConfig scheduleConfig = reportWarnDTO.getScheduleConfig();
            String quartzCron = reportWarnDTO.getCron();
            log.info("quartzCron : {}", quartzCron);
            if (!checkCron(quartzCron)) {
                log.info("cron 表达式的执行时间 早于 当前时间，属于无效的cron表达式了");
                return;
            }

            // 创建任务
            String name = reportWarnDTO.getCode() + UUID.randomUUID();
            JobDetail job = JobBuilder.newJob(ReportWarnJob.class)
                .withIdentity(name, TaskTypeEnum.OBJECT_SUBSCRIBE_RETRY.getGroup())
                .withDescription(reportWarnDTO.getName())
                .usingJobData(new JobDataMap(map))
                .build();

            // 创建触发器
            Trigger trigger = TriggerBuilder.newTrigger()
                .withIdentity("alertTrigger" + name, TaskTypeEnum.OBJECT_SUBSCRIBE_RETRY.getGroup())
                .startNow()
                .build();

            // 启动
            scheduler.scheduleJob(job, trigger);

        } catch (Exception e) {
            log.error("quartz调度任务发生异常 ", e);
        }
    }


    protected void addReportQuartzJob(ReportWarnDTO reportWarnDTO) {
        try {
            log.info("add ReportQuartzJob, reportWarnDTO : {}", JSONUtil.toJsonStr(reportWarnDTO));
            Map<String, Object> map = new HashMap<>();
            map.put("reportWarn", reportWarnDTO);
            ScheduleConfig scheduleConfig = reportWarnDTO.getScheduleConfig();
            String quartzCron = reportWarnDTO.getCron();
            log.info("quartzCron : {}", quartzCron);
            if (!checkCron(quartzCron)) {
                log.info("cron 表达式的执行时间 早于 当前时间，属于无效的cron表达式了");
                return;
            }
            String group = scheduleConfig.getTimeType().toUpperCase();
            // 创建任务
            String name = reportWarnDTO.getCode();
            JobDetail job = JobBuilder.newJob(ReportWarnJob.class)
                    .withIdentity(name, group)
                    .withDescription(reportWarnDTO.getName())
                    .usingJobData(new JobDataMap(map))
                    .build();

            // 创建触发器
            Trigger trigger = TriggerBuilder.newTrigger()
                    .withIdentity("alertTrigger" + name, group)
                    .startNow()
                    .withSchedule(
                            CronScheduleBuilder.cronSchedule(quartzCron)
                                    .withMisfireHandlingInstructionIgnoreMisfires()
                    )
                    .build();

            if (!scheduler.checkExists(job.getKey())) {
                scheduler.scheduleJob(job, trigger);
            }
            cacheJobMap.put(reportWarnDTO.getCode(), job.getKey());
            cacheWarnUpdateDateMap.put(reportWarnDTO.getCode(), reportWarnDTO.getUpdatedAt().getTime());

        } catch (Exception e) {
            log.error("quartz调度任务发生异常 ", e);
        }
    }


    private void addReportWarnManageJob() {
        // 创建定时线程池
        ScheduledExecutorService executor = Executors.newScheduledThreadPool(1);

        // 每隔5分钟执行一次任务
        log.info("start report warn manage job...");
        executor.scheduleAtFixedRate(new ReportWarnManageJob(), 0, 2, TimeUnit.MINUTES);
    }


    class ReportWarnManageJob implements Runnable {

        @Override
        public void run() {
            try {
                String traceId = LogTraceIdGenerator.generateTraceId();
                MDC.put(LogTraceIdGenerator.TRACE_ID, traceId);

                log.info("ready to manage report warn job...");
                manageReportWarnJob();
            }catch (Exception e){
                log.error("reportWarnManageJob fail", e);
                throw e;

            } finally {
                MDC.remove(LogTraceIdGenerator.TRACE_ID);
            }
        }

        protected void deleteJob(String cacheKey) {
            if (!cacheJobMap.containsKey(cacheKey)) {
                throw new BiException(ScheduleErrorCode.INVOKE_JOB_ERROR,"删除调度job失败, cacheKey  不存在" + cacheKey);
            }
            JobKey jobKey = cacheJobMap.get(cacheKey);
            try {
                scheduler.deleteJob(jobKey);
                cacheJobMap.remove(cacheKey);
            } catch (SchedulerException e) {
                log.error("删除调度任务失败，原因 ", e);
            }
        }

        protected void manageReportWarnJob() {
            // delete report warn
            Set<String> warnCodeSet = cacheJobMap.keySet();
            log.info("manage warn job, already in quartz warn size :{}", warnCodeSet.size());
            for (String code : warnCodeSet) {
                try {
                    ReportWarnQueryDTO reportWarnQueryDTO = ReportWarnQueryDTO.builder().build();
                    reportWarnQueryDTO.setCode(code);
                    List<ReportWarnDo> reportWarnDoList = reportWarnDAOService.query(reportWarnQueryDTO);
                    if (CollectionUtils.isEmpty(reportWarnDoList)) {
                        // delete
                        log.info("manage warn job , delete job because warn task deleted, code : {}", code);
                        deleteJob(code);
                    } else {
                        ReportWarnDo reportWarnDo = reportWarnDoList.get(0);
                        if (StatusCodeEnum.ONLINE.getCode() != reportWarnDo.getStatusCode()) {
                            // delete
                            log.info("manage warn job , delete job because warn task offline, code : {}", code);
                            deleteJob(code);
                        } else {
                            Long oldUpdatedAt = cacheWarnUpdateDateMap.get(code);
                            if (reportWarnDo.getUpdatedAt().getTime() != oldUpdatedAt) {
                                // update
                                log.info("manage warn job , delete job because warn task update, code : {}", code);
                                deleteJob(code);
                                // 如果时间不一致，则先删除quartz 里面的老的job，然后在把新的更新进去
                                ReportWarnDTO reportWarnDTO = reportWarnDoToDTO(reportWarnDo);
                                log.info("manage warn job , add job because warn task update, code : {}", code);
                                addReportQuartzJob(reportWarnDTO);

                            }
                        }
                    }
                } catch (Throwable e) {
                    log.error("管理report warn job 发生错误", e);
                }
            }

            try {
                ReportWarnQueryDTO reportWarnQueryDTO = ReportWarnQueryDTO.builder().build();
                List<ReportWarnDo> reportWarnDoList = reportWarnDAOService.query(reportWarnQueryDTO);
                for (ReportWarnDo reportWarnDo : reportWarnDoList) {
                    if (!warnCodeSet.contains(reportWarnDo.getCode()) &&
                            StatusCodeEnum.ONLINE.getCode() == reportWarnDo.getStatusCode()) {
                        log.info("manage warn job, find new report warn job , code : {}", reportWarnDo.getCode());
                        ReportWarnDTO reportWarnDTO = reportWarnDoToDTO(reportWarnDo);
                        addReportQuartzJob(reportWarnDTO);
                    }
                }
            } catch (Throwable e) {
                log.error("管理report warn job 发生错误", e);
            }
        }
    }
}
