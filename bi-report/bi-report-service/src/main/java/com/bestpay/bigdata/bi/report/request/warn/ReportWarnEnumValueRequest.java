package com.bestpay.bigdata.bi.report.request.warn;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel("查询枚举值请求")
public class ReportWarnEnumValueRequest{

    @ApiModelProperty(value = "报表id")
    private String reportId;

    @ApiModelProperty(value = "列uuid")
    private String columnUuid;

    @ApiModelProperty(value = "告警源类型")
    private String warnSourceType;
}
