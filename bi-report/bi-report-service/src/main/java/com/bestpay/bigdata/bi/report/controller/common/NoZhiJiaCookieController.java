package com.bestpay.bigdata.bi.report.controller.common;

import static com.bestpay.bigdata.bi.common.constant.BIConstant.OBJECT_TYPE_REPORT;
import static com.bestpay.bigdata.bi.common.constant.BIConstant.OBJECT_TYPE_REPORT_CARD;

import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.error.FileErrorCode;
import com.bestpay.bigdata.bi.common.exception.BiException;
import com.bestpay.bigdata.bi.common.oss.OssServiceFactory;
import com.bestpay.bigdata.bi.common.response.ReportDetailVO;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.common.util.Base64Util;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardDaoService;
import com.bestpay.bigdata.bi.database.api.dashboard.DataScreenConfService;
import com.bestpay.bigdata.bi.database.bean.dashboard.Dashboard;
import com.bestpay.bigdata.bi.database.bean.dashboard.DataScreenConf;
import com.bestpay.bigdata.bi.report.controller.common.CommonComponentController.FileInfo;
import com.bestpay.bigdata.bi.report.request.dashboard.OutNetDataRequest;
import com.bestpay.bigdata.bi.report.request.dashboard.QueryDashboardRequest;
import com.bestpay.bigdata.bi.report.request.subscribe.QueryStateRequest;
import com.bestpay.bigdata.bi.report.request.embed.AppEmbedObjectListRequest;
import com.bestpay.bigdata.bi.report.request.embed.AppEmbedRequest;
import com.bestpay.bigdata.bi.report.request.new_datascreen.ShareDataShowRequest;
import com.bestpay.bigdata.bi.report.request.report.DownloadApplyRequest;
import com.bestpay.bigdata.bi.report.request.report.QueryReportRequest;
import com.bestpay.bigdata.bi.report.request.subscribe.IdRequest;
import com.bestpay.bigdata.bi.report.response.common.QueryIndexAndReportResponse;
import com.bestpay.bigdata.bi.report.response.embed.AppEmbedOrgGroupVO;
import com.bestpay.bigdata.bi.report.response.embed.AppEmbedShareVO;
import com.bestpay.bigdata.bi.report.response.embed.AppEmbedVO;
import com.bestpay.bigdata.bi.report.response.report.ReportDownloadVO;
import com.bestpay.bigdata.bi.report.schedule.subscribe.ObjectSubscribeScheduler;
import com.bestpay.bigdata.bi.report.service.dashboard.OutNetDataQueryService;
import com.bestpay.bigdata.bi.report.service.datascreen.ShareDataQueryService;
import com.bestpay.bigdata.bi.report.service.embed.ReportAppEmbedService;
import com.bestpay.bigdata.bi.report.service.report.AccessStatisticsService;
import com.bestpay.bigdata.bi.report.service.report.ReportProcessService;
import com.bestpay.bigdata.bi.report.service.report.ReportQueryService;
import com.bestpay.bigdata.bi.report.service.report.ReportUpdateService;
import com.bestpay.bigdata.bi.report.service.subscribe.SubscribeManagerService;
import com.bestpay.bigdata.bi.report.util.FileUploadUtils;
import com.google.common.base.Throwables;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 无 zhijia cookie 校验 url
 * /shareDashboard前缀 外网使用
 * /shareDashboardSubscribe前缀 订阅，无头浏览器，截图使用
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/biReport")
@Api(value = "仪表板分享相关", tags = "仪表板分享相关")
public class NoZhiJiaCookieController {
    @Resource
    private ReportQueryService reportQueryService;
    @Resource
    private ReportAppEmbedService reportAppEmbedService;
    @Resource
    private OutNetDataQueryService outNetDataQueryService;
    @Resource
    private DataScreenConfService dataScreenConfService;
    @Resource
    private DashboardDaoService dashboardDaoService;
    @Resource
    private ReportProcessService reportProcessService;
    @Resource
    private ReportUpdateService updateService;
    @Resource
    private AccessStatisticsService statisticsService;
    @Resource
    private ShareDataQueryService shareDataQueryService;
    @Resource
    private OssServiceFactory ossServiceFactory;
    @Resource
    private ObjectSubscribeScheduler objectSubscribeScheduler;

    @Resource
    private SubscribeManagerService  subscribeManagerService;

    @PostMapping("/shareDashboard/syncShowData")
    @ApiOperation(value = "展示数据查询接口", httpMethod = "POST", response = Response.class)
    public Object syncShowData(@RequestBody OutNetDataRequest reportRequest) {

        return outNetDataQueryService.queryData(reportRequest);
    }

    @PostMapping("/shareDashboard/retry")
    @ApiOperation(value="任务重发", httpMethod="POST",response = Response.class)
    public Response retry(@RequestBody @Validated IdRequest request) throws Exception {
        return  Response.ok(objectSubscribeScheduler.addOnceObjectQuartzJob(request));
    }


    @PostMapping("/shareDashboard/subscribe/queryState")
    @ApiOperation(value="查询订阅任务实例状态", httpMethod="POST",response = Response.class)
    public Response queryState(@RequestBody QueryStateRequest queryStateRequest) throws Exception {
        return  Response.ok(subscribeManagerService.queryState(queryStateRequest));
    }


    /**
     * 数据大屏配置信息
     *
     * @param id
     * @return
     */
    @PostMapping("/shareDashboard/dataScreenConf/{id}")
    public Response<DataScreenConf> queryById(@PathVariable("id") Long id) {
        return Response.ok(dataScreenConfService.selectByPrimaryKey(id));
    }

    @PostMapping("/shareDashboard/getDashboardInfo")
    @Deprecated
    @ApiOperation(value="仪表板详情", httpMethod="GET",response = Response.class)
    @ApiImplicitParam(paramType = "query",name = "id",value = "仪表板Id",required = true,dataType = "Long")
    public Response<Dashboard> queryDashboardDetail(@RequestBody QueryDashboardRequest request) {

        Dashboard dashboard = dashboardDaoService.getById(request.getId());

        if(dashboard==null){
            return Response.ok();
        }

        return Response.ok(dashboard);
    }

    @PostMapping(value = {"/shareDashboard/downloadReport"})
    @ApiOperation(value = "查询报表下载", httpMethod = "POST")
    public Response<ReportDownloadVO> downloadReport(@RequestBody DownloadApplyRequest queryReportRequest)
            throws IOException {
        ReportDownloadVO download = reportQueryService.download(queryReportRequest);
        return Response.ok(download);
    }

    @PostMapping(value = {"/shareDashboard/queryAppEmbed"})
    @ApiOperation(value="应用嵌入任务查询接口", httpMethod="POST",response = Response.class)
    public Response<AppEmbedShareVO> queryAppEmbed(@RequestBody String str){
        log.debug("queryAppEmbed:{}",str);
        Response<AppEmbedVO> result = reportAppEmbedService.queryAppEmbedAuthById(str);
        AppEmbedVO data= result.getData();
        AppEmbedShareVO shareVO = new AppEmbedShareVO();
        BeanUtils.copyProperties(data, shareVO);
        return Response.ok(shareVO);
    }

    /**
     * 嵌入，订阅，分享，移动端，报表详情
     * @param str
     * @return
     */
    @PostMapping(value = {"/shareDashboard/queryReportTemplate"})
    @ApiOperation(value = "查询报表模板详情", httpMethod = "POST", response = Response.class)
    @ApiImplicitParam(paramType = "query", name = "id", value = "报表模板Id", required = true, dataType = "Long")
    public Response<ReportDetailVO> queryReportTemplate(@RequestBody String str) {

        Long id = Convert.toLong(JSONUtil.parseObj(str).get("id"));
        String userSource = JSONUtil.parseObj(str).getStr("userSource");
        String type = JSONUtil.parseObj(str).getStr("type");
        // 报表查询：报表详情
        if (Objects.nonNull(type) && type.equals("report")) {

            // 统计访问量
            statisticsService.report(id, userSource, OBJECT_TYPE_REPORT);

            Response<ReportDetailVO> response = updateService.queryReportTemplate(id);

            if (response.getData() != null) {
                response.getData().setOwnerName(null);
                response.getData().setOwnerNameCh(null);
                response.getData().setEmail(null);
                response.getData().setCreatedBy(null);
                response.getData().setUpdatedBy(null);
                response.getData().setOrgName(null);
            }
            return response;

        } else {

            // 嵌入仪表板：报表详情
            // 访问统计
            statisticsService.report(id, userSource, OBJECT_TYPE_REPORT_CARD);

            // 获取报表详情
            return reportProcessService.handlerDashboardReportCardInfo(id);
        }
    }

    @PostMapping(value = {"/shareDashboard/syncQueryReport"})
    @ApiOperation(httpMethod = "POST", value = "同步查询数据", notes = "同步查询数据")
    public Response<QueryIndexAndReportResponse> syncQueryReport(@RequestBody QueryReportRequest reportRequest) {
        try {
            Response<QueryIndexAndReportResponse> response
                =reportQueryService.syncQueryReport(reportRequest);
            return response;
        }catch (Exception e){
            log.error(Throwables.getStackTraceAsString(e));
            throw e;
        }
    }

    @PostMapping(value = {"/shareDashboard/getAppKey", "/shareDashboardSubscribe/getAppKey"})
    @ApiOperation(value="获取AppKey接口", httpMethod="POST",response = Response.class)
    public Response<Map<String, Object>> getAppKey(@RequestBody AppEmbedRequest appEmbedRequest) {
        return reportAppEmbedService.getAppKey(appEmbedRequest);
    }

    @PostMapping(value = {"/shareDashboard/queryAppEmbedObjectList"})
    @ApiOperation(value="获取应用嵌入对象信息", httpMethod="POST",response = Response.class)
    public Response<List<AppEmbedOrgGroupVO>> queryAppEmbedObjectList(@RequestBody AppEmbedObjectListRequest appEmbedRequest) {
        return reportAppEmbedService.queryAppEmbedObjectList(appEmbedRequest);
    }

    @PostMapping("/shareDashboard/datascreenShowData")
    @ApiOperation(httpMethod = "POST", value = "数据大屏展示数据", notes = "数据大屏展示数据")
    public Object dataScreenShowData(@RequestBody ShareDataShowRequest request) {
        return shareDataQueryService.queryData(request);
    }

    @GetMapping("/shareDashboard/requestDownLoadFile/{path}")
    @ApiOperation(httpMethod = "GET", value = "分享下载", notes = "分享下载")
    public Response<String> requestDownLoadFile(@PathVariable String path,
        HttpServletResponse response) throws IOException {

        FileInfo fileInfo = JSONUtil
            .toBean(new String(Base64Util.decryBASE64(path)), FileInfo.class);

        byte[] data = FileUploadUtils.downloadFile(ossServiceFactory, fileInfo.getUrl());

        response.setContentType("application/octet-stream");
        response.addHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.addHeader("charset", "utf-8");
        response.addHeader("Pragma", "no-cache");
        String encodeName = URLEncoder.encode("image"+System.currentTimeMillis()+"."+fileInfo.getSuffix(),
            StandardCharsets.UTF_8.toString());

        response.setHeader("Content-Disposition", "attachment; filename=\""
            + encodeName + "\"; filename*=utf-8''" + encodeName);

        ServletOutputStream outputStream=null;
        try {
            outputStream=response.getOutputStream();
            outputStream.write(data);
            outputStream.flush();
        } catch (Exception e) {
            throw e;
        }finally {
            try {
                if(outputStream!=null){
                    outputStream.close();
                }
            } catch (Exception e) {
                log.error("close outputStream error",e);
            }
        }

        return Response.ok();
    }

    @GetMapping("/shareDashboard/downloadFile/{path}")
    @ApiOperation(httpMethod = "GET", value = "分享下载", notes = "分享下载")
    public void downloadFile(
            @PathVariable String path,
            HttpServletResponse response) throws IOException {

        // 解码 Base64 并解析为 ObjectName
        String filePath = new String(Base64Util.decryBASE64(path));

        // 下载文件数据
        byte[] data = FileUploadUtils.downloadFile(ossServiceFactory, path);
        if (data == null || data.length == 0) {
            throw new BiException(FileErrorCode.FILE_NOT_EXIST,"File not found or empty");
        }

        // 获取文件名和扩展名
        String filename = filePath.substring(filePath.lastIndexOf("/") + 1);
        String fileExt = filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();

        // 设置 MIME 类型
        String mimeType = getMimeType(fileExt);
        response.setContentType(mimeType);

        // 配置响应头，根据文件类型设置行为
        response.addHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.addHeader("Pragma", "no-cache");
        response.setCharacterEncoding(StandardCharsets.UTF_8.toString());

        if ("jpg".equals(fileExt) || "jpeg".equals(fileExt) || "png".equals(fileExt) || "gif".equals(fileExt)) {
            // 图片类型，直接在浏览器打开
            response.setHeader("Content-Disposition", "inline; filename=\"" + filename + "\"");
        } else {
            // 其他类型，强制下载
            String encodedFileName = URLEncoder.encode("download-" + filename, StandardCharsets.UTF_8.toString());
            response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"; filename*=utf-8''" + encodedFileName);
        }

        // 文件数据写入响应流
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            outputStream.write(data);
            outputStream.flush();
        } catch (IOException e) {
            log.error("Error writing file to output stream", e);
            throw new BiException(FileErrorCode.FILE_PROCESSING_ERROR,"Error occurred while downloading file");
        }
    }

    /**
     * 根据文件扩展名返回 MIME 类型
     *
     * @param fileExtension 文件扩展名（不含点号）
     * @return MIME 类型
     */
    private String getMimeType(String fileExtension) {
        if (fileExtension == null || fileExtension.isEmpty()) {
            return "application/octet-stream";
        }
        switch (fileExtension.toLowerCase()) {
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "png":
                return "image/png";
            case "gif":
                return "image/gif";
            case "pdf":
                return "application/pdf";
            case "doc":
            case "docx":
                return "application/msword";
            case "xls":
            case "xlsx":
                return "application/vnd.ms-excel";
            case "zip":
                return "application/zip";
            case "txt":
                return "text/plain";
            default:
                return "application/octet-stream";
        }
    }
}
