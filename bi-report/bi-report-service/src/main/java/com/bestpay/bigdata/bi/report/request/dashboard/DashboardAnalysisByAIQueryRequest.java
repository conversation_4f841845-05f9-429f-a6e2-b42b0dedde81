package com.bestpay.bigdata.bi.report.request.dashboard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author:tangye
 * @description:
 * @createTime:2025/6/11
 * @version:1.0
 */
@Data
@ApiModel(description = "仪表盘AI解读报告查询结果请求参数")
public class DashboardAnalysisByAIQueryRequest {

    @ApiModelProperty(value = "AI解读报告请求key")
    private String key;

}
