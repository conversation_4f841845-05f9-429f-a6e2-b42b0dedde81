package com.bestpay.bigdata.bi.report.request.embed;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "应用嵌入对象列表请求参数")
public class AppEmbedObjectListRequest {
    /**
     * 嵌入类型
     */
    @ApiModelProperty(value = "嵌入类型")
    private String embedType;

    /** DashboardTypeEnum 0桌面端 1移动端 */
    @ApiModelProperty(value = "DashboardTypeEnum 0桌面端 1移动端")
    private Integer isPublishMobile;

    @ApiModelProperty(value = "用户邮箱")
    private String email;

    @ApiModelProperty(value = "关键字")
    private String keyWord;
}
