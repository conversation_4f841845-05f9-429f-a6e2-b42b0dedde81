package com.bestpay.bigdata.bi.report.response.ai;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @create 2025-06-12
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel("AI智能找报表AI回复数据")
public class AIFindReportOutputResponse {

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "类型(报表: report, 仪表板: dashboard, 数据大屏: new_datascreen)")
    private String type;

    @ApiModelProperty(value = "责任人")
    private String owner;

    @ApiModelProperty(value = "描述")
    private String desc;
}
