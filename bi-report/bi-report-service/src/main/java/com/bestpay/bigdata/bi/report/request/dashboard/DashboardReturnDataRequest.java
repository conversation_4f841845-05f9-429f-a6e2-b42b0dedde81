package com.bestpay.bigdata.bi.report.request.dashboard;

import com.bestpay.bigdata.bi.common.entity.ColumnName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
@ApiModel(value = "AI解析报告传参数据对象")
public class DashboardReturnDataRequest {
  @ApiModelProperty(value = "查询结果")
  private List<ColumnName> columnNameMaps;
  @ApiModelProperty(value = "查询结果")
  private List<Map<String, Object>> data;
  @ApiModelProperty(value = "文本")
  private String text;
}
