package com.bestpay.bigdata.bi.report.response.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "报表访问量topk基本信息")
public class TopKReportInfoResponse {
    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "类型(report, dashboard, new_datascreen)")
    private String type;
}
