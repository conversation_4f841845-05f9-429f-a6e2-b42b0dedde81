package com.bestpay.bigdata.bi.report.service.embed;

import com.bestpay.bigdata.bi.common.response.PageQueryVO;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.database.dao.common.AppEmbedDO;
import com.bestpay.bigdata.bi.report.request.embed.AppEmbedListRequest;
import com.bestpay.bigdata.bi.report.request.embed.AppEmbedObjectListRequest;
import com.bestpay.bigdata.bi.report.request.embed.AppEmbedRequest;
import com.bestpay.bigdata.bi.report.response.embed.AppEmbedOrgGroupVO;
import com.bestpay.bigdata.bi.report.response.embed.AppEmbedVO;
import java.util.List;
import java.util.Map;

/**
 * @author: bj
 * @date: 2022/09/15
 */
public interface ReportAppEmbedService {
    /**
     * 应用嵌入新增
     * @param appEmbedRequest
     * @return
     */
    Response<Long> insertAppEmbed(AppEmbedRequest appEmbedRequest);


    Response<String> completeAppEmbed(AppEmbedRequest appEmbedRequest);

    /**
     * 应用嵌入列表查询V2
     * @param request
     * @return
     */
    Response<PageQueryVO<AppEmbedVO>> queryAppEmbedListV2(AppEmbedListRequest request);

    /**
     * 获取App 可以
     * @param appEmbedRequest
     * @return
     */
    Response<Map<String, Object>> getAppKey(AppEmbedRequest appEmbedRequest);

    /**
     * 应用嵌入详情查询
     * @param id
     * @return
     */
    Response<AppEmbedDO> queryAppEmbedById(Long id);

    /**
     * 应用嵌入详情查询（有鉴权）
    * @param str
     * @return
     */
    Response<AppEmbedVO> queryAppEmbedAuthById(String str);


    /**
     * 提供自定义鉴权, str为前后端定义的字符串,包含 app key 以及id等信息
     * @param str
     * @return
     */
    AppEmbedVO checkCustomCookie(String str);

    /**
     * 应用嵌入修改
     * @param appEmbedRequest
     * @return
     */
    Response<Boolean> updateAppEmbedById(AppEmbedRequest appEmbedRequest);


    /**
     * 应用嵌入获取url
     * @param appEmbedRequest request
     * @return url
     */
    Response<String> getEncryptCodeUrl(AppEmbedRequest appEmbedRequest);

    /**
     * 查询嵌入对象
     * @param request
     * @return
     */
    Response<List<AppEmbedOrgGroupVO>> queryAppEmbedObjectList(AppEmbedObjectListRequest request);

    Response<String> getKey(Long id);
}
