package com.bestpay.bigdata.bi.report.service.impl.ai;

import static com.bestpay.bigdata.bi.common.enums.NewCardTypeEnum.EMBED_TEXT_INDEX;

import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.api.RedisBaseService;
import com.bestpay.bigdata.bi.common.common.ChartTypeEnum;
import com.bestpay.bigdata.bi.common.config.AuthConfig;
import com.bestpay.bigdata.bi.common.dto.report.QueryReportConditionInfo;
import com.bestpay.bigdata.bi.common.entity.ColumnName;
import com.bestpay.bigdata.bi.common.enums.CodeEnum;
import com.bestpay.bigdata.bi.common.enums.NewCardTypeEnum;
import com.bestpay.bigdata.bi.common.error.DashboardErrorCode;
import com.bestpay.bigdata.bi.common.exception.BiException;
import com.bestpay.bigdata.bi.common.response.InsightAiResponse;
import com.bestpay.bigdata.bi.common.response.NorthStarResultResponse;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.common.util.HttpUtils;
import com.bestpay.bigdata.bi.common.util.StringUtil;
import com.bestpay.bigdata.bi.report.enums.ai.AIDashboardAnalysisStatusEnum;
import com.bestpay.bigdata.bi.report.enums.report.FieldDisplayTypeEnum;
import com.bestpay.bigdata.bi.report.request.ai.AIFindReportRequest;
import com.bestpay.bigdata.bi.report.request.dashboard.DashboardAnalysisByAIParamRequest;
import com.bestpay.bigdata.bi.report.request.dashboard.DashboardAnalysisByAIQueryRequest;
import com.bestpay.bigdata.bi.report.request.dashboard.DashboardAnalysisByAIRequest;
import com.bestpay.bigdata.bi.report.response.ai.AIFindReportResponse;
import com.bestpay.bigdata.bi.report.response.dashboard.DashboardAnalysisByAIResponse;
import com.bestpay.bigdata.bi.report.service.ai.AIService;
import com.bestpay.bigdata.bi.report.util.JsoupUtil;
import com.facebook.presto.jdbc.internal.google.api.client.util.Preconditions;
import com.opencsv.CSVWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;

/**
 * @author: tangye
 * @date: 2025/06/10
 */
@Slf4j
@Service
public class AIServiceImpl implements AIService {

  //解读报告cache时长(分钟)
  private static final int AI_ANALYSIS_REPORT_CACHE_TIME_MINUTES = 30;
  //调用AI侧解读报告接口超时时间(分钟)
  private static final int AI_ANALYSIS_REPORT_TIMEOUT_MINUTES = 10;
  @Resource
  private AuthConfig authConfig;
  @Resource
  private AIActionManager aiActionManager;
  @Resource
  private RedisBaseService redisService;

  /**
   * 普通表格
   * data里按key去ColumnNameMap取对应的头
   * @param columnNameMaps
   * @param data
   * @return
   */
  private void writeCSVDataListTable(CSVWriter csvWriter,List<ColumnName> columnNameMaps, List<Map<String, Object>> data) {
    //header
    String[] header=new String[columnNameMaps.size()];
    for(int i=0; i<columnNameMaps.size(); ++i){
      header[i]=columnNameMaps.get(i).getLabel();
    }
    csvWriter.writeNext(header);
    //data
    for(Map<String, Object> map : data){
      String[] line = new String[map.size()];
      for(int i = 0; i<line.length; ++i){
        line[i] = map.get(columnNameMaps.get(i).getProp()).toString();
      }
      csvWriter.writeNext(line);
    }
  }

  /**
   * 柱状图，条形图，折线图
   * data和header一一对齐
   * @param columnNameMaps
   * @param data
   * @return
   */
  private void writeCSVDataColumnChart(CSVWriter csvWriter,List<ColumnName> columnNameMaps, List<Map<String, Object>> data) {
    //列数要一样
    if(columnNameMaps.size() != data.size()){
      throw new BiException(DashboardErrorCode.DASHBOARD_NOT_SUPPORT_CARD_TYPE," 卡片数据格式有问题, header和data列数不一致.");
    }
    //header
    String[] header=new String[columnNameMaps.size()];
    for(int i=0; i<columnNameMaps.size(); ++i){
      header[i]=columnNameMaps.get(i).getLabel();
    }
    csvWriter.writeNext(header);
    //data
    List<List<String>> convertedList=new ArrayList<>();
    for(Map<String, Object> map : data){
      if(map.size() != 1){
        throw new BiException(DashboardErrorCode.DASHBOARD_NOT_SUPPORT_CARD_TYPE," 卡片数据格式有问题.");
      }
      //json数组
      log.debug("将object: {} 转换成List<String>",map.values().toArray()[0].toString());
      List<String> l=JSONUtil.toList(map.values().toArray()[0].toString(),String.class);
      convertedList.add(l);
    }
    int lineSize=convertedList.get(0).size();
    for(List<String> strList : convertedList) {
      if(strList.size() != lineSize){
        throw new BiException(DashboardErrorCode.DASHBOARD_NOT_SUPPORT_CARD_TYPE," 卡片数据格式有问题.");
      }
    }

    for(int i=0; i< lineSize; ++i){
      //一行数据
      String[] csvLine=new String[convertedList.size()];
      //填充每一列
      for(int j=0; j<convertedList.size(); ++j){
        csvLine[j]=convertedList.get(j).get(i);
      }
      csvWriter.writeNext(csvLine);
    }
  }

  /**
   * 环形图
   * data里按key去ColumnNameMap取对应的头
   * @param columnNameMaps
   * @param data
   * @return
   */
  private void writeCSVDataDonutChart(CSVWriter csvWriter,List<ColumnName> columnNameMaps, List<Map<String, Object>> data) {
    //header
    //多一列占比
    String[] header=new String[columnNameMaps.size()+1];
    for(int i=0; i<columnNameMaps.size(); ++i){
      header[i]=columnNameMaps.get(i).getLabel();
    }
    header[columnNameMaps.size()]="占比";
    csvWriter.writeNext(header);
    //data
    for(Map<String, Object> map : data){
      String[] line = new String[header.length];
      line[0]=map.get("name").toString();
      line[1]=map.get("value").toString();
      line[2]=map.get("percent_value").toString();
      csvWriter.writeNext(line);
    }
  }

  /**
   * 百分比堆积图
   * @param columnNameMaps
   * @param data
   * @return
   */
  private void writeCSVDataPerctChartChart(CSVWriter csvWriter,List<ColumnName> columnNameMaps, List<Map<String, Object>> data) {
    //添加上百分比列后与普通柱状图一样，百分比的列数和ColumnNameMap中的index数一样
    List<ColumnName> columnNameMapsCopy=new ArrayList<>(columnNameMaps);
    columnNameMaps.stream().filter(columnName -> columnName.getReportField().equals("index"))
        .forEach(columnName -> {
          ColumnName columnNameCopy=new ColumnName();
          columnNameCopy.setLabel(columnName.getLabel()+"百分比");
          //columnNameCopy.setProp(columnName.getProp()+"_percent");
          columnNameMapsCopy.add(columnNameCopy);
        });
    log.debug("百分比堆积图, 转换后的表头: {}", columnNameMapsCopy);
    writeCSVDataColumnChart(csvWriter,columnNameMapsCopy,data);
  }

  /**
   * 生成report类卡片CSV数据
   * @param csvWriter
   * @param columnNameMaps
   * @param data
   * @param chartType
   */
  private void writeCSVDataForReport(CSVWriter csvWriter,List<ColumnName> columnNameMaps, List<Map<String, Object>> data, ChartTypeEnum chartType) {
    switch(chartType) {
      case LIST_TABLE:
        writeCSVDataListTable(csvWriter,columnNameMaps,data);
        break;
      case BAR_CHART:
      case LINE_CHART:
      case COLUMN_CHART:
      case STACK_AND_LINE_CHART:
      case AREA_CHART:
      case STACK_CHART:
      case CLUSTER_AND_LINE_CHART:
      case BAR_STACKING_CHART:
        writeCSVDataColumnChart(csvWriter,columnNameMaps,data);
        break;
      case DONUT_CHART:
      case PIE_CHART:
        writeCSVDataDonutChart(csvWriter,columnNameMaps,data);
        break;
      case PERCENTAGE_CHART:
        writeCSVDataPerctChartChart(csvWriter,columnNameMaps,data);
        break;
    }
  }

  /**
   * 为report类卡片生成CSV数据，之所以单独提出一个方法是因为以后可能会扩展成后端查询数据
   * @param analysisByAIRequest
   * @return
   */
  private String getReportCSVDataForAI(DashboardAnalysisByAIRequest analysisByAIRequest) {

    //csv
    StringWriter sw=new StringWriter();
    CSVWriter csvWriter = new CSVWriter(sw);
    writeCSVDataForReport(csvWriter,
        analysisByAIRequest.getReturnData().getColumnNameMaps(),
        analysisByAIRequest.getReturnData().getData(),
        ChartTypeEnum.getChartType(analysisByAIRequest.getCardInfo().getChartType()));

    return sw.toString();
  }

  /**
   * 拼接筛选器信息为AI能理解的信息,比如：
   * 当前 分区日期 为 2025-06-03,当前 申请日期 为 2025-06-03
   * @param queryConditionInfos
   * @return
   */
  private String generateFilterText(List<QueryReportConditionInfo> queryConditionInfos) {
    log.debug("generating filter text: {}", JSONUtil.toJsonStr(queryConditionInfos));
    List<String> result=new ArrayList<>();
    for(QueryReportConditionInfo info : queryConditionInfos) {
      String str="当前 "+info.getCardName()+" 为 ";
      //当前只支持日期时间
      if(StringUtil.isNotEmpty(info.getStringValue()) && FieldDisplayTypeEnum.DATETIME.getCode().equals(info.getShowTypeName())){
        str+=info.getStringValue();
      }
      result.add(str);
    }

    return StringUtil.join(result,",");
  }

  /**
   * 把charType转换成AI能理解的信息
   * @param type
   * @return
   */
  private static String getChartTypeMessage(ChartTypeEnum type) {
    if(null == type){
      return "未知";
    }
    switch (type) {
      case COLUMN_CHART:
        return "柱状图";
      case LINE_CHART:
        return "折线图";
      case DONUT_CHART:
        return "环形图";
      case BAR_CHART:
        return "条形图";
      case CLUSTER_AND_LINE_CHART:
        return "簇状图";
      case STACK_CHART:
        return "堆积图";
      case STACK_AND_LINE_CHART:
        return "堆积折线图";
      case AREA_CHART:
        return "面积图";
      case MAP_CHART:
        return "地图";
      case RECTANGLE_CHART:
        return "矩形树图";
      case PERCENTAGE_CHART:
        return "百分比堆积图";
      case BAR_STACKING_CHART:
        return "条形堆积图";
      case SAN_KEY_CHART:
        return "桑基图";
      case SCATTER_CHART:
        return "散点图";
      case PIE_CHART:
        return "饼图";
      case FUNNEL_CHART:
        return "漏斗图";
      case RADAR_CHART:
        return "雷达图";
      case WATER_FALL_CHART:
        return "瀑布图";
      case INDEX:
        return "指标";
      case LIST_TABLE:
        return "表格";
      default:
        return "未知";
    }
  }

  /**
   * 生成图表及表格AI用数据
   * @param analysisByAIRequest
   * @return
   */
  private DashboardAnalysisByAIParamRequest getReportInfoForAI(DashboardAnalysisByAIRequest analysisByAIRequest) {
    String name = Optional.ofNullable(analysisByAIRequest.getCardInfo().getCardName())
        .filter(n -> !StringUtil.isEmpty(n))
        .orElse(analysisByAIRequest.getCardInfo().getReportName());

    ChartTypeEnum chartType=ChartTypeEnum.getChartType(analysisByAIRequest.getCardInfo().getChartType());
    String csvData=getReportCSVDataForAI(analysisByAIRequest);
    String filterTxt=generateFilterText(analysisByAIRequest.getParamsInfo().getQueryConditions());
    return DashboardAnalysisByAIParamRequest.builder()
        .cardName(name)
        .chartType(getChartTypeMessage(chartType))
        .data(csvData)
        .filter(filterTxt)
        .build();
  }

  /**
   * 生成指标文本AI用数据
   * @param analysisByAIRequest
   * @return
   */
  private DashboardAnalysisByAIParamRequest getIndexTextInfoForAI(DashboardAnalysisByAIRequest analysisByAIRequest) {
    String filterTxt=generateFilterText(analysisByAIRequest.getParamsInfo().getQueryConditions());
    List<String> lines=new ArrayList<>();
    for(Map map : analysisByAIRequest.getReturnData().getData()) {
      String name=(String)map.get("name");
      String val=(String)map.get("value");
      lines.add(name+" 为 "+val);
    }
    String inputText=StringUtil.join(lines,",");

    String name = Optional.ofNullable(analysisByAIRequest.getCardInfo().getCardName())
        .filter(n -> !StringUtil.isEmpty(n))
        .orElse(analysisByAIRequest.getCardInfo().getReportName());
    ChartTypeEnum chartType=ChartTypeEnum.getChartType(analysisByAIRequest.getCardInfo().getChartType());

    return DashboardAnalysisByAIParamRequest.builder()
        .cardName(name)
        .chartType(null == chartType ? "指标文本" : getChartTypeMessage(chartType))
        .indexText(inputText)
        .filter(filterTxt)
        .build();
  }

  /**
   * 生成带指标引用文本AI用数据
   * @param analysisByAIRequest
   * @return
   */
  private DashboardAnalysisByAIParamRequest getEmbedTextIndexInfoForAI(DashboardAnalysisByAIRequest analysisByAIRequest) {

    return DashboardAnalysisByAIParamRequest.builder()
        .indexText(JsoupUtil.getText(analysisByAIRequest.getReturnData().getText()))
        .chartType(EMBED_TEXT_INDEX.getMessage())
        .build();
  }

  @Override
  public List<DashboardAnalysisByAIParamRequest> getDashboardInfoForAI(List<DashboardAnalysisByAIRequest> analysisByAIRequest) {
    List<DashboardAnalysisByAIParamRequest> jsonResult=new ArrayList<>();

    for (DashboardAnalysisByAIRequest singleObj : analysisByAIRequest) {
      if(Objects.isNull(singleObj.getParamsInfo())){
        continue;
      }
      //先把各类型卡片信息和数据拼成JSON
      NewCardTypeEnum cardTypeEnum = NewCardTypeEnum.getByCode(singleObj.getParamsInfo().getCardType());
      DashboardAnalysisByAIParamRequest jsonForAI;
      try{
        //如果cardTypeEnum是null,则属于非法数据,这个卡片会被忽略
        switch (cardTypeEnum) {
          case REPORT:
            jsonForAI =  getReportInfoForAI(singleObj);
            break;
          case INDEX_TEXT:
            jsonForAI = getIndexTextInfoForAI(singleObj);
            break;
          case EMBED_TEXT_INDEX:
            jsonForAI = getEmbedTextIndexInfoForAI(singleObj);
            break;
          default:
            throw new BiException(DashboardErrorCode.DASHBOARD_NOT_SUPPORT_CARD_TYPE, "不支持的卡片类型, cardType: " + singleObj.getParamsInfo().getCardType());
        }
        log.debug("card: {}, generated for AI: {}", singleObj.getParamsInfo().getCardCode(), JSONUtil.toJsonStr(jsonForAI));
        jsonResult.add(jsonForAI);
      } catch (Exception e) {
        log.error("仪表板卡片生成AI数据失败, 这个卡片将被忽略, cardCode: {}", singleObj.getParamsInfo().getCardCode(),e);
      }
    }

    return jsonResult;
  }

  private String doAIRequest(Map<String, String> headers, Map<String, Object> body, String url) throws Exception{
    //返回结果
    InsightAiResponse response;
    final int MAX_TRY_CNT = 3;
    Exception lastException = null;
    // 如果失败，则重试三次
    for (int i = 1; i <= MAX_TRY_CNT; i++) {
      try {
        // 调用AI时间比较长，这里先设置为10分钟  1000 * 60 * 10
        String s = HttpUtils.sendHttpRequest(Method.POST, url,
            headers, body, 1000 * 60 * AI_ANALYSIS_REPORT_TIMEOUT_MINUTES);
        log.debug("调用insight结果返回：{}", s);
        response = JSONUtil.toBean(s, InsightAiResponse.class);
        Preconditions.checkArgument(200 == response.getCode(), "调用AI接口状态返回失败");
        String dataStr = String.valueOf(response.getData());
        NorthStarResultResponse data = JSONUtil.toBean(dataStr, NorthStarResultResponse.class);
        String reply = data.getOutputs().getReply();
        log.debug("AI调用结果：{}", reply);
        return reply;
      } catch (Exception e) {
        lastException = e;
        log.error("第:{}次,调用insight ai接口失败(5秒后再次尝试): ", i, e);
        try{Thread.sleep(5000);}catch(Exception ignored){}
      }
    }
    //最后还是失败则彻底失败，抛出最近的异常
    throw lastException;
  }

  private void doDashboardAIAnalysisAction(List<DashboardAnalysisByAIParamRequest> jsonResult, String key) {
    //自己处理所有异常,不要交给后台线程池
    try {
      //调用AI侧接口获取解读报告
      //header
      Map<String, String> headers = new HashMap<>();
      headers.put("Content-Type", "application/json");
      headers.put("apiKey", authConfig.getDashboardAIAnalysisAPIKey());
      //body
      Map<String, Object> body = new HashMap<>();
      body.put("appId", authConfig.getDashboardAIAnalysisAppID());
      body.put("chatId", UUID.randomUUID().toString());
      body.put("userId", "bigdata-bi");
      Map<String, Object> query = new HashMap<>();
      query.put("query", JSONUtil.toJsonStr(jsonResult));
      query.put("bi_list", jsonResult);
      body.put("input_variables", query);

      //返回结果
      String reply = doAIRequest(headers,body,authConfig.getDashboardAIAnalysisUrl());
      //更新redis里解读状态和结果
      DashboardAnalysisByAIResponse analysisResponse = DashboardAnalysisByAIResponse
          .builder()
          .key(key)
          .aiOutput(reply)
          .status(AIDashboardAnalysisStatusEnum.SUCCESS.getCode())
          .build();

      if(!redisService.setObj(key, analysisResponse, AI_ANALYSIS_REPORT_CACHE_TIME_MINUTES, TimeUnit.MINUTES)){
        log.error("解读完成但是更新缓存信息失败.");
      }

    } catch (Exception e) {
      log.error("调用insight ai接口并更新缓存结果失败: ", e);
      //将redis中的解读记录标记为失败
      DashboardAnalysisByAIResponse analysisResponse = DashboardAnalysisByAIResponse
          .builder()
          .key(key)
          .aiOutput(e.getMessage())
          .status(AIDashboardAnalysisStatusEnum.FAILED.getCode())
          .build();

      if(!redisService.setObj(key, analysisResponse, AI_ANALYSIS_REPORT_CACHE_TIME_MINUTES, TimeUnit.MINUTES)){
        log.error("解读失败更新缓存信息失败.");
      }
    }
  }

  /**
   * 根据传入AI的信息生成key,有任何信息变动则重新解读,排除chartType
   * @param jsonResult
   * @return
   */
  private String generateKey(List<DashboardAnalysisByAIParamRequest> jsonResult){
    List<DashboardAnalysisByAIParamRequest> copyForHash = new ArrayList<>();
    for (DashboardAnalysisByAIParamRequest request : jsonResult) {
      DashboardAnalysisByAIParamRequest copy = DashboardAnalysisByAIParamRequest.builder()
          .cardName(request.getCardName())
          .data(request.getData())
          .indexText(request.getIndexText())
          .filter(request.getFilter())
          .build();
      copyForHash.add(copy);
    }

    return DigestUtils.md5Hex(JSONUtil.toJsonStr(copyForHash));
  }

  @Override
  public Response<DashboardAnalysisByAIResponse> getDashboardAnalysisFromAI(
      List<DashboardAnalysisByAIRequest> analysisByAIRequest) {

    List<DashboardAnalysisByAIParamRequest> jsonResult = getDashboardInfoForAI(analysisByAIRequest);
    if(jsonResult.isEmpty()){
      return Response.ok(DashboardAnalysisByAIResponse
          .builder()
          .aiOutput("没有信息可供AI解读.")
          .status(AIDashboardAnalysisStatusEnum.FAILED.getCode())
          .build());
    }
    //忽略chartType生成key
    String key=generateKey(jsonResult);
    log.debug("generated key: {}", key);
    //如果缓存里有结果，则直接返回
    Optional<DashboardAnalysisByAIResponse> responseInCache=redisService.getObj(key);
    if(responseInCache.isPresent()) {
      log.debug("result in cache: {}, {}", key, responseInCache.get());
      return Response.ok(responseInCache.get());
    }
    //否则提交AI解读
    DashboardAnalysisByAIResponse response = DashboardAnalysisByAIResponse
        .builder()
        .key(key)
        .aiOutput("AI解读中...")
        .status(AIDashboardAnalysisStatusEnum.ANALYSING.getCode())
        .build();
    //在redis注册解读记录,初始内容为"解读中"
    boolean isSet=redisService.setObj(key, response, AI_ANALYSIS_REPORT_CACHE_TIME_MINUTES, TimeUnit.MINUTES);
    if(!isSet){
      log.error("无法注册解读记录到缓存, 当前redis可能不可用.");
      return Response.ok(DashboardAnalysisByAIResponse
          .builder()
          .aiOutput("无法注册解读记录到缓存, 解读失败.")
          .status(AIDashboardAnalysisStatusEnum.FAILED.getCode())
          .build());
    }
    //提交后台执行
    boolean isSubmited=aiActionManager.submit(new Runnable() {
      @Override
      public void run() {
        doDashboardAIAnalysisAction(jsonResult, key);
      }
    });

    if(!isSubmited){
      log.error("AI解读报告任务提交失败,可能是队列已满.");
      return Response.ok(DashboardAnalysisByAIResponse
          .builder()
          .aiOutput("AI解读报告任务提交失败,可能是队列已满.")
          .status(AIDashboardAnalysisStatusEnum.FAILED.getCode())
          .build());
    }

    return Response.ok(response);
  }

  @Override
  public Response<DashboardAnalysisByAIResponse> queryDashboardAnalysisFromAIResult(
      DashboardAnalysisByAIQueryRequest analysisByAIQueryRequest) {
    log.debug("query by key, request: {}", analysisByAIQueryRequest);
    Optional<DashboardAnalysisByAIResponse> response=redisService.getObj(analysisByAIQueryRequest.getKey());
    return response.map(Response::ok).orElseGet(() -> Response.ok(
        DashboardAnalysisByAIResponse
            .builder()
            .key(analysisByAIQueryRequest.getKey())
            .aiOutput("查询AI解读报告失败, 该key没有对应解读报告.")
            .status(AIDashboardAnalysisStatusEnum.FAILED.getCode())
            .build()));
  }

  @Override
  public Response<AIFindReportResponse> findReport(AIFindReportRequest findReportRequest) {
    //调用AI侧接口获取解读报告
    //header
    Map<String, String> headers = new HashMap<>();
    headers.put("Content-Type", "application/json");
    headers.put("apiKey", authConfig.getFindReportAIAPIKey());
    //body
    Map<String, Object> body = new HashMap<>();
    body.put("appId", authConfig.getFindReportAIAppID());
    body.put("chatId", UUID.randomUUID().toString());
    body.put("userId", "bigdata-bi");
    Map<String, Object> query = new HashMap<>();
    query.put("query", findReportRequest.getQuery());
    body.put("input_variables", query);

    try {
      //返回结果
      String reply = doAIRequest(headers,body,authConfig.getFindReportAIUrl());
      //尝试将结果转成对应的对象
      AIFindReportResponse response;
      try {
        response = JSONUtil.toBean(reply, AIFindReportResponse.class);
        //这里确保message为空,目前和前端确定若message有内容只展示message而忽略其他信息
        response.setMessage(null);
      } catch (Exception e) {
        log.warn("调用AI侧接口成功,但是返回的数据有问题: {}", reply);
        //如果转换失败,则返回前端错误信息
        return Response.ok(AIFindReportResponse.builder().message("我没听懂你的问题, 请换种方式试试看.").build());
      }
      return Response.ok(response);
    } catch (Exception e) {
      //如果调用接口报错
      return Response.error(CodeEnum.CALL_AI_FAILED);
    }
  }
}
