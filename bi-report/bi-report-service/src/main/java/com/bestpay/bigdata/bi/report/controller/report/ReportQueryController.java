package com.bestpay.bigdata.bi.report.controller.report;

import cn.hutool.core.collection.ListUtil;
import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.common.util.UserContextUtil;
import com.bestpay.bigdata.bi.database.bean.AuthResourceTypeEnum;
import com.bestpay.bigdata.bi.database.bean.Collection;
import com.bestpay.bigdata.bi.report.bean.report.ReportInfoVo;
import com.bestpay.bigdata.bi.report.enums.download.FileFormatTypeEnum;
import com.bestpay.bigdata.bi.report.request.auth.ObjectAuthRequest;
import com.bestpay.bigdata.bi.report.request.report.DownloadApplyRequest;
import com.bestpay.bigdata.bi.report.request.report.MoveReportRequest;
import com.bestpay.bigdata.bi.report.request.report.QueryReportRequest;
import com.bestpay.bigdata.bi.report.request.report.ReportMarketRequest;
import com.bestpay.bigdata.bi.report.response.common.QueryIndexAndReportResponse;
import com.bestpay.bigdata.bi.report.response.report.ReportMarket;
import com.bestpay.bigdata.bi.report.response.report.TopKReportInfoResponse;
import com.bestpay.bigdata.bi.report.service.dashboard.TableauDownLoadService;
import com.bestpay.bigdata.bi.report.service.report.ReportQueryService;
import com.bestpay.bigdata.bi.report.util.AuthorityCheckUtil;
import com.bestpay.bigdata.bi.report.util.EngineQueryParamCheckUtil;
import com.google.common.base.Preconditions;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 报表查询
 * 内网使用
 * @Author: dengyanwei
 * @CreateDate: 2022/4/28 14:27
 */
@RestController
@RequestMapping("/biReport/reportQuery")
@Slf4j
@Api(value = "报表查询", tags = "报表查询")
public class ReportQueryController {
  @Resource
  private ReportQueryService reportQueryService;
  @Resource
  private TableauDownLoadService tableauDownLoadService;
  @Resource
  private EngineQueryParamCheckUtil engineQueryParamCheckUtil;
  @Resource
  private AuthorityCheckUtil authorityCheckUtil;

  @PostMapping("/syncQueryReport")
  @ApiOperation(httpMethod = "POST", value = "同步查询报表", notes = "同步查询报表")
  public Response<QueryIndexAndReportResponse> syncQueryReport(@RequestBody QueryReportRequest request) {

    // 权限校验
    ObjectAuthRequest authRequest = new ObjectAuthRequest();
    authRequest.setAuthResourceId(request.getReportId()+"");
    authRequest.setAuthResourceType(AuthResourceTypeEnum.report.name());
    authorityCheckUtil.checkOwnerAndAuth(authRequest);

    return reportQueryService.syncQueryReport(request);
  }

  @GetMapping("/reportInfo/{id}")
  @ApiOperation(httpMethod = "GET", value = "获取报表详情", notes = "获取报表详情")
  public Response<ReportInfoVo> getReportInfo(@PathVariable Long id){
    return reportQueryService.getReportInfo(id);
  }


  @PostMapping("/downloadApply")
  @ApiOperation(value = "查询报表下载申请", httpMethod = "POST")
  public Response<QueryIndexAndReportResponse> downloadApply(@RequestBody DownloadApplyRequest queryReportRequest) {
    engineQueryParamCheckUtil.check(queryReportRequest);
    DownloadApplyRequest.judgeJiraProcess(queryReportRequest);
    Integer isFormat = queryReportRequest.getIsFormat();
    Preconditions.checkArgument(Objects.nonNull(isFormat),"文件类型不能拿为空");
    Preconditions.checkArgument(Objects.nonNull(FileFormatTypeEnum.getByCode(isFormat)),"文件类型不存在");
    return reportQueryService.downloadApply(queryReportRequest);
  }

  @PostMapping("/getReportMarket")
  @ApiOperation(value = "报表市场接口", httpMethod = "GET", response = Response.class)
  public Response getReportMarket(@RequestBody ReportMarketRequest reportMarketRequest) {
    return Response.ok(reportQueryService.getReportMarket(reportMarketRequest));
  }


  @PostMapping("/getReportList")
  @ApiOperation(value = "报表市场接口", httpMethod = "GET", response = Response.class)
  public Response getReportList(@RequestBody ReportMarketRequest reportMarketRequest) {
    return Response.ok(reportQueryService.getReportList(reportMarketRequest));
  }


  @PostMapping("/move")
  @ApiOperation(httpMethod = "POST", value = "报表移动", notes = "报表移动")
  public Response<Boolean> moveReportLocation(@RequestBody MoveReportRequest moveReportRequest) {
    return reportQueryService.moveReportLocation(moveReportRequest);
  }

  @PostMapping("/createCollection")
  @ApiOperation(value = "收藏报表", httpMethod = "POST", response = Response.class)
  public Response<Long> createCollection(@RequestBody Collection collection) {
    return reportQueryService.createCollection(collection);
  }

  @PostMapping("/cancelCollection")
  @ApiOperation(value = "取消收藏报表", httpMethod = "POST", response = Response.class)
  public Response<Integer> cancelCollection(@RequestBody Collection collection) {
    return reportQueryService.cancelCollection(collection);
  }

  @PostMapping("/getCollectionList")
  @ApiOperation(value = "查询收藏报表列表", httpMethod = "POST", response = Response.class)
  public Response<List<ReportMarket>> getCollectionList(@RequestBody Collection collection) {
    return reportQueryService.getCollectionList(collection);
  }

  @GetMapping("/tableauDownLoad/{id}")
  @ApiOperation(value = "tableau下载", httpMethod = "GET", response = Response.class)
  public Response<String> getReportMarket(@PathVariable Integer id) throws IOException {

    return tableauDownLoadService.down(id);
  }

  @GetMapping("/topk")
  @ApiOperation(value = "按访问量取topk", httpMethod = "GET", response = Response.class)
  public Response<List<TopKReportInfoResponse>> topk(){
    //查询当前用户所属组织下的报表
    List<String> orgCodeList = new ArrayList<>();
    UserInfo userInfo = UserContextUtil.getUserInfo();
    if(Objects.nonNull(userInfo.getOrg())
      && Objects.nonNull(userInfo.getOrg().getCode())) {
      orgCodeList.add(userInfo.getOrg().getCode());
    }

    return reportQueryService.topk(orgCodeList);
  }
}
