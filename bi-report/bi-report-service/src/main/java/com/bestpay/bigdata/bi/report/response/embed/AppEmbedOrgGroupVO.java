package com.bestpay.bigdata.bi.report.response.embed;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "应用嵌入对象信息")
public class AppEmbedOrgGroupVO {

    @ApiModelProperty(value = "组织名称")
    private String orgName;

    @ApiModelProperty(value = "组织code")
    private String orgCode;

    @ApiModelProperty(value = "嵌入类型")
    private String embedType;

    @ApiModelProperty(value = "应用嵌入对象详情")
    private List<AppEmbedResourceItem> appEmbedResourceItems;

}
