package com.bestpay.bigdata.bi.report.service.impl.dashboard;

import static java.util.Objects.requireNonNull;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.dto.dashboard.DashboardPageSettingModel;
import com.bestpay.bigdata.bi.common.dto.dashboard.newcard.NewFilterCardDTO;
import com.bestpay.bigdata.bi.common.dto.dashboard.newcard.NewIndexCardDTO;
import com.bestpay.bigdata.bi.common.dto.dashboard.newcard.NewReportCardDTO;
import com.bestpay.bigdata.bi.common.dto.dashboard.newcard.NewTextCardDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.FilterComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.common.enums.FieldType;
import com.bestpay.bigdata.bi.common.enums.IndexCardTypeEnum;
import com.bestpay.bigdata.bi.common.enums.NewCardTypeEnum;
import com.bestpay.bigdata.bi.common.enums.StatusCodeEnum;
import com.bestpay.bigdata.bi.common.error.DashboardErrorCode;
import com.bestpay.bigdata.bi.common.exception.BiException;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import com.bestpay.bigdata.bi.common.request.TableCardPublishRequest;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.common.util.ReportDTOInstantiateUtil;
import com.bestpay.bigdata.bi.common.util.StringUtil;
import com.bestpay.bigdata.bi.database.api.common.DatePickerDAOService;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardCardService;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardConfigService;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardDaoService;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardFilterCardService;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardIndexTextCardService;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardReportCardService;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardTextCardService;
import com.bestpay.bigdata.bi.database.bean.dashboard.DashBoardConfigDO;
import com.bestpay.bigdata.bi.database.bean.dashboard.DashBoardConfigDTO;
import com.bestpay.bigdata.bi.database.bean.dashboard.Dashboard;
import com.bestpay.bigdata.bi.database.dao.common.DatePickerConfigDO;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardCardDO;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardFilterCardDO;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardIndexTextCardDO;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardReportCardDO;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardTextCardDO;
import com.bestpay.bigdata.bi.report.chart.handler.util.ReportOrderTypeEnum;
import com.bestpay.bigdata.bi.report.service.dashboard.DashboardReportCardProcessService;
import com.bestpay.bigdata.bi.report.service.dashboard.ReportNewTableCardDbService;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ReportNewTableCardDbServiceImpl implements ReportNewTableCardDbService
{

  @Autowired
  private DashboardCardService cardService;

  @Autowired
  private DashboardFilterCardService filterCardService;

  @Autowired
  private DashboardTextCardService textCardService;

  @Autowired
  private DashboardIndexTextCardService indexTextCardService;

  @Autowired
  private DashboardDaoService dashboardDaoService;

  @Autowired
  private DashboardReportCardService reportCardService;

  @Autowired
  private DashboardReportCardProcessService reportCardProcessService;

  @Resource
  private DatePickerDAOService pickerDAOService;
  @Resource
  private DashboardConfigService dashboardConfigService;

  @Override
  @Transactional(rollbackFor = Throwable.class)
  public Response<String> newPublishTableCardV2(TableCardPublishRequest request,
      Map<String, String> cardInfoMap,
      UserInfo user) {
    String email = user.getEmail();
    requireNonNull(request.getDashboardId(), "dashboard id can not be null");

    // 更新仪表板信息
    Dashboard dashboard = new Dashboard();
    dashboard.setId(request.getDashboardId());

    dashboard.setUpdatedAt(Instant.now());
    dashboard.setUpdatedBy(email);
    dashboard.setLatestPublishAt(Instant.now());
    dashboardDaoService.updateById(dashboard);

    if (CollectionUtil.isEmpty(request.getData())) {
      return Response.ok();
    }

    // 更新卡片信息
    delete(request, user);
    storeCardList(request.getDashboardId(), request.getData(), cardInfoMap, null, user);
    DashboardPageSettingModel pageSettingModel = request.getDashboardPageSettingModel();
    if (Objects.nonNull(pageSettingModel)){
      DashBoardConfigDO dashBoardConfigDO = new DashBoardConfigDO();
      dashBoardConfigDO.setDashboardId(request.getDashboardId());
      dashBoardConfigDO.setCreatedBy(email);
      dashBoardConfigDO.setUpdatedBy(email);
      dashBoardConfigDO.setPageWidthSetting(pageSettingModel.getPageWidthSetting());
      dashBoardConfigDO.setWidthPx(pageSettingModel.getWidthPx());
      dashBoardConfigDO.setWatermarkSetting(JSONUtil.toJsonStr(pageSettingModel.getWatermarkSetting()));
      dashBoardConfigDO.setBgSetting(Objects.nonNull(pageSettingModel.getBgSetting())
          ? JSONUtil.toJsonStr(pageSettingModel.getBgSetting()) :"");
      dashBoardConfigDO.setGridSetting(JSONUtil.toJsonStr(pageSettingModel.getGridSetting()));
      dashboardConfigService.insert(dashBoardConfigDO);
    }
    return Response.ok();
  }

  private void delete(TableCardPublishRequest request, UserInfo user) {

    // 删除主表数据
    Long dashboardId = request.getDashboardId();
    cardService.update(dashboardId, StatusCodeEnum.DELETE.getCode(), user.getAccount());

    // 删除过滤卡片
    filterCardService.update(dashboardId, StatusCodeEnum.DELETE.getCode(), user.getAccount());

    // 删除指标卡片
    indexTextCardService.delete(dashboardId, user.getAccount());

    // 删除报表卡片
    reportCardService.delete(dashboardId, user.getAccount());

    // 删除文本卡片
    textCardService.update(dashboardId, StatusCodeEnum.DELETE.getCode(), user.getAccount());
    // 删除 仪表板页面配置信息
    DashBoardConfigDTO configDTO = DashBoardConfigDTO.builder()
        .dashboardId(dashboardId)
        .statusCode(StatusCodeEnum.DELETE.getCode())
        .updatedBy(user.getAccount()).build();

    dashboardConfigService.updateStatus(configDTO);
  }

  private void storeCardList(Long dashboardId,
      List<TableCardPublishRequest.CardInfo> tableCardList,
      Map<String, String> cardInfoMap,
      Long pid,
      UserInfo user) {

    for (TableCardPublishRequest.CardInfo card : tableCardList) {
      Long cardId = null;
      NewCardTypeEnum cardType = NewCardTypeEnum.getByCode(card.getCardType());

      try {
        switch (cardType) {
          case REPORT:
            cardId = reportCardStoreHandler(cardInfoMap, dashboardId, card, pid, user);
            break;
          case FILTER:
            cardId = filterCardStoreHandler(cardInfoMap, dashboardId, card, pid, user);
            break;
          case TEXT:
            cardId = textCardStoreHandler(cardInfoMap, dashboardId, card, pid, user);
            break;
          case INDEX_TEXT:
            cardId = indexTextStoreHandler(cardInfoMap, dashboardId, card, pid, user);
            break;
          default:
            cardId = otherCardStoreHandler(dashboardId, card, pid, user);
            break; // Skip unknown card types
        }
      }catch (Exception e){
        log.error("storeCardList error, dashboardId={}, cardCode={}",
            dashboardId, card.getCardCode(),e);
        throw new BiException(DashboardErrorCode.DASHBOARD_STORE_CARD_LIST_ERROR,"storeCardList error, dashboardId="+
                dashboardId+", cardCode="+card.getCardCode());
      }

      if (CollUtil.isEmpty(card.getCardList())) {
        continue;
      }

      storeCardList(dashboardId, card.getCardList(), cardInfoMap, cardId, user);
    }
  }

  private Long otherCardStoreHandler(Long dashboardId,
      TableCardPublishRequest.CardInfo card,
      Long pid,
      UserInfo user) {

    // 插入卡片主表
    return insertMainTable(dashboardId, card,
        NewCardTypeEnum.getByCode(card.getCardType()), null, pid, user);
  }

  private Long reportCardStoreHandler(Map<String, String> cardInfoMap,
      Long dashboardId,
      TableCardPublishRequest.CardInfo card,
      Long pid,
      UserInfo user) {

    String redisCardInfo = cardInfoMap.get(card.getCardCode());
    log.info("reportCardStoreHandler redisCardInfo : {}", redisCardInfo);
    if (StringUtil.isEmpty(redisCardInfo)) {
      throw new BiException(DashboardErrorCode.DASHBOARD_REDIS_DATA_NOT_EXIST,String.format("report card, dashboardId=%s, cardCode=%s, 缓存中不存在卡片信息",
              dashboardId, card.getCardCode()));
    }

    NewReportCardDTO reportCard = ReportDTOInstantiateUtil.toNewReportCardDTO(redisCardInfo);
    DashboardReportCardDO report = reportCardProcessService.convertReportCardDo(reportCard);
    if(StringUtil.isNotEmpty(card.getCardName())) {
      report.setReportName(card.getCardName());
    }

    report.setDashboardId(dashboardId);
    report.setCreatedBy(user.getAccount());
    report.setUpdatedBy(user.getAccount());
    report.setDashboardId(dashboardId);
    report.setCreatedAt(new Date());
    report.setUpdatedAt(new Date());
    log.info("reportCardStoreHandler report info : {}", report);

    return store(dashboardId, card, pid, report, NewCardTypeEnum.REPORT, user);
  }

  private Long filterCardStoreHandler(Map<String, String> cardInfoMap,
      Long dashboardId,
      TableCardPublishRequest.CardInfo card,
      Long pid,
      UserInfo user) {

    String redisCardInfo = cardInfoMap.get(card.getCardCode());
    if (StringUtil.isEmpty(redisCardInfo)) {
      throw new BiException(DashboardErrorCode.DASHBOARD_REDIS_DATA_NOT_EXIST,String.format("filter card, dashboardId=%s, cardCode=%s, 缓存中不存在卡片信息",
              dashboardId, card.getCardCode()));
    }

    // filterCardDTO
    NewFilterCardDTO filterCardDTO
        = JSONUtil.toBean(redisCardInfo, NewFilterCardDTO.class);

    BeanUtils.copyProperties(card, filterCardDTO);

    // filterCardDO
    DashboardFilterCardDO filterCardDO = DashboardFilterCardDO.toDTO(filterCardDTO, user, dashboardId);
    return store(dashboardId, card, pid, filterCardDO, NewCardTypeEnum.FILTER, user);
  }

  public Long indexTextStoreHandler(Map<String, String> cardInfoMap,
      Long dashboardId,
      TableCardPublishRequest.CardInfo card,
      Long pid,
      UserInfo user) {

    String redisCardInfo = cardInfoMap.get(card.getCardCode());
    if (StringUtil.isEmpty(redisCardInfo)) {
      throw new BiException(DashboardErrorCode.DASHBOARD_REDIS_DATA_NOT_EXIST, String.format("index card, dashboardId=%s, cardCode=%s, 缓存中不存在卡片信息",
              dashboardId, card.getCardCode()));
    }

    // indexCardDTO
    NewIndexCardDTO indexCardDTO
        = JSONUtil.toBean(redisCardInfo, NewIndexCardDTO.class);

    // 发布情况下，前端没有传卡片名称
    if(StringUtil.isNotEmpty(card.getCardName())) {
      indexCardDTO.getIndexInfo().setName(card.getCardName());
    }

    // 位置信息
    BeanUtils.copyProperties(card, indexCardDTO);

    // indexTextCardDO
    DashboardIndexTextCardDO indexTextCardDO = toIndexTextCardDO(
        dashboardId, user, indexCardDTO, IndexCardTypeEnum.EMBED_DASHBOARD.getCode());
    return store(dashboardId, card, pid, indexTextCardDO, NewCardTypeEnum.INDEX_TEXT,
        user);
  }

  @Override
  public DashboardIndexTextCardDO toIndexTextCardDO(Long dashboardId, UserInfo user,
      NewIndexCardDTO indexCardDTO,
      String indexCardType) {

    DashboardIndexTextCardDO indexTextCardDO = new DashboardIndexTextCardDO();
    BeanUtils.copyProperties(indexCardDTO, indexTextCardDO);

    indexTextCardDO.setIndexInfo(JSONUtil.toJsonStr(indexCardDTO.getIndexInfo()));
    indexTextCardDO.setDragResult(JSONUtil.toJsonStr(indexCardDTO.getDragResult()));

    // 获取日期配置
    for (FilterComponentPropertyDTO filter : indexCardDTO.getFilterdragResult()) {
      if(FieldType.DATETIME.name().equalsIgnoreCase(filter.getShowTypeName())) {
        DatePickerConfigDO picker = new DatePickerConfigDO();
        BeanUtils.copyProperties(filter, picker);
        picker.setDefaultValue(JSONUtil.toJsonStr(filter.getDefaultValues()));
        Long pickerId = pickerDAOService.insert(picker);
        filter.setDatePickerId(pickerId);
      }
    }

    indexTextCardDO.setFilterdragResult(JSONUtil.toJsonStr(indexCardDTO.getFilterdragResult()));
    indexTextCardDO.setCountFiledList(JSONUtil.toJsonStr(indexCardDTO.getCountFiledList()));
    indexTextCardDO.setCardStyleConfig(JSONUtil.toJsonStr(indexCardDTO.getCardStyleConfig()));
    indexTextCardDO.setCreatedBy(user.getAccount());
    indexTextCardDO.setUpdatedBy(user.getAccount());
    indexTextCardDO.setDashboardId(dashboardId);
    indexTextCardDO.setIndexCardType(indexCardType);
    indexTextCardDO.setDatasetId(indexCardDTO.getIndexInfo().getDataSet());
    return indexTextCardDO;
  }

  private Long textCardStoreHandler(Map<String, String> cardInfoMap,
      Long dashboardId,
      TableCardPublishRequest.CardInfo card,
      Long pid,
      UserInfo user) {

    String redisCardInfo = cardInfoMap.get(card.getCardCode());
    if (StringUtil.isEmpty(redisCardInfo)) {
      throw new BiException(DashboardErrorCode.DASHBOARD_REDIS_DATA_NOT_EXIST,  String.format("text card, dashboardId=%s, cardCode=%s, 缓存中不存在卡片信息",
              dashboardId, card.getCardCode()));
    }

    // textCardDTO
    NewTextCardDTO textCardDTO
        = JSONUtil.toBean(redisCardInfo, NewTextCardDTO.class);
    BeanUtils.copyProperties(card, textCardDTO);

    // textCardDO
    DashboardTextCardDO textCardDO = new DashboardTextCardDO();
    BeanUtils.copyProperties(textCardDTO, textCardDO);

    textCardDO.setCreatedBy(user.getAccount());
    textCardDO.setUpdatedBy(user.getAccount());
    textCardDO.setDashboardId(dashboardId);
    textCardDO.setRefIndexIds(textCardDTO.getIndexIds());

    // 保存文本卡片信息
    return store(dashboardId, card, pid, textCardDO, NewCardTypeEnum.TEXT, user);
  }

  private Long store(Long dashboardId,
      TableCardPublishRequest.CardInfo card,
      Long pid,
      Object cardDO,
      NewCardTypeEnum newCardTypeEnum,
      UserInfo user) {

    Long cardId = null;
    switch (newCardTypeEnum) {
      case REPORT:
        DashboardReportCardDO insertReportCard = (DashboardReportCardDO) cardDO;
        if (Objects.isNull(insertReportCard.getOrderType())) {
          insertReportCard.setOrderType(ReportOrderTypeEnum.DEFAULT.getType());
        }
        cardId = reportCardService.insert(insertReportCard);
        break;
      case FILTER:
        cardId = filterCardService.insert((DashboardFilterCardDO) cardDO);
        break;
      case TEXT:
        cardId = textCardService.insert((DashboardTextCardDO) cardDO);
        break;
      case INDEX_TEXT:
        cardId = indexTextCardService.insert((DashboardIndexTextCardDO) cardDO);
        break;
      default:
        break;
    }

    // 插入卡片主表
    return insertMainTable(dashboardId, card, newCardTypeEnum, cardId, pid, user);
  }

  private Long insertMainTable(Long dashboardId,
      TableCardPublishRequest.CardInfo card,
      NewCardTypeEnum cardType,
      Long cardId,
      Long pid,
      UserInfo user) {

    // 插入卡片主表
    DashboardCardDO main = new DashboardCardDO();
    main.setName(card.getCardName());
    main.setDashboardId(dashboardId);
    main.setCardType(cardType.getCode());
    main.setLocation(JSONUtil.toJsonStr(card.getPcLocation()));
    main.setMobileLocation(JSONUtil.toJsonStr(card.getMobileLocation()));
    main.setCardUniqueKey(card.getCardCode());
    main.setPid(pid);
    main.setCardId(cardId);
    main.setCreatedBy(user.getAccount());
    main.setUpdatedBy(user.getAccount());
    main.setOrderNo(card.getOrderNo());
    main.setIsShow(card.getIsShow());
    return cardService.insert(main);
  }
}
