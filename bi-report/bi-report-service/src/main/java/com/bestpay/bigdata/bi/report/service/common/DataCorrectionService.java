package com.bestpay.bigdata.bi.report.service.common;

import com.bestpay.bigdata.bi.report.request.common.QueryStatRequest;

/**
 * @Author：Song
 * @Date：2024/11/28 9:48
 * @Desc:
 */
public interface DataCorrectionService {
    // sprint 58功能订正表格样式
    void tableStyle();

    // 算是sprint58的功能，发版后新增的一个功能
    void rotationAngleAndOffset();

    /**
     * 图形配置 柱状图 条形图。环形图。饼图 数据订正  订正坐标轴，图例，数据标签。基本上都是一些字体样式
     */
    void graphicsConf();

    /**
     *仪表板/表格支持配置分页 sprint 67 需求
     * @return
     */
    String dashboardAndReportSupportPage();


  /**
   * 表：t_object_subscribe
   * 进行sub_config字段的订正,增加邮箱数据的订正
   *
   * @return
   */
  String subscribeConfig();

    /**
     * 订正 t_query_status 表的user_org字段
     * @return 订正数量
     */
  int correctQueryStatOrg(QueryStatRequest request);

}
