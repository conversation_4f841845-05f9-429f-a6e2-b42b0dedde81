package com.bestpay.bigdata.bi.report.response.embed;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName AppEmbedResourceItem
 * @description 应用嵌入对象详情
 * @date 2025/6/5
 */
@Data
@ApiModel(description = "应用嵌入对象详情")
public class AppEmbedResourceItem {

  @ApiModelProperty(value = "嵌入对象id")
  private Long objectId;

  @ApiModelProperty(value = "嵌入对象名称")
  private String objectName;

  @ApiModelProperty(value = "访问地址")
  private String url;

  @ApiModelProperty(value = "负责人")
  private String ownerName;

  @ApiModelProperty(value = "仪表板更新时间")
  private String updateTime;
}
