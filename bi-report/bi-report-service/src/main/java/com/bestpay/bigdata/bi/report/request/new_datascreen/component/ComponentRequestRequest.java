package com.bestpay.bigdata.bi.report.request.new_datascreen.component;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@ApiModel(description = "组件请求参数")
public class ComponentRequestRequest {

  @ApiModelProperty(value = "数据大屏ID")
  @NotBlank(message = "数据大屏ID不能为空")
  private String dataScreenUUID;

  @ApiModelProperty(value = "组件类型")
  @NotBlank(message = "组件类型能为空")
  private String componentType;
  //@NotBlank(message = "组件编码不能为空")
  @ApiModelProperty(value = "组件code")
  private String componentCode;

  @ApiModelProperty(value = "组件信息")
  @NotNull(message = "组件信息不能为空")
  private Object componentInfo;
}
