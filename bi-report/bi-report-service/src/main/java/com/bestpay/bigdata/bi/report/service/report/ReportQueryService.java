package com.bestpay.bigdata.bi.report.service.report;

import com.bestpay.bigdata.bi.common.entity.ExtendQueryContext;
import com.bestpay.bigdata.bi.common.entity.QueryContext;
import com.bestpay.bigdata.bi.common.entity.ResultBlockMetadata;
import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.common.enums.FileType;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.database.bean.Collection;
import com.bestpay.bigdata.bi.report.bean.report.QueryReportMetaInfo;
import com.bestpay.bigdata.bi.report.bean.report.ReportInfoVo;
import com.bestpay.bigdata.bi.report.request.report.DownloadApplyRequest;
import com.bestpay.bigdata.bi.report.request.report.MoveReportRequest;
import com.bestpay.bigdata.bi.report.request.report.QueryReportRequest;
import com.bestpay.bigdata.bi.report.request.report.ReportMarketRequest;
import com.bestpay.bigdata.bi.report.request.report.ReportRequest;
import com.bestpay.bigdata.bi.report.response.common.QueryIndexAndReportResponse;
import com.bestpay.bigdata.bi.report.response.report.ReportDownloadVO;
import com.bestpay.bigdata.bi.report.response.report.ReportMarket;
import com.bestpay.bigdata.bi.report.response.report.TopKReportInfoResponse;
import java.io.IOException;
import java.util.List;

/**
 * 报表查询服务
 *
 * <AUTHOR>
 * @date 2022/02/16
 */
public interface ReportQueryService {

  /**
   * 获取报表详情
   * @return
   */
  Response<ReportInfoVo> getReportInfo(Long id);

  /**
   * 下载报表
   * @param reportRequest
   * @return
   */
  ReportDownloadVO download(DownloadApplyRequest reportRequest) throws IOException;


  /**
   * 下载报表申请
   *
   * @param reportRequest
   * @return
   */
  Response<QueryIndexAndReportResponse> downloadApply(DownloadApplyRequest reportRequest);

  /**
   * 同步查询报表
   *
   * @param reportRequest
   * @return
   */
  Response<QueryIndexAndReportResponse> syncQueryReport(QueryReportRequest reportRequest);

  /**
   * 查询 rowCount dataDate
   * @param queryContext
   * @param report
   * @return
   */
  Response<QueryReportMetaInfo> queryReportMetaInfo(ExtendQueryContext queryContext, ReportRequest report);

  /**
   * 生成 generate queryContext
   * @param report
   * @param userInfo
   * @return
   */
  ExtendQueryContext generateQueryContext(ReportRequest report, UserInfo userInfo);

  /**
   * 执行下载的sql任务
   * @param queryContext
   * @return
   */
  QueryContext executeDownloadQuery(ExtendQueryContext queryContext, FileType fileType);

  /**
   * 根据 block metadata get data from backend
   * @param blockMetadata
   * @return
   */
  List<List<String>> getDataBlockByMeta(ResultBlockMetadata blockMetadata);

  /**
   * 报表收藏
   *
   * @param collection  报表模板类
   * @return Response<Integer>
   */
  Response<Long> createCollection(Collection collection);

  /**
   * 取消报表收藏
   *
   * @param collection 报表模板类
   * @return Response<Integer>
   */
  Response<Integer> cancelCollection(Collection collection);

  /**
   * 查询收藏列表
   *
   * @param collection  报表模板类
   * @return Response<Integer>
   */
  Response<List<ReportMarket>> getCollectionList(Collection collection);

  /**
   * 获取报表市场 树形结构
   * @return
   */
  List<ReportMarket> getReportMarket(ReportMarketRequest reportMarketRequest);

  /**
   * 获取报表列表
   * @return
   */
  List<ReportMarket> getReportList(ReportMarketRequest reportMarketRequest);


  Response<Boolean> moveReportLocation(MoveReportRequest moveScriptRequest);

  /**
   * 报表访问量topk
   * @return
   */
  Response<List<TopKReportInfoResponse>> topk(List<String> orgCodeList);
}
