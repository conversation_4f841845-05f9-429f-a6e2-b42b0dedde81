package com.bestpay.bigdata.bi.report.service.dataset;

import com.bestpay.bigdata.bi.common.request.metaData.MetaDataQueryFieldRequest;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.database.dao.dataset.DatasetFieldDo;
import com.bestpay.bigdata.bi.report.request.dataset.MetaSyncRequest;
import com.bestpay.bigdata.bi.report.request.dataset.MetadataRequest;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface DatasetMetaDataService {

    /**
     * 获取元数据 表字段信息，并同步到本地表中
     * @param requests
     * @return
     */
    Map<String, List<DatasetFieldDo>> getMetaFields(List<MetaDataQueryFieldRequest> requests);

    /**
     * 元数据表字段同步
     * @param request
     * @return
     */
    Response<String> metaSync(MetaSyncRequest request);


    /**
     * 查询元数据库信息
     * @param metadataRequest
     * @return
     */
    Response<List<String>> queryMetaDatabases(MetadataRequest metadataRequest);


    /**
     * 查询元数据表信息
     * @param metadataRequest
     * @return
     */
    Response<List<String>> queryMetaDataTables(MetadataRequest metadataRequest);
}
