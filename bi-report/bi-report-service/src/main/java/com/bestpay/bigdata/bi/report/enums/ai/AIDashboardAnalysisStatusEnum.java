package com.bestpay.bigdata.bi.report.enums.ai;

import java.util.Objects;

/**
 * 仪表板AI解读报告状态枚举
 *
 * @Author: tangye
 * @CreateDate: 2025/6/11
 */
public enum AIDashboardAnalysisStatusEnum {

  /**
   * 解读中
   */
  ANALYSING(0,"解读中"),
  /**
   * 失败
   */
  FAILED(-1,"解读失败"),
  /**
   * 成功
   */
  SUCCESS(1,"解读成功");

  Integer code;
  String name;

  AIDashboardAnalysisStatusEnum(Integer code, String name) {
    this.code = code;
    this.name = name;
  }

  public Integer getCode() {
    return code;
  }

}
