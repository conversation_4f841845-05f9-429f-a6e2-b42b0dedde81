package com.bestpay.bigdata.bi.report.service.ai;

import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.database.bean.dashboard.Dashboard;
import com.bestpay.bigdata.bi.database.bean.dashboard.DashboardQuery;
import com.bestpay.bigdata.bi.report.request.ai.AIFindReportRequest;
import com.bestpay.bigdata.bi.report.request.dashboard.DashBoardCopyRequest;
import com.bestpay.bigdata.bi.report.request.dashboard.DashboardAnalysisByAIParamRequest;
import com.bestpay.bigdata.bi.report.request.dashboard.DashboardAnalysisByAIQueryRequest;
import com.bestpay.bigdata.bi.report.request.dashboard.DashboardAnalysisByAIRequest;
import com.bestpay.bigdata.bi.report.request.dashboard.DashboardPublishRequest;
import com.bestpay.bigdata.bi.report.request.dashboard.MoveDashboardRequest;
import com.bestpay.bigdata.bi.report.response.ai.AIFindReportResponse;
import com.bestpay.bigdata.bi.report.response.dashboard.DashboardAnalysisByAIResponse;
import com.bestpay.bigdata.bi.report.response.dashboard.DashboardDirectoryResponse;
import com.bestpay.bigdata.bi.report.response.dashboard.DashboardResponse;
import com.bestpay.bigdata.bi.report.response.dashboard.DashboardVo;
import java.util.List;
import javax.servlet.http.HttpServletRequest;

/**
 * @author: tangye
 * @date: 2025/06/10
 */
public interface AIService {

    /**
     * 为dashboard生成AI可理解的数据
     * @return List<DashboardAnalysisByAIParamRequest>
     */
    List<DashboardAnalysisByAIParamRequest> getDashboardInfoForAI(List<DashboardAnalysisByAIRequest> analysisByAIRequest);
    /**
     * 获取仪表盘AI解读报告
     * @return Response<DashboardAnalysisByAIResponse>
     */
    Response<DashboardAnalysisByAIResponse> getDashboardAnalysisFromAI(List<DashboardAnalysisByAIRequest> analysisByAIRequest);

    /**
     * 获取仪表盘AI解读报告状态和结果
     * @return Response<DashboardAnalysisByAIResponse>
     */
    Response<DashboardAnalysisByAIResponse> queryDashboardAnalysisFromAIResult(
        DashboardAnalysisByAIQueryRequest analysisByAIQueryRequest);

    /**
     * 智能找报表
     * @return Response<AIFindReportResponse>
     */
    Response<AIFindReportResponse> findReport(AIFindReportRequest findReportRequest);
}
