package com.bestpay.bigdata.bi.report.controller.ai;

import cn.hutool.core.collection.CollectionUtil;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.database.bean.AuthResourceTypeEnum;
import com.bestpay.bigdata.bi.report.enums.ai.AIDashboardAnalysisStatusEnum;
import com.bestpay.bigdata.bi.report.request.ai.AIFindReportRequest;
import com.bestpay.bigdata.bi.report.request.auth.ObjectAuthRequest;
import com.bestpay.bigdata.bi.report.request.dashboard.DashboardAnalysisByAIQueryRequest;
import com.bestpay.bigdata.bi.report.request.dashboard.DashboardAnalysisByAIRequest;
import com.bestpay.bigdata.bi.report.response.ai.AIFindReportResponse;
import com.bestpay.bigdata.bi.report.request.tablecard.TableCardDataRequest;
import com.bestpay.bigdata.bi.report.response.dashboard.DashboardAnalysisByAIResponse;
import com.bestpay.bigdata.bi.report.service.ai.AIService;
import com.bestpay.bigdata.bi.report.util.AuthorityCheckUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * ai+bi相关接口
 * @author: tangye
 * @date: 2025/06/10
 */
@Slf4j
@RestController
@RequestMapping("/biReport/ai")
@Api(value = "ai相关", tags = "ai相关")
public class AIController {

    @Resource
    private AIService aiService;
    @Resource
    private AuthorityCheckUtil authorityCheckUtil;

    /**
     * 获取仪表板AI解读报告
     * @param analysisByAIRequest
     * @return
     */
    @PostMapping("/getDashboardAnalysisFromAI")
    @ApiOperation(httpMethod = "POST", value = "获取仪表板AI解读报告", notes = "获取仪表板AI解读报告")
    public Response<DashboardAnalysisByAIResponse> getDashboardAnalysisFromAI(@RequestBody List<DashboardAnalysisByAIRequest> analysisByAIRequest) {
        if(CollectionUtil.isEmpty(analysisByAIRequest)) {
            return Response.ok(DashboardAnalysisByAIResponse
                .builder()
                .aiOutput("无内容需要AI解读.")
                .status(AIDashboardAnalysisStatusEnum.FAILED.getCode())
                .build());
        }

        // 提取非空 dashboard ID 并去重
        List<Long> dashboardIDs = analysisByAIRequest.stream()
            .map(request -> {
                TableCardDataRequest paramsInfo = request.getParamsInfo();
                return Objects.nonNull(paramsInfo) ? paramsInfo.getDashboardId() : null;
            })
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());

        //所有卡片有且只能有一个dashboard id
        if(dashboardIDs.size() != 1){
            log.warn("所有卡片只能属于一个dashboard: {}", dashboardIDs);
            return Response.ok(DashboardAnalysisByAIResponse
                .builder()
                .aiOutput("信息有误, AI无法解读, 请提交正确的仪表板数据.")
                .status(AIDashboardAnalysisStatusEnum.FAILED.getCode())
                .build());
        }

        // 权限校验
        ObjectAuthRequest request = new ObjectAuthRequest();
        request.setAuthResourceId(String.valueOf(dashboardIDs.get(0)));
        request.setAuthResourceType(AuthResourceTypeEnum.dashboard.name());
        authorityCheckUtil.checkOwnerAndAuth(request);

        return aiService.getDashboardAnalysisFromAI(analysisByAIRequest);
    }

    /**
     * 获取仪表板AI解读报告
     * @param analysisByAIQueryRequest
     * @return
     */
    @PostMapping("/queryDashboardAnalysisFromAIResult")
    @ApiOperation(httpMethod = "POST", value = "获取仪表板AI解读报告结果", notes = "获取仪表板AI解读报告结果")
    public Response<DashboardAnalysisByAIResponse> queryDashboardAnalysisFromAIResult(@RequestBody DashboardAnalysisByAIQueryRequest analysisByAIQueryRequest) {
        return aiService.queryDashboardAnalysisFromAIResult(analysisByAIQueryRequest);
    }

    /**
     * 智能找报表接口
     * @param findReportRequest
     * @return
     */
    @PostMapping("/findreport")
    @ApiOperation(httpMethod = "POST", value = "智能找报表接口", notes = "智能找报表接口")
    public Response<AIFindReportResponse> findReport(@RequestBody AIFindReportRequest findReportRequest) {
        return aiService.findReport(findReportRequest);
    }
}
