package com.bestpay.bigdata.bi.report.schedule.sender;

import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.report.schedule.sender.bean.BatchSendOTORequest;
import com.bestpay.bigdata.bi.report.schedule.sender.bean.DingTalkMarkdown;
import com.bestpay.bigdata.bi.report.schedule.sender.bean.DingTalkTypeBean;
import com.bestpay.bigdata.bi.report.schedule.sender.bean.DingTalkVisitRequest;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import com.bestpay.mobilebiz.mapi.facade.dto.request.HttpPostRequestDTO;
import org.apache.commons.net.util.Base64;

/**
 * <AUTHOR>
 * @create 2023-03-13-15:49
 */
public class TestDingTalk {

    public static void main(String[] args) throws  Exception{

        DingTalkTypeBean dingTalkBean = new DingTalkTypeBean();
        dingTalkBean.setWebHook("https://oapi.dingtalk.com/robot/send?access_token=9f3129bfa7d2b41e8c0334aab8a27335cd0c9f587e1f028293aef1679419a913");
        dingTalkBean.setSecret("SECbcf867ab3ca2ffc8e1f4869b3343a156f7f898c07820eebd02a1c5f986dc7eef");

        // get sign from timestamp and secret
        Long timestamp = System.currentTimeMillis();
        String stringToSign = timestamp + "\n" + dingTalkBean.getSecret();
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(dingTalkBean.getSecret().getBytes("UTF-8"), "HmacSHA256"));
        byte[] signData = mac.doFinal(stringToSign.getBytes("UTF-8"));
        String sign = URLEncoder.encode(new String(Base64.encodeBase64(signData)), "UTF-8");

        String dingTalkUrl = dingTalkBean
                .getWebHook()
                .concat("&")
                .concat("timestamp=")
                .concat(String.valueOf(timestamp))
                .concat("&")
                .concat("sign=")
                .concat(sign);

        String text = "<br>你的输入query是【生成报告】<br>你的输入日期是【2024-12-25】<br>返回的数据为：<br>__A指标的结果为1234.56__，年同比为<font color=\"#21B289\">150.89%</font>。#### <br> ![这是一张图片](https://oss.test.bestpay.net/gz/bigdata-bi-tnt-11/8J52_02/bigdata-bi_02/20241226/unAB6de9d8254f954707bad63f76924860ec28654729.jpg)";
        DingTalkMarkdown markdown = DingTalkMarkdown.builder().title("adfasd").text(text).build();
        BatchSendOTORequest batchSendOTORequest = new BatchSendOTORequest();

        batchSendOTORequest.setMsgKey("sampleMarkdown");
        batchSendOTORequest.setMsgParam(JSONUtil.toJsonStr(markdown));

        HttpPostRequestDTO requestDTO = new HttpPostRequestDTO();

        requestDTO.setPostType("JSON");
        Map<String, String> baseRequest = JSONUtil.toBean(JSONUtil.toJsonStr(batchSendOTORequest), Map.class);


        String s = baseRequest.get("msgParam");
        s = s.replaceAll("<br>", " \n\n ");
//            s = s.replaceAll("\\\\", "");
        baseRequest.put("msgParam",s);
        requestDTO.setRequestParams(baseRequest);

        // 连续通过mbpMapiFacade 调用会有系统繁忙提示
//            Thread.sleep(30000);
        Map<String, String> requestParams = requestDTO.getRequestParams();
        String s1 = requestParams.get("msgParam");
        s1 = s1.replaceAll("\\\\", "");
        requestParams.put("msgParam",s1);

        String s2 = requestDTO.getRequestParams().get("msgParam");
        DingTalkVisitRequest request = DingTalkVisitRequest.builder()
                .msgtype("markdown")
                .markdown(
                        DingTalkMarkdown.builder()
                                .text(s2)
                                .title("测试")
//                                .text(" \n " + "![这是一张图片test](" + "https://oss.bestpay.cn/gz/bigdata-bi-tnt-11/8J52_16/bigdata-bi_16/20231130/unAB9a80a604d430479e89efa863c4056c4c65870038.png" + ")")
                               .build()).build();


//        Map map = JSONUtil.toBean(JSONUtil.toJsonStr(request), Map.class);
        String body = HttpRequest.post(dingTalkUrl).body(JSONUtil.toJsonStr(request)).execute().body();
        System.out.println("------>" + body);

    }
}
