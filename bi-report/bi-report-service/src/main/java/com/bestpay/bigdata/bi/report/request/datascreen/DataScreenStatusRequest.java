package com.bestpay.bigdata.bi.report.request.datascreen;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("数据大屏更新状态请求")
public class DataScreenStatusRequest {

    @ApiModelProperty("大屏ID")
    @NotNull(message = "大屏ID不能为空")
    private Long dataScreenId;

    @ApiModelProperty("更新状态")
    @NotBlank(message = "更新状态不能为空")
    private String status;

}
