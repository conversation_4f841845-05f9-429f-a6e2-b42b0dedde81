package com.bestpay.bigdata.bi.report.service.report;

import com.bestpay.bigdata.bi.common.response.PageQueryVO;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.database.dao.warn.ReportWarnDo;
import com.bestpay.bigdata.bi.report.bean.warn.ReportWarnDTO;
import com.bestpay.bigdata.bi.report.bean.warn.ReportWarnListVO;
import com.bestpay.bigdata.bi.report.bean.warn.ReportWarnVO;
import com.bestpay.bigdata.bi.report.request.warn.ReportWarnEnumValueRequest;
import com.bestpay.bigdata.bi.report.request.warn.ReportWarnListRequest;
import com.bestpay.bigdata.bi.report.request.warn.ReportWarnRequest;
import com.bestpay.bigdata.bi.report.request.warn.ReportWarnStatusRequest;
import com.bestpay.bigdata.bi.report.response.common.QueryIndexAndReportResponse;
import com.bestpay.bigdata.bi.report.response.report.ReportWarnConfigVO;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface ReportWarnService {
    Response<List<ReportWarnConfigVO>> getConfigList(String reportId, String warnSourceType);

    Response<String> addOrUpdate(ReportWarnRequest request);

    Response<ReportWarnVO> detail(Long id);

    Response<Boolean> retry(Long id);

    Response<PageQueryVO<ReportWarnListVO>> list(ReportWarnListRequest request);

    Response<String> status(ReportWarnStatusRequest request);

    Response<QueryIndexAndReportResponse> querySelectValue(ReportWarnEnumValueRequest request);

    /**
     * 判断 告警报表是否下线，删除，告警中的配置 与报表中的维度、指标是否有过更改
     * @return
     */
    Response<Boolean> judgeWarnModify(ReportWarnDTO reportWarnDo);
}
