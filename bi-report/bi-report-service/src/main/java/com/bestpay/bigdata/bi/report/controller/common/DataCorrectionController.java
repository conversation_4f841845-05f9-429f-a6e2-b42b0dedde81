package com.bestpay.bigdata.bi.report.controller.common;

import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.report.correction.DashboardConfigReversion;
import com.bestpay.bigdata.bi.report.correction.DataAuthToDatasetAuthReversion;
import com.bestpay.bigdata.bi.report.request.common.QueryStatRequest;
import com.bestpay.bigdata.bi.report.service.common.DataCorrectionService;
import javax.annotation.Resource;
import javax.validation.Valid;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @create 2023-05-04-10:31
 */
@Slf4j
@RestController
@RequestMapping("/biReport/correct")
@Api(value = "数据订正控制器", tags = "数据订正控制器")
public class DataCorrectionController {
  @Resource
  private DataAuthToDatasetAuthReversion auth;

    @Resource
    private DashboardConfigReversion configReversion;


    @Resource
    private DataCorrectionService dataCorrectionService;

    @PostMapping("/correct/queryStat/org")
    public Response<Integer> correctQueryStatOrg(@RequestBody @Valid QueryStatRequest request){
      int count =dataCorrectionService.correctQueryStatOrg(request);
      return Response.ok(count);
    }

  @GetMapping("/auth")
  public Response auth() {
    auth.correct();
    return Response.ok("订正成功");
  }

    @GetMapping("/configReversion")
    public Response configReversion() {
        configReversion.reversion();
        return Response.ok("订正成功");
    }

    /**
     * 订正表格数据 NewReportStyleService
     * @return
     */
    @GetMapping("/tableStyle")
    @ApiOperation(httpMethod = "GET", value = "订正表格数据", notes = "订正表格数据")
    public Response tableStyle() {
        dataCorrectionService.tableStyle();
        return Response.ok("订正成功");
    }

    /**
     * 数据订正 数据标签旋转角度和偏移量
     * @return 订正结果
     */
    @GetMapping("/dataLabel/rotationAngle/offset")
    @ApiOperation(httpMethod = "GET", value = "数据订正 数据标签旋转角度和偏移量", notes = "数据订正 数据标签旋转角度和偏移量")
    public Response rotationAngleAndOffset() {
        dataCorrectionService.rotationAngleAndOffset();
        return Response.ok("订正成功");
    }

    /**
     * 图形配置 柱状图 条形图。环形图。饼图 数据订正  订正坐标轴，图例，数据标签。基本上都是一些字体样式 sprint 66 需求
     * @return 是否成功
     */
    @GetMapping("/graphicsConf")
    @ApiOperation(httpMethod = "GET", value = "图形配置 柱状图 条形图。环形图。饼图 数据订正", notes = "图形配置 柱状图 条形图。环形图。饼图 数据订正")
    public Response graphicsConf() {
        dataCorrectionService.graphicsConf();
        return Response.ok("订正成功");
    }

    /**
     * 仪表板/表格支持配置分页 sprint 67 需求
     * @return 是否成功
     */
    @GetMapping("/dashboardAndReport/supportPage")
    @ApiOperation(httpMethod = "GET", value = "仪表板/表格支持配置分页数据订正", notes = "仪表板/表格支持配置分页数据订正")
    public Response<String> dashboardAndReportSupportPage() {
        String tip = dataCorrectionService.dashboardAndReportSupportPage();
        return Response.ok(tip);
    }

  /**
   * 表：t_object_subscribe 进行sub_config字段的订正,增加邮箱数据的订正
   *
   * @return 是否成功
   */
  @GetMapping("/subScribeConfig")
  @ApiOperation(httpMethod = "GET", value = "订阅支持私人钉钉推送配置数据订正", notes = "订阅支持私人钉钉推送配置数据订正")
  public Response<String> subscribeConfig() {
    String tip = dataCorrectionService.subscribeConfig();
    return Response.ok(tip);
  }


}
