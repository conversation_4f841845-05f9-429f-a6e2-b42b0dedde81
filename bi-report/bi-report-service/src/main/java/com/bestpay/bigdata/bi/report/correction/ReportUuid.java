package com.bestpay.bigdata.bi.report.correction;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bestpay.bigdata.bi.common.dto.dataset.DatasetColumnConfigDTO;
import com.bestpay.bigdata.bi.common.dto.report.CustomColumnWidth;
import com.bestpay.bigdata.bi.common.dto.report.ReportSimpleColumn;
import com.bestpay.bigdata.bi.common.dto.report.ReportUuidGenerateUtil;
import com.bestpay.bigdata.bi.common.dto.report.TableConfiguration;
import com.bestpay.bigdata.bi.common.dto.warn.ReportWarnQueryDTO;
import com.bestpay.bigdata.bi.common.dto.warn.ReportWarnRuleDTO;
import com.bestpay.bigdata.bi.common.dto.warn.WarnConditionDTO;
import com.bestpay.bigdata.bi.common.enums.ReportResourceTypeEnum;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import com.bestpay.bigdata.bi.database.api.report.ReportWarnDAOService;
import com.bestpay.bigdata.bi.database.bean.report.ReportQueryDTO;
import com.bestpay.bigdata.bi.database.dao.report.NewReportDO;
import com.bestpay.bigdata.bi.database.dao.report.component.*;
import com.bestpay.bigdata.bi.database.dao.warn.ReportWarnDo;
import com.bestpay.bigdata.bi.database.dao.warn.ReportWarnRuleDo;
import com.bestpay.bigdata.bi.database.mapper.report.*;
import com.bestpay.bigdata.bi.database.mapper.warn.ReportWarnRuleMapper;
import com.bestpay.bigdata.bi.report.enums.report.ReportFieldEnum;
import com.bestpay.bigdata.bi.report.request.dataset.DatasetColumnConfigRequest;
import com.bestpay.bigdata.bi.report.service.dataset.DatasetService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: wybStart
 * @Date: 2025/6/9  11:18
 * @Description:
 */
@Slf4j
@Component
public class ReportUuid {

    @Resource
    private NewReportMapper newReportService;
    @Resource
    private DatasetService datasetService;
    @Resource
    private NewReportComputeMapper computeService;
    @Resource
    private NewReportDimensionMapper dimensionService;
    @Resource
    private NewReportContrastMapper contrastService;
    @Resource
    private NewReportIndexMapper indexService;
    @Resource
    private NewReportFilterMapper filterService;
    @Resource
    private NewReportConditionMapper conditionService;
    @Resource
    private NewReportKeywordMapper keywordService;
    @Resource
    private NewReportOrderMapper orderService;
    @Resource
    private NewReportStyleMapper styleService;
    @Resource
    private ReportWarnRuleMapper warnRuleDAOService;

    @Resource
    private ReportWarnDAOService warnDAOService;

    @Resource
    private UuidTestMapper uuidTestMapper;

    @Resource
    private ReportUuid reportUuid;

    public void reportMarketUuid() {
        // 查询主表中所有的报表数据
        List<NewReportDO> newReportDOS = newReportService.queryAllReport();

        List<NewReportComputeDO> reportComputeList = computeService.findAll(ReportResourceTypeEnum.REPORT.name(), null);
        Map<Long, List<NewReportComputeDO>> reportComputeMap = reportComputeList.stream()
                .collect(Collectors.groupingBy(NewReportComputeDO::getReportId));

        List<NewReportDimensionDO> dimensionComponentList = dimensionService.findAll(ReportResourceTypeEnum.REPORT.name(), null);
        Map<Long, List<NewReportDimensionDO>> reportDimensionMap = dimensionComponentList.stream()
                .collect(Collectors.groupingBy(NewReportDimensionDO::getReportId));

        List<NewReportContrastDO> contrastComponentList = contrastService.findAll(ReportResourceTypeEnum.REPORT.name(), null);
        Map<Long, List<NewReportContrastDO>> reportContrastMap = contrastComponentList.stream()
                .collect(Collectors.groupingBy(NewReportContrastDO::getReportId));

        List<NewReportIndexDO> indexComponentList = indexService.findAll(ReportResourceTypeEnum.REPORT.name(), null);
        Map<Long, List<NewReportIndexDO>> reportIndexMap = indexComponentList.stream()
                .collect(Collectors.groupingBy(NewReportIndexDO::getReportId));

        List<NewReportFilterDO> filterComponentList = filterService.findAll(ReportResourceTypeEnum.REPORT.name(), null);
        Map<Long, List<NewReportFilterDO>> reportFilterMap = filterComponentList.stream()
                .collect(Collectors.groupingBy(NewReportFilterDO::getReportId));

        List<NewReportConditionDO> conditionComponentList = conditionService.findAll(ReportResourceTypeEnum.REPORT.name(), null);
        Map<Long, List<NewReportConditionDO>> reportConditionMap = conditionComponentList.stream()
                .collect(Collectors.groupingBy(NewReportConditionDO::getReportId));

        List<NewReportKeywordDO> keywordComponentList = keywordService.findAll(ReportResourceTypeEnum.REPORT.name(), null);
        Map<Long, List<NewReportKeywordDO>> reportKeywordMap = keywordComponentList.stream()
                .collect(Collectors.groupingBy(NewReportKeywordDO::getReportId));

        List<NewReportOrderDO> orderComponentList = orderService.findAll(ReportResourceTypeEnum.REPORT.name(), null);
        Map<Long, List<NewReportOrderDO>> reportOrderMap = orderComponentList.stream()
                .collect(Collectors.groupingBy(NewReportOrderDO::getReportId));

        List<NewReportStyleDO> styleDOList = styleService.findAll(ReportResourceTypeEnum.REPORT.name(), null);
        Map<Long, List<NewReportStyleDO>> reportStyleMap = styleDOList.stream()
                .collect(Collectors.groupingBy(NewReportStyleDO::getReportId));

        List<ReportWarnDo> reportWarnList = warnDAOService.query(
                ReportWarnQueryDTO.builder().warnSourceType("report").build());
        Map<String, List<ReportWarnDo>> reportWarnMap = reportWarnList.stream()
                .collect(Collectors.groupingBy(ReportWarnDo::getReportId));

        List<ReportWarnRuleDo> warnRuleDos = warnRuleDAOService.query(
                ReportWarnRuleDTO.builder().build());
        Map<String, List<ReportWarnRuleDo>> reportWarnRuleMap = warnRuleDos.stream()
                .collect(Collectors.groupingBy(ReportWarnRuleDo::getWarnCode));

        /** key dataset id, value dataset_info */
        Map<String, List<DatasetColumnConfigDTO>> dataset_cache_map = new HashMap<>();

        log.info("报表市场，总计需要订正的报表个数: {}", newReportDOS.size());
        for (NewReportDO reportDO : newReportDOS) {
            Long datasetId = reportDO.getDatasetId();
            Long id = reportDO.getId();

            StringBuilder extraMessage = new StringBuilder();
            UuidTestDo uuidTestDo = new UuidTestDo();
            uuidTestDo.setObjectId(id);
            uuidTestDo.setObjectType(ReportResourceTypeEnum.REPORT.name());
            uuidTestDo.setInfoId(id);

            try {
                reportUuid.processSingleReport(extraMessage, reportDO, id, datasetId, dataset_cache_map,
                        reportComputeMap, reportDimensionMap, reportContrastMap, reportIndexMap,
                        reportFilterMap, reportConditionMap, reportKeywordMap, reportOrderMap, reportStyleMap,
                        reportWarnMap, reportWarnRuleMap);
            } catch (Exception e) {
                uuidTestDo.setMessage(ExceptionUtil.stacktraceToString(e, 1024));
                uuidTestMapper.insert(uuidTestDo);
            } finally {
                if (StringUtils.isNoneBlank(extraMessage.toString())) {
                    uuidTestDo.setMessage(extraMessage.toString());
                    uuidTestMapper.insert(uuidTestDo);
                }
            }
        }
        log.info("报表市场uuid订正完成...");
    }


    @Transactional(rollbackFor = Exception.class)
    public void processSingleReport(
                                    StringBuilder extraMessage,
                                    NewReportDO reportDO, Long id, Long datasetId,
                                    Map<String, List<DatasetColumnConfigDTO>> dataset_cache_map,
                                    Map<Long, List<NewReportComputeDO>> reportComputeMap,
                                    Map<Long, List<NewReportDimensionDO>> reportDimensionMap,
                                    Map<Long, List<NewReportContrastDO>> reportContrastMap,
                                    Map<Long, List<NewReportIndexDO>> reportIndexMap,
                                    Map<Long, List<NewReportFilterDO>> reportFilterMap,
                                    Map<Long, List<NewReportConditionDO>> reportConditionMap,
                                    Map<Long, List<NewReportKeywordDO>> reportKeywordMap,
                                    Map<Long, List<NewReportOrderDO>> reportOrderMap,
                                    Map<Long, List<NewReportStyleDO>> reportStyleMap,
                                    Map<String, List<ReportWarnDo>> reportWarnMap,
                                    Map<String, List<ReportWarnRuleDo>> reportWarnRuleMap) {

        // 1. 构建数据来源信息
        List<DatasetColumnConfigDTO> datasetColumnList = null;
        if (dataset_cache_map.containsKey(datasetId + "")) {
            datasetColumnList = dataset_cache_map.get(datasetId + "");
        } else {
            DatasetColumnConfigRequest configRequest = new DatasetColumnConfigRequest();
            configRequest.setDatasetId(datasetId);
            datasetColumnList = datasetService.getColumnConfigList(configRequest).getData();
            dataset_cache_map.put(datasetId + "", datasetColumnList);
        }
        if (CollUtil.isEmpty(datasetColumnList)) {
            throw new BusinessException("当前数据集可能被删除 dataset id " + datasetId);
        }

        List<NewReportComputeDO> reportComputeList = Lists.newArrayList();
        if (reportComputeMap.containsKey(id)) {
            reportComputeList = reportComputeMap.get(id);
            if (CollUtil.isNotEmpty(reportComputeList)) {
                for (NewReportComputeDO computeDO : reportComputeList) {
                    String newUuid = ReportUuidGenerateUtil.generateReportComputeUuid();
                    computeDO.setUuid(newUuid);
                }
            }
            if (CollUtil.isNotEmpty(reportComputeList)) {
                computeService.insertShade(reportComputeList);
            }
        }
        // 2. 解析report_structure
        List<ReportSimpleColumn> reportSimpleColumnList = JSONUtil.toList(reportDO.getReportStructure(), ReportSimpleColumn.class);
//        if (judgeUuidRepeat(reportSimpleColumnList)) {
//            throw new BusinessException("当前报表uuid 存在重复");
//        }
        for (ReportSimpleColumn simpleColumn : reportSimpleColumnList) {
            if (CollUtil.isNotEmpty(simpleColumn.getChildColumnList())) {
                for (ReportSimpleColumn column : simpleColumn.getChildColumnList()) {
                    try {
                        column.setUuid(matchNewUuid(column.getUuid(), column.getId(), column.getEnName(), column.getName(),
                                datasetColumnList, reportComputeList));
                    } catch (Exception e) {
                        extraMessage.append("report structure 订正失败，但是不熔断，可能存在字段已经被删了 datasetId ").append(datasetId).append(e.getMessage());
                    }
                }
            } else {
                try {
                    simpleColumn.setUuid(matchNewUuid(simpleColumn.getUuid(), simpleColumn.getId(), simpleColumn.getEnName(), simpleColumn.getName(),
                            datasetColumnList, reportComputeList));
                } catch (Exception e) {
                    extraMessage.append("report structure 订正失败，但是不熔断，可能存在字段已经被删了 datasetId ").append(datasetId).append(e.getMessage());
                }
            }
        }
        reportDO.setReportStructure(JSONUtil.toJsonStr(reportSimpleColumnList));

        // 3. 解析解密字段
        if (StringUtils.isNoneBlank(reportDO.getSensitiveFields())) {
            JSONArray sensitiveFieldList = JSON.parseArray(reportDO.getSensitiveFields());

            for (Object object : sensitiveFieldList) {
                String oldUuid = (String) ((JSONObject) object).get("uuid");
                Long tmpId = null;
                if (((JSONObject) object).get("id") != null) {
                    tmpId = Long.parseLong(((JSONObject) object).get("id").toString());
                }
                String enName = (String) ((JSONObject) object).get("enName");
                String originalName = (String) ((JSONObject) object).get("originalName");

                String newUuid = matchNewUuid(oldUuid, tmpId, enName, originalName, datasetColumnList, reportComputeList);
                ((JSONObject) object).put("uuid", newUuid);
                ((JSONObject) object).put("configUuid", ReportUuidGenerateUtil.generateReportConfigUuid());
            }
            reportDO.setSensitiveFields(sensitiveFieldList.toJSONString());
        }
        newReportService.insertShadeReport(reportDO);

        /** key old uuid, value new uuid */
        HashMap<String, String> uuidMapForWarn = new HashMap<>();
        /** key old uuid, value new config uuid */
        HashMap<String, String> uuidConfigMapForWarn = new HashMap<>();
        /** key old uuid, value new config measure uuid */
        HashMap<String, String> measureUuidMap = new HashMap<>();

        // 4. 子表 t_report_dimension_component
        List<NewReportDimensionDO> dimensionComponentList = reportDimensionMap.getOrDefault(id, Lists.newArrayList());
        for (NewReportDimensionDO dimensionDO : dimensionComponentList) {
            dimensionDO.setConfigUuid(ReportUuidGenerateUtil.generateReportConfigUuid());

            String oldUuid = dimensionDO.getUuid();
            if (StringUtils.isNoneBlank(dimensionDO.getReportField())
                    && ReportFieldEnum.MEASURE.getCode().equals(dimensionDO.getReportField())) {
                dimensionDO.setUuid(ReportUuidGenerateUtil.generateReportMeasureUuid());
                measureUuidMap.put(oldUuid, dimensionDO.getUuid());
            } else {
                String cnName = StringUtils.isNoneBlank(dimensionDO.getOriginalName())?dimensionDO.getOriginalName():dimensionDO.getName();
                dimensionDO.setUuid(matchNewUuid(dimensionDO.getUuid(), dimensionDO.getFieldId(), dimensionDO.getEnName(), cnName,
                        datasetColumnList, reportComputeList));
            }
            if (uuidMapForWarn.containsKey(oldUuid)) {
                extraMessage.append("维度中存在相同的uuid，可能会对预警造成影响");
            }
            uuidMapForWarn.put(oldUuid, dimensionDO.getUuid());
            uuidConfigMapForWarn.put(oldUuid, dimensionDO.getConfigUuid());
        }
        if (CollUtil.isNotEmpty(dimensionComponentList)) {
            dimensionService.insertShade(dimensionComponentList);
        }

        // 5. 子表 t_report_contrast_component
        List<NewReportContrastDO> contrastComponentList = reportContrastMap.getOrDefault(id, Lists.newArrayList());
        for (NewReportContrastDO contrastDO : contrastComponentList) {
            contrastDO.setConfigUuid(ReportUuidGenerateUtil.generateReportConfigUuid());
            String oldUuid = contrastDO.getUuid();
            if (StringUtils.isNoneBlank(contrastDO.getReportField())
                    && ReportFieldEnum.MEASURE.getCode().equals(contrastDO.getReportField())) {
                contrastDO.setUuid(ReportUuidGenerateUtil.generateReportMeasureUuid());
                measureUuidMap.put(oldUuid, contrastDO.getUuid());
            } else {
                String cnName = StringUtils.isNoneBlank(contrastDO.getOriginalName())?contrastDO.getOriginalName():contrastDO.getName();
                contrastDO.setUuid(matchNewUuid(contrastDO.getUuid(), contrastDO.getFieldId(), contrastDO.getEnName(), cnName,
                        datasetColumnList, reportComputeList));
            }
            if (uuidMapForWarn.containsKey(oldUuid)) {
                extraMessage.append("对比中存在相同的uuid，可能会对预警造成影响");
            }
            uuidMapForWarn.put(oldUuid, contrastDO.getUuid());
            uuidConfigMapForWarn.put(oldUuid, contrastDO.getConfigUuid());
        }
        if (CollUtil.isNotEmpty(contrastComponentList)) {
            contrastService.insertShade(contrastComponentList);
        }

        // 6. 子表 t_report_index_component
        List<NewReportIndexDO> indexComponentList = reportIndexMap.getOrDefault(id, Lists.newArrayList());
        for (NewReportIndexDO indexDO : indexComponentList) {
            indexDO.setConfigUuid(ReportUuidGenerateUtil.generateReportConfigUuid());
            String oldUuid = indexDO.getUuid();
            String cnName = StringUtils.isNoneBlank(indexDO.getOriginalName())?indexDO.getOriginalName():indexDO.getName();
            String enName = StringUtils.isNoneBlank(indexDO.getEnName())?indexDO.getEnName():indexDO.getFieldName();
            indexDO.setUuid(matchNewUuid(indexDO.getUuid(), indexDO.getFieldId(), enName, cnName,
                    datasetColumnList, reportComputeList));
            if (uuidMapForWarn.containsKey(oldUuid)) {
                extraMessage.append("指标中存在相同的uuid，可能会对预警造成影响");
            }
            uuidMapForWarn.put(oldUuid, indexDO.getUuid());
            uuidConfigMapForWarn.put(oldUuid, indexDO.getConfigUuid());
        }
        if (CollUtil.isNotEmpty(indexComponentList)) {
            indexService.insertShade(indexComponentList);
        }

        // 7. 子表 t_report_filter_component
        List<NewReportFilterDO> filterComponentList = reportFilterMap.getOrDefault(id, Lists.newArrayList());
        for (NewReportFilterDO filterDO : filterComponentList) {
            filterDO.setConfigUuid(ReportUuidGenerateUtil.generateReportConfigUuid());
            String cnName = StringUtils.isNoneBlank(filterDO.getOriginalName())?filterDO.getOriginalName():filterDO.getName();
            String enName = StringUtils.isNoneBlank(filterDO.getEnName())?filterDO.getEnName():filterDO.getFieldName();
            filterDO.setUuid(matchNewUuid(filterDO.getUuid(), filterDO.getFieldId(), enName, cnName,
                    datasetColumnList, reportComputeList));
        }
        if (CollUtil.isNotEmpty(filterComponentList)) {
            filterService.insertShade(filterComponentList);
        }

        // 8. 子表 t_report_condition_component
        List<NewReportConditionDO> conditionComponentList = reportConditionMap.getOrDefault(id, Lists.newArrayList());
        for (NewReportConditionDO conditionDO : conditionComponentList) {
            conditionDO.setConfigUuid(ReportUuidGenerateUtil.generateReportConfigUuid());
            String cnName = StringUtils.isNoneBlank(conditionDO.getOriginalName())?conditionDO.getOriginalName():conditionDO.getName();
            String enName = StringUtils.isNoneBlank(conditionDO.getEnName())?conditionDO.getEnName():conditionDO.getFieldName();
            conditionDO.setUuid(matchNewUuid(conditionDO.getUuid(), conditionDO.getFieldId(), enName, cnName,
                    datasetColumnList, reportComputeList));
        }
        if (CollUtil.isNotEmpty(conditionComponentList)) {
            conditionService.insertShade(conditionComponentList);
        }

        // 9. 子表 t_report_keyword_component
        List<NewReportKeywordDO> keywordComponentList = reportKeywordMap.getOrDefault(id, Lists.newArrayList());
        for (NewReportKeywordDO keywordDO : keywordComponentList) {
            keywordDO.setConfigUuid(ReportUuidGenerateUtil.generateReportConfigUuid());
            String cnName = StringUtils.isNoneBlank(keywordDO.getOriginalName())?keywordDO.getOriginalName():keywordDO.getName();
            keywordDO.setUuid(matchNewUuid(keywordDO.getUuid(), keywordDO.getFieldId(), keywordDO.getEnName(), cnName,
                    datasetColumnList, reportComputeList));
        }
        if (CollUtil.isNotEmpty(keywordComponentList)) {
            keywordService.insertShade(keywordComponentList);
        }

        // 10. 子表 t_report_order_component
        List<NewReportOrderDO> orderComponentList = reportOrderMap.getOrDefault(id, Lists.newArrayList());
        for (NewReportOrderDO orderDO : orderComponentList) {
            orderDO.setConfigUuid(ReportUuidGenerateUtil.generateReportConfigUuid());
            String cnName = StringUtils.isNoneBlank(orderDO.getOriginalName())?orderDO.getOriginalName():orderDO.getName();
            orderDO.setUuid(matchNewUuid(orderDO.getUuid(), orderDO.getFieldId(), orderDO.getEnName(), cnName,
                    datasetColumnList, reportComputeList));
        }
        if (CollUtil.isNotEmpty(orderComponentList)) {
            orderService.insertShade(orderComponentList);
        }

        // 11. 子表 t_report_style_config
        List<NewReportStyleDO> styleDOList = reportStyleMap.getOrDefault(id, Lists.newArrayList());
        if (CollUtil.isNotEmpty(styleDOList)) {
            NewReportStyleDO styleDO = styleDOList.get(0);
            if (StringUtils.isNoneBlank(styleDO.getTableConfiguration())) {

                TableConfiguration tableConfiguration = JSONUtil.toBean(styleDO.getTableConfiguration(),
                        TableConfiguration.class);
                if (tableConfiguration.getBasicFormat() != null
                        && CollUtil.isNotEmpty(tableConfiguration.getBasicFormat().getCustomColumnWidth())) {
                    List<CustomColumnWidth> customColumnWidth = tableConfiguration.getBasicFormat().getCustomColumnWidth();
                    for (CustomColumnWidth columnWidth : customColumnWidth) {
                        if ("度量值".equals(columnWidth.getColumnName()) && StringUtils.isBlank(columnWidth.getUuid())) {
                            continue;
                        }
                        columnWidth.setConfigUuid(uuidConfigMapForWarn.get(columnWidth.getUuid()));
                        if (!ReportFieldEnum.MEASURE.getCode().equals(columnWidth.getReportField())) {
                            if (StringUtils.isNoneBlank(columnWidth.getUuid())) {
                                String newUuid = uuidMapForWarn.get(columnWidth.getUuid());
                                if (StringUtils.isNoneBlank(newUuid)) {
                                    columnWidth.setUuid(newUuid);
                                } else {
                                    try {
                                        columnWidth.setUuid(matchNewUuid(columnWidth.getUuid(), null, null, columnWidth.getColumnName(),
                                                datasetColumnList, reportComputeList));
                                    } catch (Exception e) {
                                        extraMessage.append("自定义宽度存在数据问题" + e.getMessage());
                                    }
                                }
                            } else {
                                try {
                                    columnWidth.setUuid(matchNewUuid(columnWidth.getUuid(), null, null, columnWidth.getColumnName(),
                                            datasetColumnList, reportComputeList));
                                } catch (Exception e) {
                                    extraMessage.append("自定义宽度存在数据问题" + e.getMessage());
                                }
                            }

                        } else {
                            String newUuid = measureUuidMap.get(columnWidth.getUuid());
                            if (StringUtils.isNoneBlank(newUuid)) {
                                columnWidth.setUuid(newUuid);
                            } else {
                                throw new BusinessException("自定义宽度订正失败" + columnWidth.getUuid());
                            }
                        }
                    }
                    styleDO.setTableConfiguration(JSONUtil.toJsonStr(tableConfiguration));
                }
            }
            styleService.insertShadeStyle(styleDO);
        }

        // 12. 预警 t_report_warn_rule
        try {
            List<ReportWarnDo> reportWarnList = reportWarnMap.getOrDefault(id + "", Lists.newArrayList());
            for (ReportWarnDo warnDo : reportWarnList) {
                try {
                    String code = warnDo.getCode();
                    List<ReportWarnRuleDo> warnRuleDos = reportWarnRuleMap.getOrDefault(code, Lists.newArrayList());
                    for (ReportWarnRuleDo ruleDo : warnRuleDos) {

                        List<WarnConditionDTO> conditionDescList
                                = JSONUtil.toList(ruleDo.getConditionDesc(), WarnConditionDTO.class);

                        WarnConditionDTO expectationDesc = JSONUtil
                                .toBean(ruleDo.getExpectationDesc(), WarnConditionDTO.class);
                        for (WarnConditionDTO conditionDTO : conditionDescList) {
                            String newUuid = uuidMapForWarn.get(conditionDTO.getUuid());
                            String newConfigUuid = uuidConfigMapForWarn.get(conditionDTO.getUuid());
                            if (StringUtils.isBlank(newUuid) || StringUtils.isBlank(newConfigUuid)) {
                                throw new BusinessException("预警任务订正失败" + code);
                            }

                            conditionDTO.setUuid(newUuid);
                            conditionDTO.setConfigUuid(newConfigUuid);
                        }

                        String newUuid = uuidMapForWarn.get(expectationDesc.getUuid());
                        String newConfigUuid = uuidConfigMapForWarn.get(expectationDesc.getUuid());
                        if (StringUtils.isBlank(newUuid) || StringUtils.isBlank(newConfigUuid)) {
                            throw new BusinessException("预警任务订正失败" + code);
                        }
                        expectationDesc.setUuid(newUuid);
                        expectationDesc.setConfigUuid(newConfigUuid);

                        ruleDo.setConditionDesc(JSONUtil.toJsonStr(conditionDescList));
                        ruleDo.setExpectationDesc(JSONUtil.toJsonStr(expectationDesc));
                    }
                    warnRuleDAOService.batchShadeInsert(warnRuleDos);
                } catch (Exception e) {
                    UuidTestDo uuidTestDo = new UuidTestDo();
                    uuidTestDo.setObjectId(id);
                    uuidTestDo.setObjectType(ReportResourceTypeEnum.REPORT.name());
                    uuidTestDo.setInfoId(id);
                    uuidTestDo.setMessage(ExceptionUtil.stacktraceToString(e, 1024));
                    uuidTestMapper.insert(uuidTestDo);
                }
            }
        } catch (Exception e) {

        }
    }





    private String matchNewUuid(String oldUuid, Long fieldId, String enName, String cnName,
                                List<DatasetColumnConfigDTO> datasetColumnList,
                                List<NewReportComputeDO> reportComputeList) {
        // 优先处理匹配报表计算字段
        if (CollUtil.isNotEmpty(reportComputeList)) {
            for (NewReportComputeDO computeDO : reportComputeList) {
                if (fieldId != null) {
                    if (fieldId.equals(computeDO.getId())) {
                        return computeDO.getUuid();
                    }
                }
            }

            for (NewReportComputeDO computeDO : reportComputeList) {
                if (StringUtils.isNotBlank(enName)) {
                    if (enName.equals(computeDO.getEnName())) {
                        return computeDO.getUuid();
                    }
                }
            }

            for (NewReportComputeDO computeDO : reportComputeList) {
                if (StringUtils.isNotBlank(cnName)) {
                    if (cnName.equals(computeDO.getName())) {
                        return computeDO.getUuid();
                    }
                    // 特殊情况 对于日期字段，比如数据集为 日期，但是报表会分成 日期(年), 日期(月)等，匹配的时候需要将(x)去掉进行匹配
                    if (cnName.endsWith("(年)")
                            || cnName.endsWith("(月)")
                            || cnName.endsWith("(日)")
                            || cnName.endsWith("(周)")
                            || cnName.endsWith("(季)")
                            || cnName.endsWith("(时)")
                            || cnName.endsWith("(分)")
                            || cnName.endsWith("(秒)") ) {
                        String newCnName = cnName.substring(0, cnName.length() - 3);
                        if (newCnName.equals(computeDO.getName())) {
                            return computeDO.getUuid();
                        }
                    }
                }
            }
        }




        // 再匹配数据集配置的相关信息[ 基础字段、计算字段、参数 ]
        for (DatasetColumnConfigDTO configDTO : datasetColumnList) {
            if (fieldId != null) {
                if (fieldId.equals(configDTO.getId())) {
                    return configDTO.getUuid();
                }
            }
        }


        for (DatasetColumnConfigDTO configDTO : datasetColumnList) {
            if (StringUtils.isNotBlank(enName) && StringUtils.isNoneBlank(configDTO.getEnName())) {
                if (enName.equals(configDTO.getEnName())) {
                    return configDTO.getUuid();
                }

                if (enName.contains("(" + configDTO.getEnName() + ")")) {
                    return configDTO.getUuid();
                }

                if (enName.contains("(" + configDTO.getEnName() + ",")) {
                    return configDTO.getUuid();
                }

                if (configDTO.getEnName().contains("(" + enName +")")) {
                    return configDTO.getUuid();
                }

                if (configDTO.getEnName().contains("(" + enName +",")) {
                    return configDTO.getUuid();
                }
            }
        }

        for (DatasetColumnConfigDTO configDTO : datasetColumnList) {
            if (StringUtils.isNotBlank(enName) && StringUtils.isNoneBlank(configDTO.getOriginEnName())) {

                if (enName.equals(configDTO.getOriginEnName())) {
                    return configDTO.getUuid();
                }

                if (enName.contains("(" + configDTO.getOriginEnName() + ")")) {
                    return configDTO.getUuid();
                }

                if (enName.contains("(" + configDTO.getOriginEnName() + ",")) {
                    return configDTO.getUuid();
                }

                if (configDTO.getOriginEnName().contains("(" + enName +")")) {
                    return configDTO.getUuid();
                }

                if (configDTO.getOriginEnName().contains("(" + enName +",")) {
                    return configDTO.getUuid();
                }
            }
        }


        for (DatasetColumnConfigDTO configDTO : datasetColumnList) {
            if (StringUtils.isNotBlank(cnName)) {
                if (cnName.equals(configDTO.getName())) {
                    return configDTO.getUuid();
                }
                // 特殊情况 对于日期字段，比如数据集为 日期，但是报表会分成 日期(年), 日期(月)等，匹配的时候需要将(x)去掉进行匹配
                if (cnName.endsWith("(年)")
                        || cnName.endsWith("(月)")
                        || cnName.endsWith("(日)")
                        || cnName.endsWith("(周)")
                        || cnName.endsWith("(季)")
                        || cnName.endsWith("(时)")
                        || cnName.endsWith("(分)")
                        || cnName.endsWith("(秒)") ) {
                    String newCnName = cnName.substring(0, cnName.length() - 3);
                    if (newCnName.equals(configDTO.getName())) {
                        return configDTO.getUuid();
                    }
                }

            }
        }

        throw new BusinessException("匹配失败, 原始信息 oldUuid " + oldUuid + ", fieldId " + fieldId
                + ", enName " + enName + ", cnName " + cnName);
    }



    private boolean judgeUuidRepeat(List<ReportSimpleColumn> reportSimpleColumnList) {
        HashSet<String> uuidSet = new HashSet<>();

        for (ReportSimpleColumn reportSimpleColumn : reportSimpleColumnList) {
            if (uuidSet.contains(reportSimpleColumn.getUuid())) {
                return true;
            }
            uuidSet.add(reportSimpleColumn.getUuid());
        }
        return false;
    }







    public void reportMarketUuidValid() {
        // 查询主表中所有的报表数据
        ReportQueryDTO queryDTO = new ReportQueryDTO();
        List<NewReportDO> newReportDOS = newReportService.queryShadeAll(queryDTO);


        List<NewReportComputeDO> reportComputeList = computeService.findAllShade(ReportResourceTypeEnum.REPORT.name(), null);
        Map<Long, List<NewReportComputeDO>> reportComputeMap = reportComputeList.stream()
                .collect(Collectors.groupingBy(NewReportComputeDO::getReportId));

        List<NewReportDimensionDO> dimensionComponentList = dimensionService.findAllShade(ReportResourceTypeEnum.REPORT.name(), null);
        Map<Long, List<NewReportDimensionDO>> reportDimensionMap = dimensionComponentList.stream()
                .collect(Collectors.groupingBy(NewReportDimensionDO::getReportId));

        List<NewReportContrastDO> contrastComponentList = contrastService.findAllShade(ReportResourceTypeEnum.REPORT.name(), null);
        Map<Long, List<NewReportContrastDO>> reportContrastMap = contrastComponentList.stream()
                .collect(Collectors.groupingBy(NewReportContrastDO::getReportId));

        List<NewReportIndexDO> indexComponentList = indexService.findAllShade(ReportResourceTypeEnum.REPORT.name(), null);
        Map<Long, List<NewReportIndexDO>> reportIndexMap = indexComponentList.stream()
                .collect(Collectors.groupingBy(NewReportIndexDO::getReportId));

        List<NewReportFilterDO> filterComponentList = filterService.findAllShade(ReportResourceTypeEnum.REPORT.name(), null);
        Map<Long, List<NewReportFilterDO>> reportFilterMap = filterComponentList.stream()
                .collect(Collectors.groupingBy(NewReportFilterDO::getReportId));

        List<NewReportConditionDO> conditionComponentList = conditionService.findAllShade(ReportResourceTypeEnum.REPORT.name(), null);
        Map<Long, List<NewReportConditionDO>> reportConditionMap = conditionComponentList.stream()
                .collect(Collectors.groupingBy(NewReportConditionDO::getReportId));

        List<NewReportKeywordDO> keywordComponentList = keywordService.findAllShade(ReportResourceTypeEnum.REPORT.name(), null);
        Map<Long, List<NewReportKeywordDO>> reportKeywordMap = keywordComponentList.stream()
                .collect(Collectors.groupingBy(NewReportKeywordDO::getReportId));

        List<NewReportOrderDO> orderComponentList = orderService.findAllShade(ReportResourceTypeEnum.REPORT.name(), null);
        Map<Long, List<NewReportOrderDO>> reportOrderMap = orderComponentList.stream()
                .collect(Collectors.groupingBy(NewReportOrderDO::getReportId));

        List<NewReportStyleDO> styleDOList = styleService.findAllShade(ReportResourceTypeEnum.REPORT.name(), null);
        Map<Long, List<NewReportStyleDO>> reportStyleMap = styleDOList.stream()
                .collect(Collectors.groupingBy(NewReportStyleDO::getReportId));

        List<ReportWarnDo> reportWarnList = warnDAOService.query(
                ReportWarnQueryDTO.builder().warnSourceType("report").build());
        Map<String, List<ReportWarnDo>> reportWarnMap = reportWarnList.stream()
                .collect(Collectors.groupingBy(ReportWarnDo::getReportId));

        List<ReportWarnRuleDo> warnRuleDos = warnRuleDAOService.queryShade(
                ReportWarnRuleDTO.builder().build());
        Map<String, List<ReportWarnRuleDo>> reportWarnRuleMap = warnRuleDos.stream()
                .collect(Collectors.groupingBy(ReportWarnRuleDo::getWarnCode));

        /** key dataset id, value dataset_info */
        Map<String, List<DatasetColumnConfigDTO>> dataset_cache_map = new HashMap<>();

        log.info("报表市场，总计需要验证的报表个数: {}", newReportDOS.size());
        for (NewReportDO reportDO : newReportDOS) {
            Long datasetId = reportDO.getDatasetId();
            Long id = reportDO.getId();

            StringBuilder extraMessage = new StringBuilder();

            UuidTestDo uuidTestDo = new UuidTestDo();
            uuidTestDo.setObjectId(id);
            uuidTestDo.setObjectType(ReportResourceTypeEnum.REPORT.name() + "_validate");
            uuidTestDo.setInfoId(id);

            try {
                reportUuid.processSingleReportValidate(reportDO, id, datasetId, dataset_cache_map, extraMessage,
                        reportComputeMap, reportDimensionMap, reportContrastMap, reportIndexMap, reportFilterMap,
                        reportConditionMap, reportKeywordMap, reportOrderMap, reportStyleMap, reportWarnMap, reportWarnRuleMap);
            } catch (Exception e) {
                uuidTestDo.setMessage(ExceptionUtil.stacktraceToString(e, 512));
                uuidTestMapper.insert(uuidTestDo);
            }
        }
        log.info("报表市场uuid订正完成...");
    }



    @Transactional(rollbackFor = Exception.class)
    public void processSingleReportValidate(NewReportDO reportDO, Long id, Long datasetId, Map<String, List<DatasetColumnConfigDTO>> dataset_cache_map,
                                            StringBuilder extraMessage,
                                            Map<Long, List<NewReportComputeDO>> reportComputeMap,
                                            Map<Long, List<NewReportDimensionDO>> reportDimensionMap,
                                            Map<Long, List<NewReportContrastDO>> reportContrastMap,
                                            Map<Long, List<NewReportIndexDO>> reportIndexMap,
                                            Map<Long, List<NewReportFilterDO>> reportFilterMap,
                                            Map<Long, List<NewReportConditionDO>> reportConditionMap,
                                            Map<Long, List<NewReportKeywordDO>> reportKeywordMap,
                                            Map<Long, List<NewReportOrderDO>> reportOrderMap,
                                            Map<Long, List<NewReportStyleDO>> reportStyleMap,
                                            Map<String, List<ReportWarnDo>> reportWarnMap,
                                            Map<String, List<ReportWarnRuleDo>> reportWarnRuleMap
                                            ) {

        // 1. 构建数据来源信息
        List<DatasetColumnConfigDTO> datasetColumnList = null;
        if (dataset_cache_map.containsKey(datasetId + "")) {
            datasetColumnList = dataset_cache_map.get(datasetId + "");
        } else {
            DatasetColumnConfigRequest configRequest = new DatasetColumnConfigRequest();
            configRequest.setDatasetId(datasetId);
            datasetColumnList = datasetService.getColumnConfigList(configRequest).getData();
            dataset_cache_map.put(datasetId + "", datasetColumnList);
        }
        if (CollUtil.isEmpty(datasetColumnList)) {
            throw new BusinessException("当前数据集订正存在问题 dataset id " + datasetId);
        }
        List<NewReportComputeDO> reportComputeList = reportComputeMap.getOrDefault(id, Lists.newArrayList());
        if (CollUtil.isNotEmpty(reportComputeList)) {
            for (NewReportComputeDO computeDO : reportComputeList) {
                // 这里验证没啥意义
                matchNewUuidValid(
                        computeDO.getUuid(),
                        computeDO.getFieldId(),
                        computeDO.getEnName(),
                        computeDO.getName(),
                        datasetColumnList, reportComputeList);
            }
        }

        // 2. 解析report_structure
        List<ReportSimpleColumn> reportSimpleColumnList = JSONUtil.toList(reportDO.getReportStructure(), ReportSimpleColumn.class);
//        if (judgeUuidRepeat(reportSimpleColumnList)) {
//            throw new BusinessException("当前报表uuid 存在重复");
//        }
        for (ReportSimpleColumn simpleColumn : reportSimpleColumnList) {
            if (CollUtil.isNotEmpty(simpleColumn.getChildColumnList())) {
                for (ReportSimpleColumn column : simpleColumn.getChildColumnList()) {
                    try {
                        matchNewUuidValid(column.getUuid(), column.getId(), column.getEnName(), column.getName(),
                                datasetColumnList, reportComputeList);
                    } catch (Exception e) {
                        extraMessage.append("Report Structure 中存在订正失败，但是不熔断").append(e.getMessage());
                    }
                }
            } else {
                try {
                    matchNewUuidValid(simpleColumn.getUuid(), simpleColumn.getId(), simpleColumn.getEnName(), simpleColumn.getName(),
                            datasetColumnList, reportComputeList);
                } catch (Exception e) {
                    extraMessage.append("Report Structure 中存在订正失败，但是不熔断").append(e.getMessage());
                }
            }
        }

        // 3. 解析解密字段
        if (StringUtils.isNoneBlank(reportDO.getSensitiveFields())) {
            JSONArray sensitiveFieldList = JSON.parseArray(reportDO.getSensitiveFields());

            for (Object object : sensitiveFieldList) {
                String oldUuid = (String) ((JSONObject) object).get("uuid");
                Long tmpId = null;
                if (((JSONObject) object).get("id") != null) {
                    tmpId = Long.parseLong(((JSONObject) object).get("id").toString());
                }
                String enName = (String) ((JSONObject) object).get("enName");
                String originalName = (String) ((JSONObject) object).get("originalName");

                matchNewUuidValid(oldUuid, tmpId, enName, originalName, datasetColumnList, reportComputeList);
            }
        }

        /** value new uuid */
        HashSet<String> uuidSetForWarn = new HashSet<>();
        /** key old uuid, value new config uuid */
        HashSet<String> uuidConfigSetForWarn = new HashSet<>();
        /** value new config measure uuid */
        HashSet<String> measureUuidSet = new HashSet<>();

        // 4. 子表 t_report_dimension_component
        List<NewReportDimensionDO> dimensionComponentList = reportDimensionMap.getOrDefault(id, Lists.newArrayList());
        for (NewReportDimensionDO dimensionDO : dimensionComponentList) {
            if (StringUtils.isBlank(dimensionDO.getUuid()) || StringUtils.isBlank(dimensionDO.getConfigUuid())) {
                throw new BusinessException("当前报表维度表字段订正失败");
            }
            if (StringUtils.isNoneBlank(dimensionDO.getReportField())
                    && ReportFieldEnum.MEASURE.getCode().equals(dimensionDO.getReportField())) {
                measureUuidSet.add(dimensionDO.getUuid());
            } else {
                String cnName = StringUtils.isNoneBlank(dimensionDO.getOriginalName())?dimensionDO.getOriginalName():dimensionDO.getName();
                matchNewUuidValid(dimensionDO.getUuid(), dimensionDO.getFieldId(), dimensionDO.getEnName(), cnName,
                        datasetColumnList, reportComputeList);
            }
            uuidSetForWarn.add(dimensionDO.getUuid());
            uuidConfigSetForWarn.add(dimensionDO.getConfigUuid());
        }

        // 5. 子表 t_report_contrast_component
        List<NewReportContrastDO> contrastComponentList = reportContrastMap.getOrDefault(id, Lists.newArrayList());
        for (NewReportContrastDO contrastDO : contrastComponentList) {
            if (StringUtils.isBlank(contrastDO.getUuid()) || StringUtils.isBlank(contrastDO.getConfigUuid())) {
                throw new BusinessException("当前报表对比表字段订正失败");
            }
            if (StringUtils.isNoneBlank(contrastDO.getReportField())
                    && ReportFieldEnum.MEASURE.getCode().equals(contrastDO.getReportField())) {
                measureUuidSet.add(contrastDO.getUuid());
            } else {
                String cnName = StringUtils.isNoneBlank(contrastDO.getOriginalName())?contrastDO.getOriginalName():contrastDO.getName();
                matchNewUuidValid(contrastDO.getUuid(), contrastDO.getFieldId(), contrastDO.getEnName(), cnName,
                        datasetColumnList, reportComputeList);
            }
            uuidSetForWarn.add(contrastDO.getUuid());
            uuidConfigSetForWarn.add(contrastDO.getConfigUuid());
        }

        // 6. 子表 t_report_index_component
        List<NewReportIndexDO> indexComponentList = reportIndexMap.getOrDefault(id, Lists.newArrayList());
        for (NewReportIndexDO indexDO : indexComponentList) {
            if (StringUtils.isBlank(indexDO.getUuid()) || StringUtils.isBlank(indexDO.getConfigUuid())) {
                throw new BusinessException("当前报表指标表字段订正失败");
            }
            String cnName = StringUtils.isNoneBlank(indexDO.getOriginalName())?indexDO.getOriginalName():indexDO.getName();
            String enName = StringUtils.isNoneBlank(indexDO.getEnName())?indexDO.getEnName():indexDO.getFieldName();
            matchNewUuidValid(indexDO.getUuid(), indexDO.getFieldId(), enName, cnName, datasetColumnList, reportComputeList);
            uuidSetForWarn.add(indexDO.getUuid());
            uuidConfigSetForWarn.add(indexDO.getConfigUuid());
        }

        // 7. 子表 t_report_filter_component
        List<NewReportFilterDO> filterComponentList = reportFilterMap.getOrDefault(id, Lists.newArrayList());
        for (NewReportFilterDO filterDO : filterComponentList) {
            if (StringUtils.isBlank(filterDO.getUuid()) || StringUtils.isBlank(filterDO.getConfigUuid())) {
                throw new BusinessException("当前报表过滤表字段订正失败");
            }
            String cnName = StringUtils.isNoneBlank(filterDO.getOriginalName())?filterDO.getOriginalName():filterDO.getName();
            String enName = StringUtils.isNoneBlank(filterDO.getEnName())?filterDO.getEnName():filterDO.getFieldName();
            matchNewUuidValid(filterDO.getUuid(), filterDO.getFieldId(), enName, cnName,
                    datasetColumnList, reportComputeList);
        }

        // 8. 子表 t_report_condition_component
        List<NewReportConditionDO> conditionComponentList = reportConditionMap.getOrDefault(id, Lists.newArrayList());
        for (NewReportConditionDO conditionDO : conditionComponentList) {
            if (StringUtils.isBlank(conditionDO.getUuid()) || StringUtils.isBlank(conditionDO.getConfigUuid())) {
                throw new BusinessException("当前报表筛选器表字段订正失败");
            }
            String cnName = StringUtils.isNoneBlank(conditionDO.getOriginalName())?conditionDO.getOriginalName():conditionDO.getName();
            String enName = StringUtils.isNoneBlank(conditionDO.getEnName())?conditionDO.getEnName():conditionDO.getFieldName();
            matchNewUuidValid(conditionDO.getUuid(), conditionDO.getFieldId(), enName, cnName,
                    datasetColumnList, reportComputeList);
        }

        // 9. 子表 t_report_keyword_component
        List<NewReportKeywordDO> keywordComponentList = reportKeywordMap.getOrDefault(id, Lists.newArrayList());
        for (NewReportKeywordDO keywordDO : keywordComponentList) {
            if (StringUtils.isBlank(keywordDO.getUuid()) || StringUtils.isBlank(keywordDO.getConfigUuid())) {
                throw new BusinessException("当前报表关键字表字段订正失败");
            }
            String cnName = StringUtils.isNoneBlank(keywordDO.getOriginalName())?keywordDO.getOriginalName():keywordDO.getName();
            matchNewUuidValid(keywordDO.getUuid(), keywordDO.getFieldId(), keywordDO.getEnName(), cnName,
                    datasetColumnList, reportComputeList);
        }

        // 10. 子表 t_report_order_component
        List<NewReportOrderDO> orderComponentList = reportOrderMap.getOrDefault(id, Lists.newArrayList());
        for (NewReportOrderDO orderDO : orderComponentList) {
            if (StringUtils.isBlank(orderDO.getUuid()) || StringUtils.isBlank(orderDO.getConfigUuid())) {
                throw new BusinessException("当前报表排序表字段订正失败");
            }
            String cnName = StringUtils.isNoneBlank(orderDO.getOriginalName())?orderDO.getOriginalName():orderDO.getName();
            matchNewUuidValid(orderDO.getUuid(), orderDO.getFieldId(), orderDO.getEnName(), cnName,
                    datasetColumnList, reportComputeList);
        }

        // 11. 子表 t_report_style_config
        List<NewReportStyleDO> styleDOList = reportStyleMap.getOrDefault(id, Lists.newArrayList());
        if (CollUtil.isNotEmpty(styleDOList)) {
            NewReportStyleDO styleDO = styleDOList.get(0);
            if (StringUtils.isNoneBlank(styleDO.getTableConfiguration())) {
                TableConfiguration tableConfiguration = JSONUtil.toBean(styleDO.getTableConfiguration(),
                        TableConfiguration.class);
                if (tableConfiguration.getBasicFormat() != null
                        && CollUtil.isNotEmpty(tableConfiguration.getBasicFormat().getCustomColumnWidth())) {
                    List<CustomColumnWidth> customColumnWidth = tableConfiguration.getBasicFormat().getCustomColumnWidth();
                    for (CustomColumnWidth columnWidth : customColumnWidth) {
                        if ("度量值".equals(columnWidth.getColumnName()) && StringUtils.isBlank(columnWidth.getUuid())) {
                            continue;
                        }

                        if (StringUtils.isBlank(columnWidth.getUuid()) || StringUtils.isBlank(columnWidth.getConfigUuid())) {
                            extraMessage.append("自定义宽度数据订正存在异常" + columnWidth.getUuid() + "   configUuid:" + columnWidth.getConfigUuid());
                        }

                        if (StringUtils.isNoneBlank(columnWidth.getConfigUuid()) && !uuidConfigSetForWarn.contains(columnWidth.getConfigUuid())) {
                            throw new BusinessException("自定义宽度订正失败" + columnWidth.getUuid());
                        }
                        if (StringUtils.isNoneBlank(columnWidth.getUuid()) && !ReportFieldEnum.MEASURE.getCode().equals(columnWidth.getReportField())) {
                            matchNewUuidValid(columnWidth.getUuid(), null, null, columnWidth.getColumnName(),
                                    datasetColumnList, reportComputeList);
                        } else {
                            if (!measureUuidSet.contains(columnWidth.getUuid())) {
                                throw new BusinessException("自定义宽度订正失败" + columnWidth.getUuid());
                            }
                        }
                    }
                }
            }
        }

        // 12. 预警 t_report_warn_rule
        List<ReportWarnDo> reportWarnList = reportWarnMap.getOrDefault(id + "", Lists.newArrayList());
        for (ReportWarnDo warnDo : reportWarnList) {
            String code = warnDo.getCode();
            List<ReportWarnRuleDo> warnRuleDos = reportWarnRuleMap.getOrDefault(code, Lists.newArrayList());
            for (ReportWarnRuleDo ruleDo : warnRuleDos) {

                List<WarnConditionDTO> conditionDescList
                        = JSONUtil.toList(ruleDo.getConditionDesc(), WarnConditionDTO.class);

                WarnConditionDTO expectationDesc = JSONUtil
                        .toBean(ruleDo.getExpectationDesc(), WarnConditionDTO.class);
                for (WarnConditionDTO conditionDTO : conditionDescList) {
                    if (StringUtils.isBlank(conditionDTO.getUuid()) || StringUtils.isBlank(conditionDTO.getConfigUuid())) {
                        throw new BusinessException("预警任务订正失败");
                    }

                    if (!uuidSetForWarn.contains(conditionDTO.getUuid())) {
                        throw new BusinessException("预警任务订正失败");
                    }
                    if (!uuidConfigSetForWarn.contains(conditionDTO.getConfigUuid())) {
                        throw new BusinessException("预警任务订正失败");
                    }
                }

                if (StringUtils.isBlank(expectationDesc.getUuid()) || StringUtils.isBlank(expectationDesc.getConfigUuid())) {
                    throw new BusinessException("预警任务订正失败");
                }
                if (!uuidSetForWarn.contains(expectationDesc.getUuid())) {
                    throw new BusinessException("预警任务订正失败");
                }
                if (!uuidConfigSetForWarn.contains(expectationDesc.getConfigUuid())) {
                    throw new BusinessException("预警任务订正失败");
                }
            }
        }
    }



    private void matchNewUuidValid(String newUuid, Long fieldId, String enName, String cnName,
                                List<DatasetColumnConfigDTO> datasetColumnList,
                                List<NewReportComputeDO> reportComputeList) {
        if (StringUtils.isBlank(newUuid)) {
            throw new BusinessException("存在uuid订正失败, 原始信息 newUuid " + newUuid + ", fieldId " + fieldId
                    + ", enName " + enName + ", cnName " + cnName);
        }

        // 优先处理匹配报表计算字段
        if (CollUtil.isNotEmpty(reportComputeList)) {
            for (NewReportComputeDO computeDO : reportComputeList) {
                if (newUuid.equals(computeDO.getUuid())) {
                    if (fieldId != null && Objects.nonNull(computeDO.getFieldId())) {
                        if (fieldId.equals(computeDO.getFieldId())) {
                            return;
                        }
                    }

                    if (StringUtils.isNoneBlank(enName) && StringUtils.isNoneBlank(computeDO.getEnName())) {
                        if (enName.equals(computeDO.getEnName())) {
                            return;
                        }
                    }

                    if (StringUtils.isNoneBlank(cnName) && StringUtils.isNoneBlank(computeDO.getName())) {
                        if (cnName.equals(computeDO.getName())) {
                            return;
                        } else {
                            // 特殊情况 对于日期字段，比如数据集为 日期，但是报表会分成 日期(年), 日期(月)等，匹配的时候需要将(x)去掉进行匹配
                            if (cnName.endsWith("(年)")
                                    || cnName.endsWith("(月)")
                                    || cnName.endsWith("(日)")
                                    || cnName.endsWith("(周)")
                                    || cnName.endsWith("(季)")
                                    || cnName.endsWith("(时)")
                                    || cnName.endsWith("(分)")
                                    || cnName.endsWith("(秒)") ) {
                                String newCnName = cnName.substring(0, cnName.length() - 3);
                                if (newCnName.equals(computeDO.getName())) {
                                    return ;
                                }
                            }
                        }
                    }
                }
            }
        }

        // 再匹配数据集配置的相关信息[ 基础字段、计算字段、参数 ]
        for (DatasetColumnConfigDTO configDTO : datasetColumnList) {
            if (newUuid.equals(configDTO.getUuid())) {
                if (fieldId != null && Objects.nonNull(configDTO.getId())) {
                    if (fieldId.equals(configDTO.getId())) {
                        return;
                    }
                }
                if (StringUtils.isNoneBlank(enName) && StringUtils.isNoneBlank(configDTO.getEnName())) {
                    if (enName.equals(configDTO.getEnName())) {
                        return;
                    }

                    if (enName.contains("(" + configDTO.getEnName() + ")")) {
                        return;
                    }

                    if (enName.contains("(" + configDTO.getEnName() + ",")) {
                        return;
                    }

                    if (configDTO.getEnName().contains("(" + enName + ")")) {
                        return;
                    }

                    if (configDTO.getEnName().contains("(" + enName +",")) {
                        return;
                    }
                }

                if (StringUtils.isNoneBlank(enName) && StringUtils.isNoneBlank(configDTO.getOriginEnName())) {
                    if (enName.equals(configDTO.getOriginEnName())) {
                        return;
                    }

                    if (enName.contains("(" + configDTO.getOriginEnName() + ")")) {
                        return;
                    }

                    if (enName.contains("(" + configDTO.getOriginEnName() + ",")) {
                        return;
                    }

                    if (configDTO.getOriginEnName().contains("(" + enName + ")")) {
                        return;
                    }

                    if (configDTO.getOriginEnName().contains("(" + enName + ",")) {
                        return;
                    }
                }


                if (StringUtils.isNoneBlank(cnName) && StringUtils.isNoneBlank(configDTO.getName())) {
                    if (cnName.equals(configDTO.getName())) {
                        return;
                    } else {
                        // 特殊情况 对于日期字段，比如数据集为 日期，但是报表会分成 日期(年), 日期(月)等，匹配的时候需要将(x)去掉进行匹配
                        if (cnName.endsWith("(年)")
                                || cnName.endsWith("(月)")
                                || cnName.endsWith("(日)")
                                || cnName.endsWith("(周)")
                                || cnName.endsWith("(季)")
                                || cnName.endsWith("(时)")
                                || cnName.endsWith("(分)")
                                || cnName.endsWith("(秒)") ) {
                            String newCnName = cnName.substring(0, cnName.length() - 3);
                            if (newCnName.equals(configDTO.getName())) {
                                return ;
                            }
                        }
                    }
                }
            }
        }

        throw new BusinessException("匹配失败, 原始信息 newUuid " + newUuid + ", fieldId " + fieldId
                + ", enName " + enName + ", cnName " + cnName);
    }
}
