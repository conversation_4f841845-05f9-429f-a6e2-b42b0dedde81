package com.bestpay.bigdata.bi.report.service.impl.report;

import static com.bestpay.bigdata.bi.common.constant.BIConstant.EMAIL;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.api.RedisService;
import com.bestpay.bigdata.bi.common.bean.AiPlusUserSearchRequest;
import com.bestpay.bigdata.bi.common.dto.dashboard.ReportCardQueryDTO;
import com.bestpay.bigdata.bi.common.dto.dataset.DatasetInfo;
import com.bestpay.bigdata.bi.common.dto.report.component.CommonComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.ContrastComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.DimensionComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.IndexComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.warn.ConditionDesc;
import com.bestpay.bigdata.bi.common.dto.warn.ExpectationDesc;
import com.bestpay.bigdata.bi.common.dto.warn.ReportRule;
import com.bestpay.bigdata.bi.common.dto.warn.ReportWarnQueryDTO;
import com.bestpay.bigdata.bi.common.dto.warn.ReportWarnRuleDTO;
import com.bestpay.bigdata.bi.common.dto.warn.WarnConditionDTO;
import com.bestpay.bigdata.bi.common.entity.Org;
import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.common.enums.FieldType;
import com.bestpay.bigdata.bi.common.enums.StatusCodeEnum;
import com.bestpay.bigdata.bi.common.error.DashboardErrorCode;
import com.bestpay.bigdata.bi.common.error.ReportErrorCode;
import com.bestpay.bigdata.bi.common.exception.BiException;
import com.bestpay.bigdata.bi.common.response.PageQueryVO;
import com.bestpay.bigdata.bi.common.response.ReportDetailVO;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.common.util.StringUtil;
import com.bestpay.bigdata.bi.common.util.UserContextUtil;
import com.bestpay.bigdata.bi.database.api.common.DatePickerDAOService;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardCardService;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardDaoService;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardReportCardService;
import com.bestpay.bigdata.bi.database.api.report.ReportWarnAddOrUpdateService;
import com.bestpay.bigdata.bi.database.api.report.ReportWarnDAOService;
import com.bestpay.bigdata.bi.database.api.report.ReportWarnRuleDAOService;
import com.bestpay.bigdata.bi.database.bean.dashboard.Dashboard;
import com.bestpay.bigdata.bi.database.bean.report.Report;
import com.bestpay.bigdata.bi.database.dao.common.DatePickerConfigDO;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardCardDO;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardReportCardDO;
import com.bestpay.bigdata.bi.database.dao.warn.ReportWarnDo;
import com.bestpay.bigdata.bi.database.dao.warn.ReportWarnPageDo;
import com.bestpay.bigdata.bi.database.dao.warn.ReportWarnRuleDo;
import com.bestpay.bigdata.bi.report.bean.warn.ReportWarnDTO;
import com.bestpay.bigdata.bi.report.bean.warn.ReportWarnListVO;
import com.bestpay.bigdata.bi.report.bean.warn.ReportWarnVO;
import com.bestpay.bigdata.bi.report.bean.warn.WarnConfig;
import com.bestpay.bigdata.bi.report.constant.CommonConstant;
import com.bestpay.bigdata.bi.report.enums.common.WarnSourceTypeEnum;
import com.bestpay.bigdata.bi.report.enums.report.ReportFieldEnum;
import com.bestpay.bigdata.bi.report.request.report.DimensionValueRequest;
import com.bestpay.bigdata.bi.report.request.warn.ReportWarnEnumValueRequest;
import com.bestpay.bigdata.bi.report.request.warn.ReportWarnListRequest;
import com.bestpay.bigdata.bi.report.request.warn.ReportWarnRequest;
import com.bestpay.bigdata.bi.report.request.warn.ReportWarnStatusRequest;
import com.bestpay.bigdata.bi.report.response.common.QueryIndexAndReportResponse;
import com.bestpay.bigdata.bi.report.response.report.ReportWarnConfigVO;
import com.bestpay.bigdata.bi.report.schedule.ScheduleConfig;
import com.bestpay.bigdata.bi.report.schedule.warn.ReportWarnScheduler;
import com.bestpay.bigdata.bi.report.service.report.ReportProcessService;
import com.bestpay.bigdata.bi.report.service.report.ReportUpdateService;
import com.bestpay.bigdata.bi.report.service.report.ReportWarnService;
import com.bestpay.bigdata.bi.report.util.AuthorityCheckUtil;
import com.bestpay.bigdata.bi.report.util.ReportUtil;
import com.bestpay.bigdata.bi.thirdparty.api.AiPlusUserService;
import com.bestpay.bigdata.bi.thirdparty.api.AiplusService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;

import java.util.*;
import com.google.common.collect.Maps;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ReportWarnServiceImpl implements ReportWarnService {
    @Resource
    private ReportWarnDAOService warnDAOService;
    @Resource
    private ReportWarnRuleDAOService warnRuleDAOService;
    @Resource
    private ReportUpdateService reportService;
    @Resource
    private ReportWarnAddOrUpdateService warnAddOrUpdateService;
    @Resource
    private AiplusService aiplusService;
    @Resource
    private RedisService redisService;
    @Resource
    private ReportWarnScheduler reportWarnScheduler;
    @Resource
    private DatePickerDAOService pickerDAOService;
    @Resource
    private ReportProcessService reportProcessService;
    @Resource
    private AiPlusUserService aiPlusUserService;
    public static final String REPORT_WARN_CODE_KEY = "T";
    public static final String REPORT_WARN_CODE_FORMAT = "%05d";
    @Resource
    private AuthorityCheckUtil authorityCheckUtil;
    @Resource
    private ReportUpdateService updateService;
    @Resource
    private DashboardReportCardService dashboardReportCardService;
    @Resource
    private DashboardDaoService dashboardDaoService;
    @Resource
    private DashboardCardService cardService;
    @Override
    public Response<List<ReportWarnConfigVO>> getConfigList(String reportId, String warnSourceType) {
        // 获取报表信息
        ReportDetailVO reportInfo = getReportDetail(reportId, warnSourceType);

        /**
         * 获取报表配置的指标，维度，对比信息
         */
        List<CommonComponentPropertyDTO> columns = Lists.newArrayList();
        // 对比指标
        for (ContrastComponentPropertyDTO columnProperty : reportInfo.getContrastColumnList()) {
            if (ReportFieldEnum.CONTRAST_FIELD.getCode()
                .equalsIgnoreCase(columnProperty.getReportField())) {
                columns.add(columnProperty);
            }
        }

        // 维度
        CollUtil.addAll(columns, reportInfo.getShowColumnList());

        // 指标
        CollUtil.addAll(columns, reportInfo.getIndexColumnList());

        List<ReportWarnConfigVO> configVOS = columns.stream().map(p->{
            ReportWarnConfigVO configVO = new ReportWarnConfigVO();
            BeanUtils.copyProperties(p, configVO);
            return configVO;
        }).collect(Collectors.toList());

        return Response.ok(configVOS);
    }

    private ReportDetailVO getReportDetail(String reportId, String warnSourceType) {
        // 查詢报表信息
        ReportDetailVO reportInfo = null;
        // 报表市场报表
        if(WarnSourceTypeEnum.REPORT.getCode().equals(warnSourceType)){
            reportInfo = updateService.queryReportTemplate(Long.valueOf(reportId)).getData();

            if (reportInfo == null) {
                throw new BiException(ReportErrorCode.REPORT_NOT_EXISTS,String.format("报表不存在, reportId=%s", reportId));

            }

        }else{
            // 仪表板报表
            List<DashboardCardDO> cards = cardService.findByCode(null, reportId);
            if (CollUtil.isEmpty(cards)) {
                throw new BiException(ReportErrorCode.REPORT_CARD_NOT_EXISTS,String.format("报表卡片不存在, reportId=%s", reportId));
            }

            Response<ReportDetailVO> data
                = reportProcessService.handlerDashboardReportCardInfo(cards.get(0).getCardId());

            reportInfo = data.getData();
            if (reportInfo == null) {
                throw new BiException(ReportErrorCode.REPORT_NOT_EXISTS,String.format("报表不存在, reportId=%s, warnSourceType=%s",
                        reportId, warnSourceType));
            }
        }
        return reportInfo;
    }

    @Override
    public Response<String> addOrUpdate(ReportWarnRequest request){
        UserInfo user = UserContextUtil.getUserInfo();
        String code = StringUtil.isEmpty(request.getCode()) ? getWarnCode() : request.getCode();

        ReportWarnQueryDTO reportWarnQueryDTO = ReportWarnQueryDTO.builder()
                .name(request.getName())
                .excludeCode(request.getCode())
                .build();

        List<ReportWarnDo> warnDos = warnDAOService.query(reportWarnQueryDTO);
        if(CollUtil.isNotEmpty(warnDos)){
            throw new BiException(ReportErrorCode.REPORT_WARN_CONFIG_NAME_REPEATED,"名称重复");
        }

        ReportWarnQueryDTO exist = ReportWarnQueryDTO.builder()
                .code(code)
                .build();

        List<ReportWarnDo> existWarn = warnDAOService.query(exist);

        // 校验权限
        if(CollUtil.isNotEmpty(existWarn)) {
            authorityCheckUtil.checkOwner(EMAIL, existWarn.get(0).getCreatedBy());
        }

        // 1、warn 主表
        ReportWarnDo insertWarnDo = new ReportWarnDo();
        BeanUtils.copyProperties(request, insertWarnDo);
        insertWarnDo.setCode(code);
        insertWarnDo.setScheduleConfig(JSONUtil.toJsonStr(request.getScheduleConfig()));
        insertWarnDo.setWarnConfig(JSONUtil.toJsonStr(request.getWarnConfig()));
        insertWarnDo.setUpdatedBy(user.getEmail());
        insertWarnDo.setOrgCode(user.getOrg().getCode());

        // AILAB-47538 修复数据集权限需要获取用户信息bug, 这里用创建人, 因为创建人就是责任人, 这里存在历史bug
        Date currentDate = new Date();
        if (CollUtil.isNotEmpty(existWarn)) {
            insertWarnDo.setCreatedBy(existWarn.get(0).getCreatedBy());
            insertWarnDo.setCreatedAt(existWarn.get(0).getCreatedAt());
        }else{
            insertWarnDo.setCreatedBy(user.getEmail());
            insertWarnDo.setCreatedAt(currentDate);
        }
        insertWarnDo.setUpdatedAt(currentDate);

        // 不更新
        insertWarnDo.setOwner(CollUtil.isNotEmpty(existWarn)? existWarn.get(0).getOwner() : user.getAccount());
        insertWarnDo.setOwnerCn(CollUtil.isNotEmpty(existWarn)? existWarn.get(0).getOwnerCn():user.getNickName());
        insertWarnDo.setStatusCode(CollUtil.isNotEmpty(existWarn)? existWarn.get(0).getStatusCode() : StatusCodeEnum.OFFLINE.getCode());

        insertWarnDo.setCron(reportWarnScheduler.parseScheduleConfig(request.getScheduleConfig()));

        // 2、add or update
        warnAddOrUpdateService.addOrUpdate(code, insertWarnDo, request.getReportRuleList());

        return Response.ok();
    }

    @Override
    public Response<ReportWarnVO> detail(Long id){
        ReportWarnQueryDTO queryDTO = ReportWarnQueryDTO.builder()
                .id(id)
                .build();
        List<ReportWarnDo> list = warnDAOService.query(queryDTO);

        if(CollUtil.isEmpty(list)){
            throw new BiException(ReportErrorCode.REPORT_WARN_CONFIG_NOT_EXIST,"预警配置不存在");
        }

        ReportWarnDo warnDo = list.get(0);

        // 1、warn 主表信息
        ReportWarnVO warnVO = new ReportWarnVO();
        BeanUtils.copyProperties(warnDo, warnVO);
        warnVO.setWarnConfig(JSONUtil.toBean(warnDo.getWarnConfig(), WarnConfig.class));
        warnVO.setScheduleConfig(JSONUtil.toBean(warnDo.getScheduleConfig(), ScheduleConfig.class));

        // 2、规则 子表
        ReportWarnRuleDTO query = ReportWarnRuleDTO.builder()
                .warnCodeList(Lists.newArrayList(warnDo.getCode()))
                .build();

        List<ReportWarnRuleDo> ruleDoList = warnRuleDAOService.query(query);

        List<ReportRule> reportRuleList = ruleDoList.stream().map(p->{
            ReportRule rule = new ReportRule();
            rule.setExpectationDesc(JSONUtil
                    .toBean(p.getExpectationDesc(), ExpectationDesc.class));

            // 获取预警条件规则
            List<WarnConditionDTO> conditionDTOS
                = JSONUtil.toList(p.getConditionDesc(), WarnConditionDTO.class);

            // 获取预警筛选规则
            rule.setConditionDescList(getConditionDescs(
                conditionDTOS));

            return rule;
        }).collect(Collectors.toList());

        warnVO.setReportRuleList(reportRuleList);
        return Response.ok(warnVO);
    }

    @Override
    public Response<Boolean> retry(Long id) {
        reportWarnScheduler.addReportQuartzJob(id);
        return Response.ok(true);
    }

    private List<ConditionDesc> getConditionDescs(List<WarnConditionDTO> conditionDTOS) {
        List<ConditionDesc> conditionDescList = new ArrayList<>();
        // 获取日期筛选配置
        for (WarnConditionDTO warnConditionDTO : conditionDTOS) {
            ConditionDesc conditionDesc = new ConditionDesc();
            BeanUtils.copyProperties(warnConditionDTO, conditionDesc);

            if(FieldType.DATETIME.name().equalsIgnoreCase(warnConditionDTO.getShowTypeName())
                && warnConditionDTO.getDatePickerId()!=null) {
                DatePickerConfigDO pickerConfigDO
                    = pickerDAOService.select(warnConditionDTO.getDatePickerId());
                BeanUtils.copyProperties(pickerConfigDO, conditionDesc);
                conditionDesc.setDefaultValues(JSONUtil
                    .toList(pickerConfigDO.getDefaultValue(), String.class));
            }

            conditionDescList.add(conditionDesc);
        }
        return conditionDescList;
    }

    @Override
    public Response<PageQueryVO<ReportWarnListVO>> list(ReportWarnListRequest request) {
        Integer pageNum = request.getPageNum();
        Integer pageSize = request.getPageSize();
        request.setPageSize(Objects.nonNull(pageSize)?pageSize:10);
        request.setPageNum(Objects.nonNull(pageNum)?pageNum:1);
        ReportWarnQueryDTO pageQueryDTO = ReportWarnQueryDTO.builder()
                .keyWord(ReportUtil.escapeLike(request.getKeyWord()))
                .statusCode(request.getStatusCode())
                .reportId(request.getReportId())
                .ownerName(request.getOwnerName())
                .warnSourceType(request.getWarnSourceType())
                .build();

        Page<ReportWarnPageDo> warnPageDos = PageHelper.startPage(pageNum, pageSize);
        // 分页查询
        List<ReportWarnPageDo> warnDos = warnDAOService.queryWarnList(pageQueryDTO);
        List<ReportWarnListVO> warnList = Lists.newArrayList();
        Map<String, Org> orgMap = getOrgMap();

        for (ReportWarnPageDo warnDo : warnDos) {
            ReportWarnListVO warnVO = new ReportWarnListVO();
            BeanUtils.copyProperties(warnDo, warnVO);
            Org org = orgMap.get(warnDo.getOrgCode());
            warnVO.setOrgName(Objects.nonNull(org)?org.getName(): StrUtil.EMPTY);
            UserInfo userInfo = getUserInfo(warnDo.getCreatedBy());
            warnVO.setOwnerName(Objects.nonNull(userInfo)? userInfo.getAccount() : CommonConstant.UNKNOWN);
            warnVO.setWarnSourceType(warnDo.getWarnSourceType());
            warnList.add(warnVO);
        }

        PageQueryVO<ReportWarnListVO> page = new PageQueryVO<>();
        page.setPageNum(pageNum);
        page.setPageSize(pageSize);
        page.setTotalSize(warnPageDos.getTotal());
        page.setData(warnList);
        return Response.ok(page);
    }

    private UserInfo getUserInfo(String email){
        AiPlusUserSearchRequest request = new AiPlusUserSearchRequest();
        request.setOwnerNames(Lists.newArrayList(email));
        List<UserInfo> users = aiPlusUserService.getUserList(request);
        if(CollUtil.isNotEmpty(users)){
            return users.get(0);
        }
        return null;
    }

    private Map<String,Org> getOrgMap() {
        Map<String,Org> orgMap = Maps.newHashMap();
        List<Org> orgList = aiplusService.getOrgList();
        if (CollUtil.isNotEmpty(orgList)) {
            orgMap =  orgList.stream().collect(Collectors.toMap(Org::getCode, Function.identity()));
        }
        return orgMap;
    }

    @Override
    public Response<String> status(ReportWarnStatusRequest request) {
        ReportWarnQueryDTO exist = ReportWarnQueryDTO.builder()
            .id(request.getId())
            .build();

        List<ReportWarnDo> warnDos = warnDAOService.query(exist);

        // 校验权限
        authorityCheckUtil.checkOwner(EMAIL, warnDos.get(0).getCreatedBy());

        ReportWarnDo warnDo = new ReportWarnDo();
        warnDo.setId(request.getId());
        warnDo.setUpdatedBy(UserContextUtil.getUserInfo().getEmail());

        if(request.getOperator().equalsIgnoreCase("ON")){
            warnDo.setStatusCode(StatusCodeEnum.ONLINE.getCode());
        }else if(request.getOperator().equalsIgnoreCase("OFF")){
            warnDo.setStatusCode(StatusCodeEnum.OFFLINE.getCode());
        } else if (request.getOperator().equalsIgnoreCase("DELETE")) {
            warnDo.setStatusCode(StatusCodeEnum.DELETE.getCode());
        }

        warnDAOService.update(warnDo);
        return Response.ok();
    }

    @Override
    public Response<QueryIndexAndReportResponse> querySelectValue(ReportWarnEnumValueRequest request) {
        // 获取报表信息
        ReportDetailVO report = getReportDetail(request.getReportId(), request.getWarnSourceType());

        DimensionValueRequest selectRequest = new DimensionValueRequest();
        // 数据集信息
        DatasetInfo datasetInfo = report.getDatasetInfoList().get(0);

        // 报表配置项
        List<CommonComponentPropertyDTO> columnPropertyList = getColumnProperties(
            report);

        // 匹配前端查询项枚举值， by uuid
        for (CommonComponentPropertyDTO columnProperty : columnPropertyList) {
            if(request.getColumnUuid().equals(columnProperty.getUuid())) {
                BeanUtils.copyProperties(datasetInfo, selectRequest);
                selectRequest.setDatasetId(datasetInfo.getDatasetId());
                selectRequest.setId(columnProperty.getId());
                selectRequest.setColumnName(columnProperty.getEnName());
                selectRequest.setFun(columnProperty.getFun());
                selectRequest.setComputeColumn(report.getComputeColumnList());
            }
        }

        // 查询枚举值
        return reportProcessService.querySelectValue(selectRequest);
    }

    private static List<CommonComponentPropertyDTO> getColumnProperties(ReportDetailVO report) {
        List<CommonComponentPropertyDTO> columnPropertyList = Lists.newArrayList();
        // 维度项
        for (DimensionComponentPropertyDTO response : report.getShowColumnList()) {
            CommonComponentPropertyDTO columnProperty = new CommonComponentPropertyDTO();
            BeanUtils.copyProperties(response, columnProperty);
            columnPropertyList.add(columnProperty);
        }

        // 指标项
        for (IndexComponentPropertyDTO response : report.getIndexColumnList()) {
            CommonComponentPropertyDTO columnProperty = new CommonComponentPropertyDTO();
            BeanUtils.copyProperties(response, columnProperty);
            columnPropertyList.add(columnProperty);
        }

        // 对比项
        for (ContrastComponentPropertyDTO response : report.getContrastColumnList()) {
            CommonComponentPropertyDTO columnProperty = new CommonComponentPropertyDTO();
            BeanUtils.copyProperties(response, columnProperty);
            columnPropertyList.add(columnProperty);
        }
        return columnPropertyList;
    }


    /**
     * 判断 告警报表是否下线，删除，告警中的配置 与报表中的维度、指标是否有过更改
     * @return false 没改 true 改了
     */
    @Override
    public Response<Boolean> judgeWarnModify(ReportWarnDTO reportWarnDo) {

        Boolean isOnline = isOnline(reportWarnDo);
        if (!isOnline) {
            Boolean result = Boolean.FALSE;
            List<String> newReportUuidList = getConfigList(reportWarnDo.getReportId(), reportWarnDo.getWarnSourceType()).getData()
                    .stream().map(ReportWarnConfigVO::getUuid).collect(Collectors.toList());
            log.info("newReportUuidList : {}", newReportUuidList);
            List<ReportWarnRuleDo> warnRuleDoList = warnRuleDAOService.query(
                    ReportWarnRuleDTO.builder().warnCodeList(Lists.newArrayList(reportWarnDo.getCode())).build());
            log.info("warnRuleDoList {}", warnRuleDoList);
            for (ReportWarnRuleDo reportWarnRuleDo : warnRuleDoList) {
                List<ConditionDesc> conditionDescList = JSONUtil.toList(reportWarnRuleDo.getConditionDesc(), ConditionDesc.class);
                for (ConditionDesc conditionDesc : conditionDescList) {
                    if (!newReportUuidList.contains(conditionDesc.getUuid())) {
                        result = Boolean.TRUE;
                        break;
                    }
                }
                if (result) {
                    break;
                }
                ExpectationDesc expectationDesc = JSONUtil.toBean(reportWarnRuleDo.getExpectationDesc(), ExpectationDesc.class);
                if (!newReportUuidList.contains(expectationDesc.getUuid())) {
                    result = Boolean.TRUE;
                    break;
                }
            }
            return Response.ok(result);
        }
        return Response.ok(isOnline);
    }

    public Boolean isOnline(ReportWarnDTO reportWarnDo) {
        if(WarnSourceTypeEnum.REPORT.getCode().equals(reportWarnDo.getWarnSourceType())){
            Report report = reportService.queryById(Long.valueOf(reportWarnDo.getReportId()));

            if (report == null) {
                throw new BiException(ReportErrorCode.REPORT_NOT_EXISTS,String.format("报表不存在, reportId=%s",reportWarnDo.getReportId()));
            }

            if (StatusCodeEnum.ONLINE.getCode() != report.getStatusCode()) {
                log.warn("报表已经下线, reportId={}, warnSourceType={}",
                    reportWarnDo.getReportId(), reportWarnDo.getWarnSourceType());
                return true;
            }

        }else{
            List<DashboardCardDO> cards = cardService.findByCode(null, reportWarnDo.getReportId());
            if (CollUtil.isEmpty(cards)) {
                throw new BiException(ReportErrorCode.REPORT_NOT_EXISTS,String.format("报表不存在, code=%s", reportWarnDo.getReportId()));
            }


            ReportCardQueryDTO cardQuery = new ReportCardQueryDTO();
            cardQuery.setIdList(Lists.newArrayList(cards.get(0).getCardId()));
            List<DashboardReportCardDO> reportCardDOS = dashboardReportCardService.find(cardQuery);

            if (CollUtil.isEmpty(reportCardDOS)) {
                throw new BiException(ReportErrorCode.REPORT_NOT_EXISTS,String.format("报表不存在, reportId=%s, warnSourceType=%s",
                        reportWarnDo.getReportId(),reportWarnDo.getWarnSourceType()));
            }

            Dashboard dashboard
                = dashboardDaoService.getById(reportCardDOS.get(0).getDashboardId());

            if (dashboard==null) {
                throw new BiException(DashboardErrorCode.DASHBOARD_NOT_EXISTS,String.format("仪表板不存在, dashboardId=%s, warnSourceType=%s",
                        dashboard.getId()));
            }

            if (StatusCodeEnum.ONLINE.getCode() != dashboard.getStatusCode()) {
                log.warn("报表已经下线, reportId={}, warnSourceType={}",
                    reportWarnDo.getReportId(), reportWarnDo.getWarnSourceType());
                return true;
            }
        }
        return false;
    }

    public String getWarnCode() {
        String datasetCode;
        long taskNum = redisService.getIncrementNum(REPORT_WARN_CODE_KEY);
        if (taskNum != 0) {
            log.info("get dataset num from cache");
            datasetCode = String.format(REPORT_WARN_CODE_FORMAT, taskNum);
        } else {
            log.info("get dataset num from mysql");
            ReportWarnDo warnDo = warnDAOService.findLatestDatasetDo();
            if (warnDo != null) {
                taskNum = Long.parseLong(warnDo.getCode().substring(2)) + 1;
                datasetCode = String.format(REPORT_WARN_CODE_FORMAT, taskNum);
                redisService.setIncr(REPORT_WARN_CODE_KEY, taskNum);
            } else {
                datasetCode = String.format(REPORT_WARN_CODE_FORMAT, 1);
                redisService.setIncr(REPORT_WARN_CODE_KEY, 1L);
            }
        }
        return REPORT_WARN_CODE_KEY + datasetCode;
    }
}
