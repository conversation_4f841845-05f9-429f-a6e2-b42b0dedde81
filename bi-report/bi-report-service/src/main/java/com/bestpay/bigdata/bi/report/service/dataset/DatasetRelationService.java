package com.bestpay.bigdata.bi.report.service.dataset;

import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.report.bean.dataset.DatasetRelationVO;
import com.bestpay.bigdata.bi.report.request.dataset.DatasetRelationRequest;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @create 2023-07-05-17:35
 */
public interface DatasetRelationService {


    /**
     * add DatasetRelation
     * @param datasetRelationRequest request
     * @param httpServletRequest
     * @return vo
     */
    Response<DatasetRelationVO> addDatasetRelation(DatasetRelationRequest datasetRelationRequest, HttpServletRequest httpServletRequest);


    /**
     * show DatasetRelation
     * @param datasetRelationRequest request
     * @param httpServletRequest
     * @return vo
     */
    Response<DatasetRelationVO> showDatasetRelation(DatasetRelationRequest datasetRelationRequest, HttpServletRequest httpServletRequest);
}
