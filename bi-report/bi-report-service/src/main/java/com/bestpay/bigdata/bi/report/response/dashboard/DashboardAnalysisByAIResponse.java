package com.bestpay.bigdata.bi.report.response.dashboard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @create 2025-06-06-13:56
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel("仪表盘AI解读报告")
public class DashboardAnalysisByAIResponse {

    @ApiModelProperty(value = "解读报告key")
    private String key;

    @ApiModelProperty(value = "解读报告内容")
    private String aiOutput;

    @ApiModelProperty(value = "解读状态(0: 解读中 ,1: 解读成功 ,-1: 解读失败)")
    private Integer status;


}
