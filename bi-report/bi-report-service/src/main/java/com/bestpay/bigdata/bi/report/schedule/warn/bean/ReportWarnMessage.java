package com.bestpay.bigdata.bi.report.schedule.warn.bean;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * ClassName: ReportWarnMessage
 * Package: com.bestpay.bigdata.bi.report.schedule
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/8/8 15:12
 * @Version 1.0
 */

@Data
public class ReportWarnMessage implements Serializable {

    private String exceptionMessage;

    private Map<String, String> uuidAliasMap;

    private boolean isAnyNeedWarn;

    private List<String> senderMessage;
}
