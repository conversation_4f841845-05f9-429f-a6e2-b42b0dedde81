package com.bestpay.bigdata.bi.report.service.dashboard;

import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.database.bean.dashboard.Dashboard;
import com.bestpay.bigdata.bi.database.bean.dashboard.DashboardQuery;
import com.bestpay.bigdata.bi.report.request.dashboard.DashBoardCopyRequest;
import com.bestpay.bigdata.bi.report.request.dashboard.DashboardAnalysisByAIParamRequest;
import com.bestpay.bigdata.bi.report.request.dashboard.DashboardAnalysisByAIRequest;
import com.bestpay.bigdata.bi.report.request.dashboard.DashboardPublishRequest;
import com.bestpay.bigdata.bi.report.request.dashboard.MoveDashboardRequest;
import com.bestpay.bigdata.bi.report.response.dashboard.DashboardAnalysisByAIResponse;
import com.bestpay.bigdata.bi.report.response.dashboard.DashboardDirectoryResponse;
import com.bestpay.bigdata.bi.report.response.dashboard.DashboardResponse;
import com.bestpay.bigdata.bi.report.response.dashboard.DashboardVo;
import java.util.List;
import javax.servlet.http.HttpServletRequest;

/**
 * @author: laiyao
 * @date: 2022/07/26
 */
public interface DashboardService {

    Response<Long> insert(Dashboard dashboard);


    Response<List<DashboardVo>> query(DashboardQuery dashboardQuery, HttpServletRequest request);

    Response<List<DashboardVo>> queryList(DashboardQuery dashboardQuery);

    Response<Boolean> deleteById(Long id,String email);

    Response<Dashboard> queryById(Long id, String source);

    Response<Boolean> updateById(Dashboard dashboard);

    Response<Boolean> moveDashboardLocation(MoveDashboardRequest moveDashboardRequest,
        HttpServletRequest httpServletRequest);

    Response<List<DashboardDirectoryResponse>> queryDashboardDirectoryList(Dashboard dashboard);

    Response<List<DashboardResponse>> queryDashboardListByDirId(Dashboard dashboard);


    /**
     * 仪表板发布
     * @param request 仪表板id 和是否发布到手机端
     * @return 成功标识
     */
    Integer publish(DashboardPublishRequest request);

    /**
     * 仪表板 下线
     * @param id 仪表板ID
     * @return 仪表板ID
     */
    Integer offLine(Long id);

    /**
     * 复制仪表板/数据大屏
     * @param request 入参
     * @return 是否成功标识
     */
    Long copy(DashBoardCopyRequest request);
}
