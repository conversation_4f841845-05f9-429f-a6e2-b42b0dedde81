package com.bestpay.bigdata.bi.report.correction;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.dto.dataset.DatasetColumnConfigDTO;
import com.bestpay.bigdata.bi.common.dto.report.ReportUuidGenerateUtil;
import com.bestpay.bigdata.bi.common.dto.warn.ReportWarnQueryDTO;
import com.bestpay.bigdata.bi.common.dto.warn.ReportWarnRuleDTO;
import com.bestpay.bigdata.bi.common.enums.NewCardTypeEnum;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.database.api.dashboard.*;
import com.bestpay.bigdata.bi.database.api.report.ReportWarnDAOService;
import com.bestpay.bigdata.bi.database.api.report.ReportWarnRuleDAOService;
import com.bestpay.bigdata.bi.database.api.subscribe.ObjectSubscribeService;
import com.bestpay.bigdata.bi.database.bean.dashboard.Dashboard;
import com.bestpay.bigdata.bi.database.bean.dashboard.DashboardQuery;
import com.bestpay.bigdata.bi.database.bean.subscribe.ObjectSubScribeDO;
import com.bestpay.bigdata.bi.database.bean.subscribe.ObjectSubScribeDTO;
import com.bestpay.bigdata.bi.database.dao.dashboard.*;
import com.bestpay.bigdata.bi.database.dao.warn.ReportWarnDo;
import com.bestpay.bigdata.bi.database.dao.warn.ReportWarnRuleDo;
import com.bestpay.bigdata.bi.report.request.dataset.DatasetColumnConfigRequest;
import com.bestpay.bigdata.bi.report.service.dataset.DatasetService;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class DashboardUuid {

    @Resource
    private DashboardDaoService dashboardDaoService;

    @Resource
    private DashboardCardService dashboardCardService;

    @Resource
    private DatasetService datasetService;

    @Resource
    private DashboardReportCardService dashboardReportCardService;

    @Resource
    private DashboardFilterCardService dashboardFilterCardService;

    @Resource
    private DashboardIndexTextCardService dashboardIndexTextCardService;

    @Resource
    private ObjectSubscribeService objectSubscribeService;

    @Resource
    private ReportWarnDAOService warnDAOService;

    @Resource
    private ReportWarnRuleDAOService warnRuleDAOService;

    @Resource
    private DashboardCorrectionLogService dashboardCorrectionLogService;

    // 批量处理大小
    private static final int BATCH_SIZE = 10;

    @Transactional
    public void dashboardUuid() {
        log.info("开始执行仪表板UUID数据订正");

        // 生成批次ID
        String batchId = "dashboard_uuid_correction_" + System.currentTimeMillis();
        log.info("批次ID: {}", batchId);

        try {
            // 获取所有仪表板
            List<Dashboard> allDashboardList = dashboardDaoService.queryByCondition(new DashboardQuery());
            List<Long> dashboardIdList = allDashboardList.stream()
                    .map(Dashboard::getId)
                    .collect(Collectors.toList());
            log.info("获取到仪表板数量: {}", allDashboardList.size());

            // 获取所有仪表板报表卡片（从备份表查询）
            List<DashboardReportCardDO> allDashboradReportCardList = dashboardReportCardService.findBak();
            List<DashboardReportCardDO> activeDashboardReportCardList = allDashboradReportCardList.stream()
                    .filter(e -> dashboardIdList.contains(e.getDashboardId()))
                    .collect(Collectors.toList());
            log.info("获取到仪表板报表卡片数量: {}", activeDashboardReportCardList.size());

            // 获取所有仪表板筛选器卡片（从备份表查询）
            List<DashboardFilterCardDO> dashboardFilterCardList = dashboardFilterCardService.findBak();
            List<DashboardFilterCardDO> activeDashboardFilterCardList = dashboardFilterCardList.stream()
                    .filter(e -> dashboardIdList.contains(e.getDashboardId()))
                    .collect(Collectors.toList());
            log.info("获取到仪表板筛选器卡片数量: {}", activeDashboardFilterCardList.size());

            // 获取所有仪表板指标文本卡片（从备份表查询）
            List<DashboardIndexTextCardDO> dashboardIndexTextCardList = dashboardIndexTextCardService.findBak();
            List<DashboardIndexTextCardDO> activeDashboardIndexTextCardList = dashboardIndexTextCardList.stream()
                    .filter(e -> dashboardIdList.contains(e.getDashboardId()))
                    .collect(Collectors.toList());
            log.info("获取到仪表板指标文本卡片数量: {}", activeDashboardIndexTextCardList.size());

            // 获取所有仪表板订阅（从备份表查询）
            ObjectSubScribeDTO objectSubScribeReq = new ObjectSubScribeDTO();
            objectSubScribeReq.setObjectType("dashboard");
            List<ObjectSubScribeDO> objectSubScribeList = objectSubscribeService.findBak(objectSubScribeReq);
            List<ObjectSubScribeDO> dashboardObjectSubscribeList = objectSubScribeList.stream()
                    .filter(e -> dashboardIdList.contains(e.getObjectId()))
                    .collect(Collectors.toList());
            log.info("获取到仪表板订阅数量: {}", dashboardObjectSubscribeList.size());

            // 获取所有仪表板预警规则
            ReportWarnQueryDTO dashboardReportReq = ReportWarnQueryDTO.builder().warnSourceType("dashboard_report").build();
            List<ReportWarnDo> dashboardReportWarnList = warnDAOService.query(dashboardReportReq);
            List<DashboardCardDO> dashboardCardList = dashboardCardService.findByIdsAndCardType(dashboardIdList, NewCardTypeEnum.REPORT);
            List<String> cardUniqueKeyList = dashboardCardList.stream().map(DashboardCardDO::getCardUniqueKey).collect(Collectors.toList());
            List<String> warnCodeList = dashboardReportWarnList.stream()
                    .filter(e -> cardUniqueKeyList.contains(e.getReportId()))
                    .map(ReportWarnDo::getCode)
                    .collect(Collectors.toList());
            ReportWarnRuleDTO warnRuleReq = ReportWarnRuleDTO.builder().warnCodeList(warnCodeList).build();
            List<ReportWarnRuleDo> activeDashboardReportWarnRuleList = warnRuleDAOService.findBak(warnRuleReq);
            log.info("获取到仪表板预警数量: {}", activeDashboardReportWarnRuleList.size());


            // 创建数据集字段配置缓存
            Map<Long, List<DatasetColumnConfigDTO>> columnConfigMap = new HashMap<>();
            // 创建UUID映射表，用于记录原始UUID到新UUID的映射关系
            Map<String, String> uuidMap = new HashMap<>();
            // 创建config_uuid映射表，用于记录原始uuid到新config_uuid的映射关系
            Map<String, String> configUuidMap = new HashMap<>();

            // 处理各个表的UUID订正
            processReportCardTable(batchId, activeDashboardReportCardList, columnConfigMap, uuidMap, configUuidMap);
            processFilterCardTable(batchId, activeDashboardFilterCardList, columnConfigMap, uuidMap, configUuidMap);
            processIndexTextCardTable(batchId, activeDashboardIndexTextCardList, columnConfigMap, uuidMap, configUuidMap);
            processObjectSubscribeTable(batchId, dashboardObjectSubscribeList, columnConfigMap, uuidMap, configUuidMap);
            processReportWarnTable(batchId, activeDashboardReportWarnRuleList, columnConfigMap, uuidMap, configUuidMap);

            log.info("仪表板UUID数据订正完成");
        } catch (Exception e) {
            log.error("仪表板UUID数据订正失败", e);
            throw new BusinessException("仪表板UUID数据订正失败: " + e.getMessage());
        }
    }

    /**
     * 匹配新的UUID，复用ReportUuid中的逻辑
     */
    private String matchDatasetUuid(String oldUuid, Long id, String enName, String cnName,
                                    List<DatasetColumnConfigDTO> datasetColumnList, Map<String, String> uuidMap) {
        boolean hasEnName = StringUtils.isNotBlank(enName);
        boolean hasCnName = StringUtils.isNotBlank(cnName);

        for (DatasetColumnConfigDTO configDTO : datasetColumnList) {
            // 1. 优先根据 id 匹配
            if (id != null && id.equals(configDTO.getId())) {
                return configDTO.getUuid();
            }

            // 2. enName 匹配
            if (hasEnName && enName.equals(configDTO.getEnName())) {
                return configDTO.getUuid();
            }

            // 3. cnName 精确匹配或日期后缀裁剪匹配
            if (hasCnName) {
                String targetCnName = configDTO.getName();
                if (cnName.equals(targetCnName)) {
                    return configDTO.getUuid();
                }

                // 日期字段特殊处理：去掉最后3位后匹配
                if (cnName.length() > 3 && (
                        cnName.endsWith("(年)") || cnName.endsWith("(月)") || cnName.endsWith("(日)") ||
                                cnName.endsWith("(周)") || cnName.endsWith("(季)") || cnName.endsWith("(时)") ||
                                cnName.endsWith("(分)") || cnName.endsWith("(秒)"))
                ) {
                    String trimmedCnName = cnName.substring(0, cnName.length() - 3);
                    if (trimmedCnName.equals(targetCnName)) {
                        return configDTO.getUuid();
                    }
                }
            }
        }

        // 如果已有的计算字段，根据id + enName生成key，匹配uuid返回
        if (uuidMap.containsKey(id + enName)) {
            return uuidMap.get(id + enName);
        }

        // 如果都没匹配上，抛出异常
        throw new BusinessException("匹配失败, 原始信息 oldUuid " + oldUuid + ", fieldId " + id
                + ", enName " + enName + ", cnName " + cnName + ", datasetColumnList " + datasetColumnList);
    }

    /**
     * 处理t_dashboard_report_card表
     */
    private void processReportCardTable(String batchId, List<DashboardReportCardDO> reportCardList,
                                        Map<Long, List<DatasetColumnConfigDTO>> columnConfigMap,
                                        Map<String, String> uuidMap,
                                        Map<String, String> configUuidMap) {
        // 记录日志
        List<DashboardCorrectionLogDO> correctionLogs = new ArrayList<>();
        // 收集需要批量更新的记录
        List<DashboardReportCardDO> toUpdateList = new ArrayList<>();

        for (DashboardReportCardDO reportCard : reportCardList) {
            try {
                // 获取数据集ID
                Long datasetId = getDatasetIdFromReportCard(reportCard);

                // 获取数据集字段配置
                List<DatasetColumnConfigDTO> datasetColumnList = getDatasetColumnConfig(columnConfigMap, datasetId);

                if (CollUtil.isEmpty(datasetColumnList)) {
                    log.warn("报表卡片ID: {} 对应的数据集ID: {} 没有找到字段配置", reportCard.getId(), datasetId);
                    throw new BusinessException("报表卡片ID: " + reportCard.getId() + " 对应的数据集ID: " + datasetId + " 没有找到字段配置");
                }

                boolean hasChanges = false;

                // 处理compute_column字段，对于报表生成的计算字段，需要生成uuid，不生成configUuid
                FieldProcessResult computeColumnResult = processComputeColumnWithLogging(
                        reportCard.getComputeColumn(), "compute_column", datasetColumnList, uuidMap, configUuidMap);

                if (computeColumnResult.hasChanges()) {
                    reportCard.setComputeColumn(computeColumnResult.getProcessedValue());
                    hasChanges = true;
                }

                correctionLogs.add(buildCorrectionLog(batchId, DashboardCorrectionLogDO.TableName.DASHBOARD_REPORT_CARD,
                        "compute_column", reportCard.getId(), reportCard.getDashboardId(), datasetId,
                        computeColumnResult.getOriginalValue(), computeColumnResult.getProcessedValue(),
                        computeColumnResult.getStatus(), computeColumnResult.getErrorType(),
                        computeColumnResult.getErrorMessage(),
                        computeColumnResult.getException() != null ? getStackTrace(computeColumnResult.getException()) : null));

                // 处理report_structure字段 - 仅替换uuid，不生成configUuid
                FieldProcessResult reportStructureResult = processReportStructureWithLogging(
                        reportCard.getReportStructure(), "report_structure", datasetColumnList, uuidMap, configUuidMap);

                if (reportStructureResult.hasChanges()) {
                    reportCard.setReportStructure(reportStructureResult.getProcessedValue());
                    hasChanges = true;
                }

                correctionLogs.add(buildCorrectionLog(batchId, DashboardCorrectionLogDO.TableName.DASHBOARD_REPORT_CARD,
                        "report_structure", reportCard.getId(), reportCard.getDashboardId(), datasetId,
                        reportStructureResult.getOriginalValue(), reportStructureResult.getProcessedValue(),
                        reportStructureResult.getStatus(), reportStructureResult.getErrorType(),
                        reportStructureResult.getErrorMessage(),
                        reportStructureResult.getException() != null ? getStackTrace(reportStructureResult.getException()) : null));

                // 处理show_column字段
                FieldProcessResult showColumnResult = processShowColumnWithLogging(
                        reportCard.getShowColumn(), "show_column", datasetColumnList, uuidMap, configUuidMap);

                if (showColumnResult.hasChanges()) {
                    reportCard.setShowColumn(showColumnResult.getProcessedValue());
                    hasChanges = true;
                }

                correctionLogs.add(buildCorrectionLog(batchId, DashboardCorrectionLogDO.TableName.DASHBOARD_REPORT_CARD,
                        "show_column", reportCard.getId(), reportCard.getDashboardId(), datasetId,
                        showColumnResult.getOriginalValue(), showColumnResult.getProcessedValue(),
                        showColumnResult.getStatus(), showColumnResult.getErrorType(),
                        showColumnResult.getErrorMessage(),
                        showColumnResult.getException() != null ? getStackTrace(showColumnResult.getException()) : null));

                // 处理contrast_column字段
                FieldProcessResult contrastColumnResult = processShowColumnWithLogging(
                        reportCard.getContrastColumn(), "contrast_column", datasetColumnList, uuidMap, configUuidMap);

                if (contrastColumnResult.hasChanges()) {
                    reportCard.setContrastColumn(contrastColumnResult.getProcessedValue());
                    hasChanges = true;
                }

                correctionLogs.add(buildCorrectionLog(batchId, DashboardCorrectionLogDO.TableName.DASHBOARD_REPORT_CARD,
                        "contrast_column", reportCard.getId(), reportCard.getDashboardId(), datasetId,
                        contrastColumnResult.getOriginalValue(), contrastColumnResult.getProcessedValue(),
                        contrastColumnResult.getStatus(), contrastColumnResult.getErrorType(),
                        contrastColumnResult.getErrorMessage(),
                        contrastColumnResult.getException() != null ? getStackTrace(contrastColumnResult.getException()) : null));

                // 处理index_column字段
                FieldProcessResult indexColumnResult = processIndexColumnWithLogging(
                        reportCard.getIndexColumn(), "index_column", datasetColumnList, uuidMap, configUuidMap);

                if (indexColumnResult.hasChanges()) {
                    reportCard.setIndexColumn(indexColumnResult.getProcessedValue());
                    hasChanges = true;
                }

                correctionLogs.add(buildCorrectionLog(batchId, DashboardCorrectionLogDO.TableName.DASHBOARD_REPORT_CARD,
                        "index_column", reportCard.getId(), reportCard.getDashboardId(), datasetId,
                        indexColumnResult.getOriginalValue(), indexColumnResult.getProcessedValue(),
                        indexColumnResult.getStatus(), indexColumnResult.getErrorType(),
                        indexColumnResult.getErrorMessage(),
                        indexColumnResult.getException() != null ? getStackTrace(indexColumnResult.getException()) : null));

                // 处理filter_column字段
                FieldProcessResult filterColumnResult = processFilterColumnWithLogging(
                        reportCard.getFilterColumn(), "filter_column", datasetColumnList, uuidMap, configUuidMap);

                if (filterColumnResult.hasChanges()) {
                    reportCard.setFilterColumn(filterColumnResult.getProcessedValue());
                    hasChanges = true;
                }

                correctionLogs.add(buildCorrectionLog(batchId, DashboardCorrectionLogDO.TableName.DASHBOARD_REPORT_CARD,
                        "filter_column", reportCard.getId(), reportCard.getDashboardId(), datasetId,
                        filterColumnResult.getOriginalValue(), filterColumnResult.getProcessedValue(),
                        filterColumnResult.getStatus(), filterColumnResult.getErrorType(),
                        filterColumnResult.getErrorMessage(),
                        filterColumnResult.getException() != null ? getStackTrace(filterColumnResult.getException()) : null));

                // 处理keyword字段
                FieldProcessResult keywordResult = processKeywordWithLogging(
                        reportCard.getKeyword(), "keyword", datasetColumnList, uuidMap, configUuidMap);

                if (keywordResult.hasChanges()) {
                    reportCard.setKeyword(keywordResult.getProcessedValue());
                    hasChanges = true;
                }

                correctionLogs.add(buildCorrectionLog(batchId, DashboardCorrectionLogDO.TableName.DASHBOARD_REPORT_CARD,
                        "keyword", reportCard.getId(), reportCard.getDashboardId(), datasetId,
                        keywordResult.getOriginalValue(), keywordResult.getProcessedValue(),
                        keywordResult.getStatus(), keywordResult.getErrorType(),
                        keywordResult.getErrorMessage(),
                        keywordResult.getException() != null ? getStackTrace(keywordResult.getException()) : null));

                // 处理order_column字段
                FieldProcessResult orderColumnResult = processOrderColumnWithLogging(
                        reportCard.getOrderColumn(), "order_column", datasetColumnList, uuidMap, configUuidMap);

                if (orderColumnResult.hasChanges()) {
                    reportCard.setOrderColumn(orderColumnResult.getProcessedValue());
                    hasChanges = true;
                }

                correctionLogs.add(buildCorrectionLog(batchId, DashboardCorrectionLogDO.TableName.DASHBOARD_REPORT_CARD,
                        "order_column", reportCard.getId(), reportCard.getDashboardId(), datasetId,
                        orderColumnResult.getOriginalValue(), orderColumnResult.getProcessedValue(),
                        orderColumnResult.getStatus(), orderColumnResult.getErrorType(),
                        orderColumnResult.getErrorMessage(),
                        orderColumnResult.getException() != null ? getStackTrace(orderColumnResult.getException()) : null));

                // 处理sensitive_fields字段
                FieldProcessResult sensitiveFieldsResult = processSensitiveFieldsWithLogging(
                        reportCard.getSensitiveFields(), "sensitive_fields", datasetColumnList, uuidMap, configUuidMap);

                if (sensitiveFieldsResult.hasChanges()) {
                    reportCard.setSensitiveFields(sensitiveFieldsResult.getProcessedValue());
                    hasChanges = true;
                }

                correctionLogs.add(buildCorrectionLog(batchId, DashboardCorrectionLogDO.TableName.DASHBOARD_REPORT_CARD,
                        "sensitive_fields", reportCard.getId(), reportCard.getDashboardId(), datasetId,
                        sensitiveFieldsResult.getOriginalValue(), sensitiveFieldsResult.getProcessedValue(),
                        sensitiveFieldsResult.getStatus(), sensitiveFieldsResult.getErrorType(),
                        sensitiveFieldsResult.getErrorMessage(),
                        sensitiveFieldsResult.getException() != null ? getStackTrace(sensitiveFieldsResult.getException()) : null));

                // 处理table_configuration字段
                FieldProcessResult tableConfigurationResult = processTableConfigurationWithLogging(
                        reportCard.getTableConfiguration(), "table_configuration", datasetColumnList, uuidMap, configUuidMap);

                if (tableConfigurationResult.hasChanges()) {
                    reportCard.setTableConfiguration(tableConfigurationResult.getProcessedValue());
                    hasChanges = true;
                }

                correctionLogs.add(buildCorrectionLog(batchId, DashboardCorrectionLogDO.TableName.DASHBOARD_REPORT_CARD,
                        "table_configuration", reportCard.getId(), reportCard.getDashboardId(), datasetId,
                        tableConfigurationResult.getOriginalValue(), tableConfigurationResult.getProcessedValue(),
                        tableConfigurationResult.getStatus(), tableConfigurationResult.getErrorType(),
                        tableConfigurationResult.getErrorMessage(),
                        tableConfigurationResult.getException() != null ? getStackTrace(tableConfigurationResult.getException()) : null));

                // 如果有变更，添加到批量更新列表
                if (hasChanges) {
                    toUpdateList.add(reportCard);
                    log.info("准备批量更新报表卡片ID: {}", reportCard.getId());
                }

            } catch (Exception e) {
                log.error("处理报表卡片ID: {} 时发生整体错误", reportCard.getId(), e);
                // 记录整体错误日志
                correctionLogs.add(buildCorrectionLog(batchId, DashboardCorrectionLogDO.TableName.DASHBOARD_REPORT_CARD,
                        "RECORD_LEVEL_ERROR", reportCard.getId(), reportCard.getDashboardId(),
                        getDatasetIdFromReportCard(reportCard), null, null,
                        DashboardCorrectionLogDO.Status.FAILURE, DashboardCorrectionLogDO.ErrorType.DATA_PROCESSING_ERROR,
                        e.getMessage(), getStackTrace(e)));
            }
        }

        // 批量更新数据库
        if (!toUpdateList.isEmpty()) {
            try {
                for (List<DashboardReportCardDO> reportCardDOS : Lists.partition(toUpdateList, BATCH_SIZE)) {
                    dashboardReportCardService.batchUpdateBak(reportCardDOS);
                }
                dashboardReportCardService.batchUpdateBak(toUpdateList);
                log.info("批量更新了{}条t_dashboard_report_card_bak表记录", toUpdateList.size());
            } catch (Exception e) {
                log.error("批量更新t_dashboard_report_card_bak表失败", e);
            }
        }

        // 批量记录订正日志
        if (!correctionLogs.isEmpty()) {
            try {
                for (List<DashboardCorrectionLogDO> dashboardCorrectionLogDOS : Lists.partition(correctionLogs, BATCH_SIZE)) {
                    dashboardCorrectionLogService.batchInsert(dashboardCorrectionLogDOS);
                }
                log.info("记录了{}条t_dashboard_report_card表订正日志", correctionLogs.size());
            } catch (Exception e) {
                log.error("记录t_dashboard_report_card表订正日志失败", e);
            }
        }

        log.info("完成处理t_dashboard_report_card表");
    }


    /**
     * 获取数据集字段配置
     */
    private List<DatasetColumnConfigDTO> getDatasetColumnConfig(Map<Long, List<DatasetColumnConfigDTO>> columnConfigMap,
                                                                Long datasetId) {
        List<DatasetColumnConfigDTO> datasetColumnList = new ArrayList<>();
        if (columnConfigMap.containsKey(datasetId)) {
            datasetColumnList = columnConfigMap.get(datasetId);
        } else {
            DatasetColumnConfigRequest req = new DatasetColumnConfigRequest();
            req.setDatasetId(datasetId);
            Response<List<DatasetColumnConfigDTO>> response = datasetService.getColumnConfigList(req);
            if (response.isSuccess()) {
                datasetColumnList = response.getData();
                columnConfigMap.put(datasetId, datasetColumnList);
            }
        }
        return datasetColumnList;
    }

    /**
     * 从报表卡片中获取数据集ID
     */
    private Long getDatasetIdFromReportCard(DashboardReportCardDO reportCard) {
        try {
            if (reportCard.getDatasetId() != null) {
                return reportCard.getDatasetId();
            }
            if (StringUtils.isNotBlank(reportCard.getDatasetInfo())) {
                JSONArray datasetInfoArray = JSONUtil.parseArray(reportCard.getDatasetInfo());
                if (!datasetInfoArray.isEmpty()) {
                    JSONObject datasetInfo = datasetInfoArray.getJSONObject(0);
                    return datasetInfo.getLong("datasetId");
                }
            }
        } catch (Exception e) {
            log.error("获取报表卡片数据集ID失败，报表卡片ID: {}", reportCard.getId(), e);
        }
        throw new BusinessException("无法获取报表卡片的数据集ID，报表卡片ID: " + reportCard.getId());
    }

    /**
     * 处理report_structure字段 - 仅替换uuid，不生成configUuid
     */
    private String processReportStructure(String reportStructure,
                                          List<DatasetColumnConfigDTO> datasetColumnList,
                                          Map<String, String> uuidMap,
                                          Map<String, String> configUuidMap) {
        try {
            JSONArray jsonArray = JSONUtil.parseArray(reportStructure);
            for (Object item : jsonArray) {
                if (item instanceof JSONObject) {
                    JSONObject jsonObject = (JSONObject) item;
                    processJsonObjectUuid(jsonObject, datasetColumnList, false, uuidMap, configUuidMap);
                }
            }
            return jsonArray.toString();
        } catch (Exception e) {
            log.error("处理report_structure字段失败", e);
            throw e;
        }
    }

    /**
     * 处理show_column字段
     */
    private String processShowColumn(String showColumn,
                                     List<DatasetColumnConfigDTO> datasetColumnList,
                                     Map<String, String> uuidMap,
                                     Map<String, String> configUuidMap) {
        try {
            JSONArray jsonArray = JSONUtil.parseArray(showColumn);
            for (Object item : jsonArray) {
                if (item instanceof JSONObject) {
                    JSONObject jsonObject = (JSONObject) item;
                    String name = jsonObject.getStr("name");
                    String enName = jsonObject.getStr("enName");
                    String reportField = jsonObject.getStr("reportField");
                    if ("measure".equalsIgnoreCase(reportField)) {
                        // 如果name为"度量名"，生成uuid（report_measure_ + UUID.randomUUID()），生成configUuid
                        String oldUuid = jsonObject.getStr("uuid");
                        String newUuid = ReportUuidGenerateUtil.generateReportMeasureUuid();
                        String configUuid = ReportUuidGenerateUtil.generateReportConfigUuid();
                        jsonObject.set("uuid", newUuid);
                        jsonObject.set("configUuid", configUuid);

                        // 记录UUID映射关系
                        if (StringUtils.isNotBlank(oldUuid) && !oldUuid.equals(newUuid)) {
                            uuidMap.put(oldUuid, newUuid);
                        }
                        if (StringUtils.isNotBlank(oldUuid) && !oldUuid.equals(configUuid)) {
                            configUuidMap.put(oldUuid, configUuid);
                        }

                    } else {
                        // 如果name非"度量名"，替换uuid，生成configUuid
                        processJsonObjectUuid(jsonObject, datasetColumnList, true, uuidMap, configUuidMap);
                    }
                }
            }
            return jsonArray.toString();
        } catch (Exception e) {
            log.error("处理show_column字段失败", e);
            throw e;
        }
    }

    /**
     * 处理index_column字段
     */
    private String processIndexColumn(String indexColumn, List<DatasetColumnConfigDTO> datasetColumnList, Map<String, String> uuidMap, Map<String, String> configUuidMap) {
        return processStandardJsonField(indexColumn, datasetColumnList, "index_column", uuidMap, configUuidMap);
    }

    /**
     * 处理filter_column字段
     */
    private String processFilterColumn(String filterColumn, List<DatasetColumnConfigDTO> datasetColumnList, Map<String, String> uuidMap, Map<String, String> configUuidMap) {
        return processStandardJsonField(filterColumn, datasetColumnList, "filter_column", uuidMap, configUuidMap);
    }

    /**
     * 处理con_control字段
     */
    private String processConControl(String conControl, List<DatasetColumnConfigDTO> datasetColumnList, Map<String, String> uuidMap, Map<String, String> configUuidMap) {
        return processStandardJsonField(conControl, datasetColumnList, "con_control", uuidMap, configUuidMap);
    }

    /**
     * 处理keyword字段
     */
    private String processKeyword(String keyword, List<DatasetColumnConfigDTO> datasetColumnList, Map<String, String> uuidMap, Map<String, String> configUuidMap) {
        return processStandardJsonField(keyword, datasetColumnList, "keyword", uuidMap, configUuidMap);
    }

    /**
     * 处理order_column字段
     */
    private String processOrderColumn(String orderColumn, List<DatasetColumnConfigDTO> datasetColumnList, Map<String, String> uuidMap, Map<String, String> configUuidMap) {
        return processStandardJsonField(orderColumn, datasetColumnList, "order_column", uuidMap, configUuidMap);
    }

    /**
     * 处理compute_column字段
     */
    private String processComputeColumn(String computeColumn, List<DatasetColumnConfigDTO> datasetColumnList, Map<String, String> uuidMap, Map<String, String> configUuidMap) {
        try {
            JSONArray jsonArray = JSONUtil.parseArray(computeColumn);
            for (Object item : jsonArray) {
                if (item instanceof JSONObject) {
                    JSONObject jsonObject = (JSONObject) item;
                    String id = jsonObject.getStr("id");
                    String enName = jsonObject.getStr("enName");
                    if (StringUtils.isBlank(enName)) {
                        throw new BusinessException("计算字段缺少enName");
                    }
                    // 生成uuid（report_compute_ + UUID.randomUUID()）
                    String newUuid = ReportUuidGenerateUtil.generateReportComputeUuid();
                    jsonObject.set("uuid", newUuid);

                    // 记录计算字段UUID映射关系，Map<id + enName, uuid>
                    uuidMap.put(id + enName, newUuid);
                }
            }
            return jsonArray.toString();
        } catch (Exception e) {
            log.error("处理compute_column字段失败", e);
            throw e;
        }
    }

    /**
     * 处理sensitive_fields字段
     */
    private String processSensitiveFields(String sensitiveFields,
                                          List<DatasetColumnConfigDTO> datasetColumnList,
                                          Map<String, String> uuidMap,
                                          Map<String, String> configUuidMap) {
        try {
            JSONArray jsonArray = JSONUtil.parseArray(sensitiveFields);
            for (Object item : jsonArray) {
                if (item instanceof JSONObject) {
                    JSONObject jsonObject = (JSONObject) item;
                    // 替换uuid
                    processJsonObjectUuid(jsonObject, datasetColumnList, false, uuidMap, configUuidMap);

                    // 生成新的configUuid
                    jsonObject.set("configUuid", ReportUuidGenerateUtil.generateReportConfigUuid());
                }
            }
            return jsonArray.toString();
        } catch (Exception e) {
            log.error("处理sensitive_fields字段失败", e);
            throw e;
        }
    }

    /**
     * 处理table_configuration字段
     */
    private String processTableConfiguration(String tableConfiguration,
                                             List<DatasetColumnConfigDTO> datasetColumnList,
                                             Map<String, String> uuidMap,
                                             Map<String, String> configUuidMap) {
        try {

            JSONObject jsonObject = JSONUtil.parseObj(tableConfiguration);
            Object basicFormat = jsonObject.get("basicFormat");
            if (null != basicFormat) {
                JSONObject basicFormatObj = JSONUtil.parseObj(basicFormat);
                Object customColumnWidth = basicFormatObj.get("customColumnWidth");
                if (null != customColumnWidth) {
                    JSONArray customColumnWidthArray = JSONUtil.parseArray(customColumnWidth);
                    for (Object item : customColumnWidthArray) {
                        if (item instanceof JSONObject) {
                            JSONObject customColumnWidthObj = (JSONObject) item;
                            String oldUuid = customColumnWidthObj.getStr("uuid");
                            String columnName = customColumnWidthObj.getStr("columnName");
                            String reportField = customColumnWidthObj.getStr("reportField");

                            // 度量值
                            if ("measureValue".equalsIgnoreCase(reportField)) {
                                continue;
                            }

                            if (StringUtils.isNotBlank(oldUuid) && uuidMap.containsKey(oldUuid)) {
                                String newUuid = uuidMap.get(oldUuid);
                                customColumnWidthObj.set("uuid", newUuid);
                                log.debug("自定义宽度UUID替换: {} -> {}", oldUuid, newUuid);
                            } else {
                                throw new BusinessException("自定义宽度uuid订正失败 old uuid: {}" + oldUuid + ", columnName: {}," + columnName + ", reportField: {}" + reportField);
                            }

                            if (StringUtils.isNotBlank(oldUuid) && configUuidMap.containsKey(oldUuid)) {
                                String newConfigUuid = configUuidMap.get(oldUuid);
                                customColumnWidthObj.set("configUuid", newConfigUuid);
                                log.debug("自定义宽度configUuid匹配:{}", newConfigUuid);
                            } else {
                                throw new BusinessException("自定义宽度configUuid订正失败 old uuid: {}" + oldUuid + ", columnName: {}," + columnName + ", reportField: {}" + reportField);
                            }
                        }
                    }
                }
            }

            return jsonObject.toString();
        } catch (Exception e) {
            log.error("处理table_configuration字段失败", e);
            throw e;
        }
    }

    /**
     * 处理标准JSON字段（替换uuid，生成configUuid）
     */
    private String processStandardJsonField(String jsonField, List<DatasetColumnConfigDTO> datasetColumnList,
                                            String fieldName,
                                            Map<String, String> uuidMap,
                                            Map<String, String> configUuidMap) {
        try {
            JSONArray jsonArray = JSONUtil.parseArray(jsonField);
            for (Object item : jsonArray) {
                if (item instanceof JSONObject) {
                    JSONObject jsonObject = (JSONObject) item;
                    processJsonObjectUuid(jsonObject, datasetColumnList, true, uuidMap, configUuidMap);
                }
            }
            return jsonArray.toString();
        } catch (Exception e) {
            log.error("处理{}字段失败", fieldName, e);
            throw e;
        }
    }

    /**
     * 处理JSON对象中的UUID
     */
    private void processJsonObjectUuid(JSONObject jsonObject,
                                       List<DatasetColumnConfigDTO> datasetColumnList,
                                       boolean generateConfigUuid,
                                       Map<String, String> uuidMap,
                                       Map<String, String> configUuidMap) {
        try {
            String oldUuid = jsonObject.getStr("uuid");
            Long id = jsonObject.getLong("id");
            String enName = jsonObject.getStr("enName");
            String name = jsonObject.getStr("name");
            String originalName = jsonObject.getStr("originalName");
            String childColumnList = jsonObject.getStr("childColumnList");
            JSONArray jsonArray = JSONUtil.parseArray(childColumnList);

            // 使用name或originalName作为中文名进行匹配
            String cnName = StringUtils.isNotBlank(originalName) ? originalName : name;

            if (!jsonArray.isEmpty()) {
                for (Object item : jsonArray) {
                    if (item instanceof JSONObject) {
                        JSONObject child = (JSONObject) item;
                        processJsonObjectUuid(child, datasetColumnList, false, uuidMap, configUuidMap);
                    }
                }
            } else {
                // 匹配新的UUID
                String datasetUuid = matchDatasetUuid(oldUuid, id, enName, cnName, datasetColumnList, uuidMap);
                jsonObject.set("uuid", datasetUuid);

                // 记录UUID映射关系（只有当原始UUID和新UUID不同时才记录）
                if (StringUtils.isNotBlank(oldUuid) && !oldUuid.equals(datasetUuid)) {
                    uuidMap.put(oldUuid, datasetUuid);
                }

                // 生成configUuid
                if (generateConfigUuid) {
                    String configUuid = ReportUuidGenerateUtil.generateReportConfigUuid();
                    jsonObject.set("configUuid", configUuid);
                    configUuidMap.put(oldUuid, configUuid);
                }
            }

        } catch (Exception e) {
            log.error("处理JSON对象UUID失败: {}", jsonObject.toString(), e);
            throw e;
        }
    }

    /**
     * 处理t_dashboard_filter_card表
     */
    private void processFilterCardTable(String batchId, List<DashboardFilterCardDO> filterCardList,
                                        Map<Long, List<DatasetColumnConfigDTO>> columnConfigMap,
                                        Map<String, String> uuidMap,
                                        Map<String, String> configUuidMap) {
        log.info("开始处理t_dashboard_filter_card表，数量: {}", filterCardList.size());

        List<DashboardCorrectionLogDO> correctionLogs = new ArrayList<>();
        // 收集需要批量更新的记录
        List<DashboardFilterCardDO> toUpdateList = new ArrayList<>();

        for (DashboardFilterCardDO filterCard : filterCardList) {
            try {
                Long datasetId = filterCard.getDatasetId();
                if (datasetId == null) {
                    log.warn("筛选器卡片ID: {} 没有数据集ID", filterCard.getId());
                    throw new BusinessException("筛选器卡片ID: " + filterCard.getId() + " 没有数据集ID");
                }

                // 获取数据集配置
                List<DatasetColumnConfigDTO> datasetColumnList = getDatasetColumnConfig(columnConfigMap, datasetId);
                if (CollUtil.isEmpty(datasetColumnList)) {
                    log.warn("筛选器卡片ID: {} 对应的数据集ID: {} 没有找到字段配置", filterCard.getId(), datasetId);
                    throw new BusinessException("筛选器卡片ID: " + filterCard.getId() + " 对应的数据集ID: " + datasetId + " 没有找到字段配置");
                }

                boolean hasChanges = false;

                // 处理fields字段 - 替换uuid，不生成configUuid
                FieldProcessResult fieldsResult = processFilterCardFieldsWithLogging(
                        filterCard.getFields(), "fields", datasetColumnList, uuidMap, configUuidMap);

                if (fieldsResult.hasChanges()) {
                    filterCard.setFields(fieldsResult.getProcessedValue());
                    hasChanges = true;
                }

                correctionLogs.add(buildCorrectionLog(batchId, DashboardCorrectionLogDO.TableName.DASHBOARD_FILTER_CARD,
                        "fields", filterCard.getId(), filterCard.getDashboardId(), filterCard.getDatasetId(),
                        fieldsResult.getOriginalValue(), fieldsResult.getProcessedValue(),
                        fieldsResult.getStatus(), fieldsResult.getErrorType(),
                        fieldsResult.getErrorMessage(),
                        fieldsResult.getException() != null ? getStackTrace(fieldsResult.getException()) : null));

                // 如果有变更，添加到批量更新列表
                if (hasChanges) {
                    toUpdateList.add(filterCard);
                    log.info("准备批量更新筛选器卡片ID: {}", filterCard.getId());
                }

            } catch (Exception e) {
                log.error("处理筛选器卡片ID: {} 时发生整体错误", filterCard.getId(), e);
                // 记录整体错误日志
                correctionLogs.add(buildCorrectionLog(batchId, DashboardCorrectionLogDO.TableName.DASHBOARD_FILTER_CARD,
                        "RECORD_LEVEL_ERROR", filterCard.getId(), filterCard.getDashboardId(), filterCard.getDatasetId(),
                        null, null, DashboardCorrectionLogDO.Status.FAILURE,
                        DashboardCorrectionLogDO.ErrorType.DATA_PROCESSING_ERROR, e.getMessage(), getStackTrace(e)));
            }
        }

        // 批量更新数据库
        if (!toUpdateList.isEmpty()) {
            try {
                dashboardFilterCardService.batchUpdateAllBak(toUpdateList);
                log.info("批量更新了{}条t_dashboard_filter_card_bak表记录", toUpdateList.size());
            } catch (Exception e) {
                log.error("批量更新t_dashboard_filter_card_bak表失败", e);
            }
        }

        // 批量记录订正日志
        if (!correctionLogs.isEmpty()) {
            try {
                dashboardCorrectionLogService.batchInsert(correctionLogs);
                log.info("记录了{}条t_dashboard_filter_card表订正日志", correctionLogs.size());
            } catch (Exception e) {
                log.error("记录t_dashboard_filter_card表订正日志失败", e);
            }
        }

        log.info("完成处理t_dashboard_filter_card表");
    }

    /**
     * 处理筛选器卡片的fields字段
     */
    private String processFilterCardFields(String fields, List<DatasetColumnConfigDTO> datasetColumnList, Map<String, String> uuidMap, Map<String, String> configUuidMap) {
        try {
            JSONObject jsonObject = JSONUtil.parseObj(fields);
            processJsonObjectUuid(jsonObject, datasetColumnList, false, uuidMap, configUuidMap);
            return jsonObject.toString();
        } catch (Exception e) {
            log.error("处理筛选器卡片fields字段失败", e);
            throw e;
        }
    }

    /**
     * 处理t_dashboard_index_text_card表
     */
    private void processIndexTextCardTable(String batchId, List<DashboardIndexTextCardDO> indexTextCardList,
                                           Map<Long, List<DatasetColumnConfigDTO>> columnConfigMap,
                                           Map<String, String> uuidMap,
                                           Map<String, String> configUuidMap) {
        log.info("开始处理t_dashboard_index_text_card表，数量: {}", indexTextCardList.size());

        List<DashboardCorrectionLogDO> correctionLogs = new ArrayList<>();
        // 收集需要批量更新的记录
        List<DashboardIndexTextCardDO> toUpdateList = new ArrayList<>();

        for (DashboardIndexTextCardDO indexTextCard : indexTextCardList) {
            try {
                Long datasetId = indexTextCard.getDatasetId();
                String indexInfo = indexTextCard.getIndexInfo();
                JSONObject jsonObject = JSONUtil.parseObj(indexInfo);
                datasetId = datasetId == null ? jsonObject.getLong("dataSet") : datasetId;
                if (datasetId == null) {
                    log.warn("指标文本卡片ID: {} 没有数据集ID", indexTextCard.getId());
                    throw new BusinessException("指标文本卡片ID: " + indexTextCard.getId() + " 没有数据集ID");
                }

                // 获取数据集配置信息
                List<DatasetColumnConfigDTO> datasetColumnList = getDatasetColumnConfig(columnConfigMap, datasetId);
                if (CollUtil.isEmpty(datasetColumnList)) {
                    log.warn("指标文本卡片ID: {} 对应的数据集ID: {} 没有找到字段配置", indexTextCard.getId(), datasetId);
                    throw new BusinessException("指标文本卡片ID: " + indexTextCard.getId() + " 对应的数据集ID: " + datasetId + " 没有找到字段配置");
                }

                boolean hasChanges = false;

                // 处理drag_result字段
                FieldProcessResult dragResultResult = processStandardJsonFieldWithLogging(
                        indexTextCard.getDragResult(), "drag_result", datasetColumnList, uuidMap, configUuidMap);

                if (dragResultResult.hasChanges()) {
                    indexTextCard.setDragResult(dragResultResult.getProcessedValue());
                    hasChanges = true;
                }

                correctionLogs.add(buildCorrectionLog(batchId, DashboardCorrectionLogDO.TableName.DASHBOARD_INDEX_TEXT_CARD,
                        "drag_result", indexTextCard.getId(), indexTextCard.getDashboardId(), indexTextCard.getDatasetId(),
                        dragResultResult.getOriginalValue(), dragResultResult.getProcessedValue(),
                        dragResultResult.getStatus(), dragResultResult.getErrorType(),
                        dragResultResult.getErrorMessage(),
                        dragResultResult.getException() != null ? getStackTrace(dragResultResult.getException()) : null));

                // 处理filter_drag_result字段
                FieldProcessResult filterDragResultResult = processStandardJsonFieldWithLogging(
                        indexTextCard.getFilterdragResult(), "filter_drag_result", datasetColumnList, uuidMap, configUuidMap);

                if (filterDragResultResult.hasChanges()) {
                    indexTextCard.setFilterdragResult(filterDragResultResult.getProcessedValue());
                    hasChanges = true;
                }

                correctionLogs.add(buildCorrectionLog(batchId, DashboardCorrectionLogDO.TableName.DASHBOARD_INDEX_TEXT_CARD,
                        "filter_drag_result", indexTextCard.getId(), indexTextCard.getDashboardId(), indexTextCard.getDatasetId(),
                        filterDragResultResult.getOriginalValue(), filterDragResultResult.getProcessedValue(),
                        filterDragResultResult.getStatus(), filterDragResultResult.getErrorType(),
                        filterDragResultResult.getErrorMessage(),
                        filterDragResultResult.getException() != null ? getStackTrace(filterDragResultResult.getException()) : null));

                // 处理count_field_list字段
                FieldProcessResult countFieldListResult = processStandardJsonFieldWithLogging(
                        indexTextCard.getFilterdragResult(), "count_field_list", datasetColumnList, uuidMap, configUuidMap);

                if (countFieldListResult.hasChanges()) {
                    indexTextCard.setFilterdragResult(countFieldListResult.getProcessedValue());
                    hasChanges = true;
                }

                correctionLogs.add(buildCorrectionLog(batchId, DashboardCorrectionLogDO.TableName.DASHBOARD_INDEX_TEXT_CARD,
                        "count_field_list", indexTextCard.getId(), indexTextCard.getDashboardId(), indexTextCard.getDatasetId(),
                        countFieldListResult.getOriginalValue(), countFieldListResult.getProcessedValue(),
                        countFieldListResult.getStatus(), countFieldListResult.getErrorType(),
                        countFieldListResult.getErrorMessage(),
                        countFieldListResult.getException() != null ? getStackTrace(countFieldListResult.getException()) : null));

                // 如果有变更，添加到批量更新列表
                if (hasChanges) {
                    toUpdateList.add(indexTextCard);
                    log.info("准备批量更新指标文本卡片ID: {}", indexTextCard.getId());
                }

            } catch (Exception e) {
                log.error("处理指标文本卡片ID: {} 时发生整体错误", indexTextCard.getId(), e);
                // 记录整体错误日志
                correctionLogs.add(buildCorrectionLog(batchId, DashboardCorrectionLogDO.TableName.DASHBOARD_INDEX_TEXT_CARD,
                        "RECORD_LEVEL_ERROR", indexTextCard.getId(), indexTextCard.getDashboardId(), indexTextCard.getDatasetId(),
                        null, null, DashboardCorrectionLogDO.Status.FAILURE,
                        DashboardCorrectionLogDO.ErrorType.DATA_PROCESSING_ERROR, e.getMessage(), getStackTrace(e)));
            }
        }

        // 批量更新数据库
        if (!toUpdateList.isEmpty()) {
            try {
                dashboardIndexTextCardService.batchUpdateBak(toUpdateList);
                log.info("批量更新了{}条t_dashboard_index_text_card_bak表记录", toUpdateList.size());
            } catch (Exception e) {
                log.error("批量更新t_dashboard_index_text_card_bak表失败", e);
            }
        }

        // 批量记录订正日志
        if (!correctionLogs.isEmpty()) {
            try {
                dashboardCorrectionLogService.batchInsert(correctionLogs);
                log.info("记录了{}条t_dashboard_index_text_card表订正日志", correctionLogs.size());
            } catch (Exception e) {
                log.error("记录t_dashboard_index_text_card表订正日志失败", e);
            }
        }

        log.info("完成处理t_dashboard_index_text_card表");
    }

    /**
     * 处理t_object_subscribe表
     */
    private void processObjectSubscribeTable(String batchId, List<ObjectSubScribeDO> objectSubscribeList,
                                             Map<Long, List<DatasetColumnConfigDTO>> columnConfigMap,
                                             Map<String, String> uuidMap,
                                             Map<String, String> configUuidMap) {
        log.info("开始处理t_object_subscribe表，数量: {}", objectSubscribeList.size());

        List<DashboardCorrectionLogDO> correctionLogs = new ArrayList<>();
        // 收集需要批量更新的记录
        List<ObjectSubScribeDO> toUpdateList = new ArrayList<>();

        for (ObjectSubScribeDO objectSubscribe : objectSubscribeList) {
            try {
                boolean hasChanges = false;

                // 处理condition字段 - 替换uuid，configUuid
                if (StringUtils.isNotBlank(objectSubscribe.getCondition())) {
                    FieldProcessResult conditionResult = processObjectSubscribeConditionWithLogging(
                            objectSubscribe.getCondition(), "condition", columnConfigMap, uuidMap, configUuidMap);

                    if (conditionResult.hasChanges()) {
                        objectSubscribe.setCondition(conditionResult.getProcessedValue());
                        hasChanges = true;
                    }

                    correctionLogs.add(buildCorrectionLog(batchId, DashboardCorrectionLogDO.TableName.OBJECT_SUBSCRIBE,
                            "condition", objectSubscribe.getId(), objectSubscribe.getObjectId(), null,
                            conditionResult.getOriginalValue(), conditionResult.getProcessedValue(),
                            conditionResult.getStatus(), conditionResult.getErrorType(),
                            conditionResult.getErrorMessage(),
                            conditionResult.getException() != null ? getStackTrace(conditionResult.getException()) : null));
                }

                // 如果有变更，添加到批量更新列表
                if (hasChanges) {
                    toUpdateList.add(objectSubscribe);
                    log.info("准备批量更新订阅ID: {}", objectSubscribe.getId());
                }

            } catch (Exception e) {
                log.error("处理订阅ID: {} 时发生整体错误", objectSubscribe.getId(), e);
                // 记录整体错误日志
                correctionLogs.add(buildCorrectionLog(batchId, DashboardCorrectionLogDO.TableName.OBJECT_SUBSCRIBE,
                        "RECORD_LEVEL_ERROR", objectSubscribe.getId(), objectSubscribe.getObjectId(), null,
                        null, null, DashboardCorrectionLogDO.Status.FAILURE,
                        DashboardCorrectionLogDO.ErrorType.DATA_PROCESSING_ERROR, e.getMessage(), getStackTrace(e)));
            }
        }

        // 批量更新数据库
        if (!toUpdateList.isEmpty()) {
            try {
                objectSubscribeService.batchUpdateByIdBak(toUpdateList);
                log.info("批量更新了{}条t_object_subscribe_bak表记录", toUpdateList.size());
            } catch (Exception e) {
                log.error("批量更新t_object_subscribe_bak表失败", e);
            }
        }

        // 批量记录订正日志
        if (!correctionLogs.isEmpty()) {
            try {
                dashboardCorrectionLogService.batchInsert(correctionLogs);
                log.info("记录了{}条t_object_subscribe表订正日志", correctionLogs.size());
            } catch (Exception e) {
                log.error("记录t_object_subscribe表订正日志失败", e);
            }
        }

        log.info("完成处理t_object_subscribe表");
    }

    /**
     * 处理订阅表的condition字段
     * condition字段结构: {"tab-uuid": [{"cardCode": "filter-uuid", "datasetId": 195, "fields": {...}}]}
     */
    private String processObjectSubscribeCondition(String condition,
                                                   Map<Long, List<DatasetColumnConfigDTO>> columnConfigMap,
                                                   Map<String, String> uuidMap,
                                                   Map<String, String> configUuidMap) throws Exception {
        try {
            JSONObject conditionJson = JSONUtil.parseObj(condition);

            // 遍历每个tab
            for (String tabKey : conditionJson.keySet()) {
                JSONArray filterArray = conditionJson.getJSONArray(tabKey);
                if (filterArray == null || filterArray.isEmpty()) {
                    continue;
                }

                // 遍历每个筛选器
                for (int i = 0; i < filterArray.size(); i++) {
                    JSONObject filterObject = filterArray.getJSONObject(i);
                    if (filterObject == null) {
                        continue;
                    }

                    // 获取数据集ID
                    Long datasetId = filterObject.getLong("datasetId");
                    if (datasetId == null) {
                        log.warn("筛选器配置中缺少datasetId: {}", filterObject);
                        continue;
                    }

                    // 获取数据集字段配置
                    List<DatasetColumnConfigDTO> datasetColumnList = getDatasetColumnConfig(columnConfigMap, datasetId);
                    if (CollUtil.isEmpty(datasetColumnList)) {
                        log.warn("数据集ID: {} 没有找到字段配置", datasetId);
                        continue;
                    }

                    // 处理fields字段
                    JSONObject fieldsObject = filterObject.getJSONObject("fields");
                    if (fieldsObject != null) {
                        processSubscribeFilterFields(fieldsObject, datasetColumnList, uuidMap, configUuidMap);
                    }
                }
            }

            return conditionJson.toString();
        } catch (Exception e) {
            log.error("处理订阅condition字段失败", e);
            throw e;
        }
    }

    /**
     * 处理订阅筛选器的fields字段
     */
    private void processSubscribeFilterFields(JSONObject fieldsObject,
                                              List<DatasetColumnConfigDTO> datasetColumnList,
                                              Map<String, String> uuidMap,
                                              Map<String, String> configUuidMap) throws Exception {
        try {
            // 获取字段信息
            String oldUuid = fieldsObject.getStr("uuid");
            Long id = fieldsObject.getLong("id");
            String enName = fieldsObject.getStr("enName");
            String name = fieldsObject.getStr("name");

            // 匹配新的UUID
            String datasetUuid = matchDatasetUuid(oldUuid, id, enName, name, datasetColumnList, uuidMap);

            // 查询旧的UUID
            for (Map.Entry<String, String> entry : uuidMap.entrySet()) {
                if (Objects.equals(entry.getValue(), datasetUuid)) {
                    oldUuid = entry.getKey();
                    break;
                }
            }

            // 更新UUID、configUuid
            fieldsObject.set("uuid", datasetUuid);

            if (!configUuidMap.containsKey(oldUuid)) {
                throw new Exception("configUuidMap中不存在新configUuid oldUuid: " + oldUuid + ", name: " + name + ", enName: " + enName + ", id: " + id);
            }

            fieldsObject.set("configUuid", configUuidMap.get(oldUuid));


            log.debug("订阅筛选器字段UUID替换: {} -> {}", oldUuid, datasetUuid);

        } catch (Exception e) {
            log.error("处理订阅筛选器fields字段失败: {}", fieldsObject.toString(), e);
            throw e;
        }
    }

    /**
     * 处理t_report_warn表
     */
    private void processReportWarnTable(String batchId, List<ReportWarnRuleDo> reportWarnRuleList,
                                        Map<Long, List<DatasetColumnConfigDTO>> columnConfigMap,
                                        Map<String, String> uuidMap,
                                        Map<String, String> configUuidMap) {
        log.info("开始处理t_report_warn表，数量: {}", reportWarnRuleList.size());

        List<DashboardCorrectionLogDO> correctionLogs = new ArrayList<>();

        for (ReportWarnRuleDo reportWarnRule : reportWarnRuleList) {
            try {
                boolean hasChanges = false;

                // 处理conditionDesc字段
                String conditionDesc = reportWarnRule.getConditionDesc();
                if (StringUtils.isNotBlank(conditionDesc)) {
                    FieldProcessResult conditionResult = processReportWarnConditionDescWithLogging(
                            conditionDesc, "condition_desc", uuidMap, configUuidMap);

                    if (conditionResult.hasChanges()) {
                        reportWarnRule.setConditionDesc(conditionResult.getProcessedValue());
                        hasChanges = true;
                    }

                    correctionLogs.add(buildCorrectionLog(batchId, DashboardCorrectionLogDO.TableName.REPORT_WARN_RULE,
                            "condition_desc", reportWarnRule.getId(), null, null,
                            conditionResult.getOriginalValue(), conditionResult.getProcessedValue(),
                            conditionResult.getStatus(), conditionResult.getErrorType(),
                            conditionResult.getErrorMessage(),
                            conditionResult.getException() != null ? getStackTrace(conditionResult.getException()) : null));
                }

                // 处理expectationDesc字段
                String expectationDesc = reportWarnRule.getExpectationDesc();
                if (StringUtils.isNotBlank(expectationDesc)) {
                    FieldProcessResult expectationResult = processReportWarnExpectationDescWithLogging(
                            expectationDesc, "expectation_desc", uuidMap, configUuidMap);

                    if (expectationResult.hasChanges()) {
                        reportWarnRule.setExpectationDesc(expectationResult.getProcessedValue());
                        hasChanges = true;
                    }

                    correctionLogs.add(buildCorrectionLog(batchId, DashboardCorrectionLogDO.TableName.REPORT_WARN_RULE,
                            "expectation_desc", reportWarnRule.getId(), null, null,
                            expectationResult.getOriginalValue(), expectationResult.getProcessedValue(),
                            expectationResult.getStatus(), expectationResult.getErrorType(),
                            expectationResult.getErrorMessage(),
                            expectationResult.getException() != null ? getStackTrace(expectationResult.getException()) : null));
                }

                // t_report_warn表不涉及修改，只做查询逻辑处理
                if (hasChanges) {
                    log.info("预警规则ID: {} 需要更新，但t_report_warn表不涉及修改", reportWarnRule.getId());
                }

            } catch (Exception e) {
                log.error("处理预警规则ID: {} 时发生整体错误", reportWarnRule.getId(), e);
                // 记录整体错误日志
                correctionLogs.add(buildCorrectionLog(batchId, DashboardCorrectionLogDO.TableName.REPORT_WARN_RULE,
                        "RECORD_LEVEL_ERROR", reportWarnRule.getId(), null, null,
                        null, null, DashboardCorrectionLogDO.Status.FAILURE,
                        DashboardCorrectionLogDO.ErrorType.DATA_PROCESSING_ERROR, e.getMessage(), getStackTrace(e)));
            }
        }

        // 批量记录订正日志
        if (!correctionLogs.isEmpty()) {
            try {
                dashboardCorrectionLogService.batchInsert(correctionLogs);
                log.info("记录了{}条t_report_warn表订正日志", correctionLogs.size());
            } catch (Exception e) {
                log.error("记录t_report_warn表订正日志失败", e);
            }
        }

        log.info("完成处理t_report_warn表");
    }

    /**
     * 处理预警规则的conditionDesc字段
     * conditionDesc字段结构: [{"uuid": "xxx", "reportField": "field", "name": "xxx", ...}]
     */
    private String processReportWarnConditionDesc(String conditionDesc, Map<String, String> uuidMap, Map<String, String> configUuidMap) throws Exception {
        try {
            JSONArray jsonArray = JSONUtil.parseArray(conditionDesc);
            boolean hasChanges = false;

            for (Object item : jsonArray) {
                if (item instanceof JSONObject) {
                    JSONObject jsonObject = (JSONObject) item;
                    String oldUuid = jsonObject.getStr("uuid");
                    String name = jsonObject.getStr("name");
                    String reportField = jsonObject.getStr("reportField");

                    if (StringUtils.isNotBlank(oldUuid) && uuidMap.containsKey(oldUuid)) {
                        String newUuid = uuidMap.get(oldUuid);

                        if (StringUtils.isNotBlank(newUuid)) {
                            jsonObject.set("uuid", newUuid);
                            hasChanges = true;
                        } else {
                            throw new Exception("uuidMap中不存在新UUID oldUuid: " + oldUuid + ", name: " + name + ", reportField: " + reportField);
                        }

                    }

                    if (StringUtils.isNotBlank(oldUuid) && configUuidMap.containsKey(oldUuid)) {
                        String newConfigUuid = configUuidMap.get(oldUuid);
                        if (StringUtils.isBlank(newConfigUuid)) {
                            throw new Exception("configUuidMap中不存在新configUuid oldUuid: " + oldUuid + ", name: " + name + ", reportField: " + reportField);
                        }
                        jsonObject.set("configUuid", newConfigUuid);
                        hasChanges = true;
                    }
                }
            }

            return hasChanges ? jsonArray.toString() : conditionDesc;
        } catch (Exception e) {
            log.error("处理预警条件conditionDesc字段失败", e);
            throw e;
        }
    }

    /**
     * 处理预警规则的expectationDesc字段
     * expectationDesc字段结构: {"uuid": "xxx", "reportField": "index", "name": "xxx", ...}
     */
    private String processReportWarnExpectationDesc(String expectationDesc, Map<String, String> uuidMap, Map<String, String> configUuidMap) {
        try {
            JSONObject jsonObject = JSONUtil.parseObj(expectationDesc);
            String oldUuid = jsonObject.getStr("uuid");

            if (StringUtils.isNotBlank(oldUuid) && uuidMap.containsKey(oldUuid)) {
                String newUuid = uuidMap.get(oldUuid);
                jsonObject.set("uuid", newUuid);
                log.debug("预警期望UUID替换: {} -> {}", oldUuid, newUuid);
                return jsonObject.toString();
            }

            if (StringUtils.isNotBlank(oldUuid) && configUuidMap.containsKey(oldUuid)) {
                String newConfigUuid = configUuidMap.get(oldUuid);
                jsonObject.set("configUuid", newConfigUuid);
                log.debug("预警期望configUuid替换: {} -> {}", oldUuid, newConfigUuid);
                return jsonObject.toString();
            }

            return expectationDesc;
        } catch (Exception e) {
            log.error("处理预警期望expectationDesc字段失败", e);
            throw e;
        }
    }

    /**
     * 构建订正日志对象
     */
    private DashboardCorrectionLogDO buildCorrectionLog(String batchId, String tableName, String fieldName,
                                                        Long recordId, Long dashboardId, Long datasetId,
                                                        String originalValue, String processedValue, String status,
                                                        String errorType, String errorMessage, String stackTrace) {
        return DashboardCorrectionLogDO.builder()
                .batchId(batchId)
                .tableName(tableName)
                .fieldName(fieldName)
                .recordId(recordId)
                .dashboardId(dashboardId)
                .datasetId(datasetId)
                .originalValue(originalValue)
                .processedValue(processedValue)
                .status(status)
                .errorType(errorType)
                .errorMessage(errorMessage)
                .stackTrace(stackTrace)
                .createdAt(new Date())
                .createdBy("system")
                .build();
    }

    /**
     * 获取异常堆栈跟踪信息
     */
    private String getStackTrace(Exception e) {
        if (e == null) {
            return null;
        }
        try {
            java.io.StringWriter sw = new java.io.StringWriter();
            java.io.PrintWriter pw = new java.io.PrintWriter(sw);
            e.printStackTrace(pw);
            String stackTrace = sw.toString();
            // 限制堆栈跟踪长度，避免过长
            return stackTrace.length() > 2000 ? stackTrace.substring(0, 2000) + "..." : stackTrace;
        } catch (Exception ex) {
            return "获取堆栈跟踪失败: " + ex.getMessage();
        }
    }

    /**
     * 字段处理结果类
     */
    @Data
    private static class FieldProcessResult {
        private final String originalValue;
        private final String processedValue;
        private final String status;
        private final String errorType;
        private final String errorMessage;
        private final Exception exception;

        public FieldProcessResult(String originalValue, String processedValue, String status) {
            this.originalValue = originalValue;
            this.processedValue = processedValue;
            this.status = status;
            this.errorType = null;
            this.errorMessage = null;
            this.exception = null;
        }

        public FieldProcessResult(String originalValue, String status, String errorType, String errorMessage, Exception exception) {
            this.originalValue = originalValue;
            this.processedValue = null;
            this.status = status;
            this.errorType = errorType;
            this.errorMessage = errorMessage;
            this.exception = exception;
        }

        public boolean isSuccess() {
            return DashboardCorrectionLogDO.Status.SUCCESS.equals(status);
        }

        public boolean hasChanges() {
            return isSuccess() && !originalValue.equals(processedValue);
        }
    }

    /**
     * 标准字段处理器函数式接口
     */
    @FunctionalInterface
    private interface StandardFieldProcessor {
        String process(String fieldValue, List<DatasetColumnConfigDTO> datasetColumnList,
                       Map<String, String> uuidMap, Map<String, String> configUuidMap) throws Exception;
    }

    /**
     * 订阅字段处理器函数式接口
     */
    @FunctionalInterface
    private interface SubscribeFieldProcessor {
        String process(String fieldValue, Map<Long, List<DatasetColumnConfigDTO>> columnConfigMap,
                       Map<String, String> uuidMap, Map<String, String> configUuidMap) throws Exception;
    }

    /**
     * 预警字段处理器函数式接口
     */
    @FunctionalInterface
    private interface WarnFieldProcessor {
        String process(String fieldValue, Map<String, String> uuidMap, Map<String, String> configUuidMap) throws Exception;
    }

    /**
     * 通用字段处理方法 - 标准字段处理器
     */
    private FieldProcessResult processFieldWithLogging(String fieldValue, String fieldName,
                                                       List<DatasetColumnConfigDTO> datasetColumnList,
                                                       Map<String, String> uuidMap, Map<String, String> configUuidMap,
                                                       StandardFieldProcessor processor) {
        if (StringUtils.isBlank(fieldValue)) {
            return new FieldProcessResult(fieldValue, fieldValue, DashboardCorrectionLogDO.Status.NO_CHANGE);
        }

        try {
            String processedValue = processor.process(fieldValue, datasetColumnList, uuidMap, configUuidMap);

            if (fieldValue.equals(processedValue)) {
                return new FieldProcessResult(fieldValue, processedValue, DashboardCorrectionLogDO.Status.NO_CHANGE);
            } else {
                return new FieldProcessResult(fieldValue, processedValue, DashboardCorrectionLogDO.Status.SUCCESS);
            }
        } catch (Exception e) {
            log.error("处理{}字段失败", fieldName, e);
            return new FieldProcessResult(fieldValue, DashboardCorrectionLogDO.Status.FAILURE,
                    DashboardCorrectionLogDO.ErrorType.DATA_PROCESSING_ERROR, e.getMessage(), e);
        }
    }

    /**
     * 通用字段处理方法 - 订阅字段处理器
     */
    private FieldProcessResult processFieldWithLogging(String fieldValue, String fieldName,
                                                       Map<Long, List<DatasetColumnConfigDTO>> columnConfigMap,
                                                       Map<String, String> uuidMap, Map<String, String> configUuidMap,
                                                       SubscribeFieldProcessor processor) {
        if (StringUtils.isBlank(fieldValue)) {
            return new FieldProcessResult(fieldValue, fieldValue, DashboardCorrectionLogDO.Status.NO_CHANGE);
        }

        try {
            String processedValue = processor.process(fieldValue, columnConfigMap, uuidMap, configUuidMap);

            if (fieldValue.equals(processedValue)) {
                return new FieldProcessResult(fieldValue, processedValue, DashboardCorrectionLogDO.Status.NO_CHANGE);
            } else {
                return new FieldProcessResult(fieldValue, processedValue, DashboardCorrectionLogDO.Status.SUCCESS);
            }
        } catch (Exception e) {
            log.error("处理{}字段失败", fieldName, e);
            return new FieldProcessResult(fieldValue, DashboardCorrectionLogDO.Status.FAILURE,
                    DashboardCorrectionLogDO.ErrorType.DATA_PROCESSING_ERROR, e.getMessage(), e);
        }
    }

    /**
     * 通用字段处理方法 - 预警字段处理器
     */
    private FieldProcessResult processFieldWithLogging(String fieldValue, String fieldName,
                                                       Map<String, String> uuidMap, Map<String, String> configUuidMap,
                                                       WarnFieldProcessor processor) {
        if (StringUtils.isBlank(fieldValue)) {
            return new FieldProcessResult(fieldValue, fieldValue, DashboardCorrectionLogDO.Status.NO_CHANGE);
        }

        try {
            String processedValue = processor.process(fieldValue, uuidMap, configUuidMap);

            if (fieldValue.equals(processedValue)) {
                return new FieldProcessResult(fieldValue, processedValue, DashboardCorrectionLogDO.Status.NO_CHANGE);
            } else {
                return new FieldProcessResult(fieldValue, processedValue, DashboardCorrectionLogDO.Status.SUCCESS);
            }
        } catch (Exception e) {
            log.error("处理{}字段失败", fieldName, e);
            return new FieldProcessResult(fieldValue, DashboardCorrectionLogDO.Status.FAILURE,
                    DashboardCorrectionLogDO.ErrorType.DATA_PROCESSING_ERROR, e.getMessage(), e);
        }
    }


    /**
     * 处理report_structure字段的专门方法
     */
    private FieldProcessResult processReportStructureWithLogging(String fieldValue,
                                                                 String fieldName,
                                                                 List<DatasetColumnConfigDTO> datasetColumnList,
                                                                 Map<String, String> uuidMap,
                                                                 Map<String, String> configUuidMap) {
        return processFieldWithLogging(fieldValue, fieldName, datasetColumnList, uuidMap, configUuidMap,
                this::processReportStructure);
    }

    /**
     * 处理show_column字段的专门方法
     */
    private FieldProcessResult processShowColumnWithLogging(String fieldValue, String fieldName,
                                                            List<DatasetColumnConfigDTO> datasetColumnList,
                                                            Map<String, String> uuidMap,
                                                            Map<String, String> configUuidMap) {
        return processFieldWithLogging(fieldValue, fieldName, datasetColumnList, uuidMap, configUuidMap,
                this::processShowColumn);
    }

    /**
     * 处理index_column字段的专门方法
     */
    private FieldProcessResult processIndexColumnWithLogging(String fieldValue, String fieldName,
                                                             List<DatasetColumnConfigDTO> datasetColumnList,
                                                             Map<String, String> uuidMap,
                                                             Map<String, String> configUuidMap) {
        return processFieldWithLogging(fieldValue, fieldName, datasetColumnList, uuidMap, configUuidMap,
                this::processIndexColumn);
    }

    /**
     * 处理filter_column字段的专门方法
     */
    private FieldProcessResult processFilterColumnWithLogging(String fieldValue, String fieldName,
                                                              List<DatasetColumnConfigDTO> datasetColumnList,
                                                              Map<String, String> uuidMap,
                                                              Map<String, String> configUuidMap) {
        return processFieldWithLogging(fieldValue, fieldName, datasetColumnList, uuidMap, configUuidMap,
                this::processFilterColumn);
    }

    /**
     * 处理condition字段的专门方法
     */
    private FieldProcessResult processConControlWithLogging(String fieldValue, String fieldName,
                                                            List<DatasetColumnConfigDTO> datasetColumnList,
                                                            Map<String, String> uuidMap,
                                                            Map<String, String> configUuidMap) {
        return processFieldWithLogging(fieldValue, fieldName, datasetColumnList, uuidMap, configUuidMap,
                this::processConControl);
    }

    /**
     * 处理keyword字段的专门方法
     */
    private FieldProcessResult processKeywordWithLogging(String fieldValue, String fieldName,
                                                         List<DatasetColumnConfigDTO> datasetColumnList,
                                                         Map<String, String> uuidMap,
                                                         Map<String, String> configUuidMap) {
        return processFieldWithLogging(fieldValue, fieldName, datasetColumnList, uuidMap, configUuidMap,
                this::processKeyword);
    }

    /**
     * 处理order_column字段的专门方法
     */
    private FieldProcessResult processOrderColumnWithLogging(String fieldValue, String fieldName,
                                                             List<DatasetColumnConfigDTO> datasetColumnList,
                                                             Map<String, String> uuidMap,
                                                             Map<String, String> configUuidMap) {
        return processFieldWithLogging(fieldValue, fieldName, datasetColumnList, uuidMap, configUuidMap,
                this::processOrderColumn);
    }

    /**
     * 处理compute_column字段的专门方法
     */
    private FieldProcessResult processComputeColumnWithLogging(String fieldValue, String fieldName,
                                                               List<DatasetColumnConfigDTO> datasetColumnList,
                                                               Map<String, String> uuidMap,
                                                               Map<String, String> configUuidMap) {
        return processFieldWithLogging(fieldValue, fieldName, datasetColumnList, uuidMap, configUuidMap,
                this::processComputeColumn);
    }

    /**
     * 处理sensitive_fields字段的专门方法
     */
    private FieldProcessResult processSensitiveFieldsWithLogging(String fieldValue, String fieldName,
                                                                 List<DatasetColumnConfigDTO> datasetColumnList,
                                                                 Map<String, String> uuidMap,
                                                                 Map<String, String> configUuidMap) {
        return processFieldWithLogging(fieldValue, fieldName, datasetColumnList, uuidMap, configUuidMap,
                this::processSensitiveFields);
    }

    /**
     * 处理table_configuration字段的专门方法
     */
    private FieldProcessResult processTableConfigurationWithLogging(String fieldValue, String fieldName,
                                                                    List<DatasetColumnConfigDTO> datasetColumnList,
                                                                    Map<String, String> uuidMap,
                                                                    Map<String, String> configUuidMap) {
        return processFieldWithLogging(fieldValue, fieldName, datasetColumnList, uuidMap, configUuidMap,
                this::processTableConfiguration);
    }

    /**
     * 处理filter card fields字段的专门方法
     */
    private FieldProcessResult processFilterCardFieldsWithLogging(String fieldValue, String fieldName,
                                                                  List<DatasetColumnConfigDTO> datasetColumnList,
                                                                  Map<String, String> uuidMap,
                                                                  Map<String, String> configUuidMap) {
        return processFieldWithLogging(fieldValue, fieldName, datasetColumnList, uuidMap, configUuidMap,
                this::processFilterCardFields);
    }

    /**
     * 处理标准JSON字段的专门方法
     */
    private FieldProcessResult processStandardJsonFieldWithLogging(String fieldValue, String fieldName,
                                                                   List<DatasetColumnConfigDTO> datasetColumnList,
                                                                   Map<String, String> uuidMap,
                                                                   Map<String, String> configUuidMap) {
        return processFieldWithLogging(fieldValue, fieldName, datasetColumnList, uuidMap, configUuidMap,
                (value, columnList, uMap, cMap) -> processStandardJsonField(value, columnList, fieldName, uMap, cMap));
    }

    /**
     * 处理对象订阅condition字段的专门方法
     */
    private FieldProcessResult processObjectSubscribeConditionWithLogging(String fieldValue, String fieldName,
                                                                          Map<Long, List<DatasetColumnConfigDTO>> columnConfigMap,
                                                                          Map<String, String> uuidMap,
                                                                          Map<String, String> configUuidMap) {
        return processFieldWithLogging(fieldValue, fieldName, columnConfigMap, uuidMap, configUuidMap,
                this::processObjectSubscribeCondition);
    }

    /**
     * 处理预警条件描述字段的专门方法
     */
    private FieldProcessResult processReportWarnConditionDescWithLogging(String fieldValue, String fieldName, Map<String, String> uuidMap, Map<String, String> configUuidMap) {
        return processFieldWithLogging(fieldValue, fieldName, uuidMap, configUuidMap,
                this::processReportWarnConditionDesc);
    }

    /**
     * 处理预警期望描述字段的专门方法
     */
    private FieldProcessResult processReportWarnExpectationDescWithLogging(String fieldValue, String fieldName, Map<String, String> uuidMap, Map<String, String> configUuidMap) {
        return processFieldWithLogging(fieldValue, fieldName, uuidMap, configUuidMap,
                this::processReportWarnExpectationDesc);
    }
}
