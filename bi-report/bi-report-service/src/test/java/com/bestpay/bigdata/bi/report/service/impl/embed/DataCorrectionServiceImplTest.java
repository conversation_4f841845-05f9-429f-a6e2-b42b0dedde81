package com.bestpay.bigdata.bi.report.service.impl.embed;

import com.bestpay.bigdata.bi.common.bean.UserInfoRequest;
import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.common.entity.Org;
import com.bestpay.bigdata.bi.database.api.common.QueryStatService;
import com.bestpay.bigdata.bi.database.bean.QueryStat;
import com.bestpay.bigdata.bi.database.bean.QueryStatDTO;
import com.bestpay.bigdata.bi.report.request.common.QueryStatRequest;
import com.bestpay.bigdata.bi.report.service.impl.common.DataCorrectionServiceImpl;
import com.bestpay.bigdata.bi.thirdparty.api.AiPlusUserService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DataCorrectionServiceImplTest {

    @Mock
    private QueryStatService queryStatService;

    @Mock
    private AiPlusUserService aiPlusUserService;

    @InjectMocks
    private DataCorrectionServiceImpl dataCorrectionService;

    private QueryStatRequest queryStatRequest;

    @BeforeEach
    void setUp() {
        queryStatRequest = new QueryStatRequest();
        queryStatRequest.setTypeCode(1);
        queryStatRequest.setCreatedStartAt("2023-01-01");
        queryStatRequest.setCreatedEndAt("2023-01-31");
    }

    @Test
    void correctQueryStatOrg_WhenUserListIsEmpty_ShouldReturnZero() {
        // 准备测试数据
        List<QueryStat> queryStats = new ArrayList<>();
        queryStats.add(new QueryStat());
        
        // 设置mock行为
        when(queryStatService.query(any(QueryStatDTO.class))).thenReturn(queryStats);
        when(aiPlusUserService.getUserList(any(UserInfoRequest.class))).thenReturn(new ArrayList<>());
        
        // 执行测试
        int result = dataCorrectionService.correctQueryStatOrg(queryStatRequest);
        
        // 验证结果
        assertEquals(0, result);
        verify(queryStatService, never()).updateOrgById(any(), any());
    }

    @Test
    void correctQueryStatOrg_WhenQueryStatListIsEmpty_ShouldReturnZero() {
        // 设置mock行为
        when(queryStatService.query(any(QueryStatDTO.class))).thenReturn(new ArrayList<>());

        // 执行测试
        int result = dataCorrectionService.correctQueryStatOrg(queryStatRequest);
        // 验证结果
        assertEquals(0, result);
        verify(queryStatService, never()).updateOrgById(any(), any());
    }

    @Test
    void correctQueryStatOrg_WhenNormalCase_ShouldReturnCorrectUpdateCount() {
        // 准备测试数据
        List<QueryStat> queryStats = new ArrayList<>();
        QueryStat stat1 = new QueryStat();
        stat1.setId(1L);
        stat1.setUsername("<EMAIL>");
        QueryStat stat2 = new QueryStat();
        stat2.setId(2L);
        stat2.setUsername("<EMAIL>");
        queryStats.add(stat1);
        queryStats.add(stat2);

        List<UserInfo> userInfos = new ArrayList<>();
        UserInfo user1 = new UserInfo();
        user1.setEmail("<EMAIL>");
        Org org1 = new Org();
        org1.setName("Org1");
        user1.setOrg(org1);
        
        UserInfo user2 = new UserInfo();
        user2.setEmail("<EMAIL>");
        Org org2 = new Org();
        org2.setName("Org2");
        user2.setOrg(org2);
        userInfos.add(user1);
        userInfos.add(user2);

        // 设置mock行为
        when(queryStatService.query(any(QueryStatDTO.class))).thenReturn(queryStats);
        when(aiPlusUserService.getUserList(any(UserInfoRequest.class))).thenReturn(userInfos);
        
        // 执行测试
        int result = dataCorrectionService.correctQueryStatOrg(queryStatRequest);
        
        // 验证结果
        assertEquals(2, result);
        verify(queryStatService, times(1)).updateOrgById(1L, "Org1");
        verify(queryStatService, times(1)).updateOrgById(2L, "Org2");
    }

    @Test
    void correctQueryStatOrg_WhenPartialMatch_ShouldReturnCorrectUpdateCount() {
        // 准备测试数据
        List<QueryStat> queryStats = new ArrayList<>();
        QueryStat stat1 = new QueryStat();
        stat1.setId(1L);
        stat1.setUsername("<EMAIL>");
        QueryStat stat2 = new QueryStat();
        stat2.setId(2L);
        stat2.setUsername("<EMAIL>"); // 不在用户列表中
        queryStats.add(stat1);
        queryStats.add(stat2);

        List<UserInfo> userInfos = new ArrayList<>();
        UserInfo user1 = new UserInfo();
        user1.setEmail("<EMAIL>");
        Org org1 = new Org();
        org1.setName("Org1");
        user1.setOrg(org1);
        userInfos.add(user1);

        // 设置mock行为
        when(queryStatService.query(any(QueryStatDTO.class))).thenReturn(queryStats);
        when(aiPlusUserService.getUserList(any(UserInfoRequest.class))).thenReturn(userInfos);
        
        // 执行测试
        int result = dataCorrectionService.correctQueryStatOrg(queryStatRequest);
        
        // 验证结果
        assertEquals(1, result);
    }
}